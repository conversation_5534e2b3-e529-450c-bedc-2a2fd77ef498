import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/post_model.dart';
import '../../services/social_feed_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/post_widget.dart';

/// شاشة إنشاء إعادة نشر مع تعليق مثل Facebook
class CreateRepostScreen extends StatefulWidget {
  final PostModel originalPost;

  const CreateRepostScreen({
    super.key,
    required this.originalPost,
  });

  @override
  State<CreateRepostScreen> createState() => _CreateRepostScreenState();
}

class _CreateRepostScreenState extends State<CreateRepostScreen> {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  bool _isPosting = false;

  @override
  void initState() {
    super.initState();
    // التركيز على حقل النص عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _commentFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _commentController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Color(0xFF1C1E21)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Repost',
          style: TextStyle(
            color: Color(0xFF1C1E21),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          // زر النشر
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ElevatedButton(
              onPressed: _isPosting ? null : _createRepost,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1877F2),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              ),
              child: _isPosting
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Post',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة كتابة التعليق
          _buildCommentSection(),
          
          // المنشور الأصلي
          Expanded(
            child: _buildOriginalPost(),
          ),
        ],
      ),
    );
  }

  /// بناء قسم كتابة التعليق
  Widget _buildCommentSection() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final currentUser = authService.currentUser;
        
        return Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المستخدم
              CircleAvatar(
                radius: 20,
                backgroundColor: const Color(0xFF1877F2),
                child: Text(
                  currentUser?.name.isNotEmpty == true 
                      ? currentUser!.name[0].toUpperCase() 
                      : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // حقل النص
              Expanded(
                child: TextField(
                  controller: _commentController,
                  focusNode: _commentFocusNode,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: 'Add a comment...',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 16,
                    ),
                    border: InputBorder.none,
                  ),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء المنشور الأصلي
  Widget _buildOriginalPost() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE4E6EA),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // عنوان "المنشور الأصلي"
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Color(0xFFF0F2F5),
              borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.repeat,
                  color: Colors.grey[600],
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Original post by ${widget.originalPost.authorName}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          // المنشور الأصلي (مصغر)
          Expanded(
            child: _buildMiniPost(),
          ),
        ],
      ),
    );
  }

  /// بناء المنشور المصغر
  Widget _buildMiniPost() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المؤلف
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF1877F2),
                child: Text(
                  widget.originalPost.authorName.isNotEmpty 
                      ? widget.originalPost.authorName[0].toUpperCase() 
                      : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.originalPost.authorName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1C1E21),
                      ),
                    ),
                    Text(
                      _formatTime(widget.originalPost.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // محتوى المنشور
          if (widget.originalPost.content.isNotEmpty) ...[
            Text(
              widget.originalPost.content,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF1C1E21),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
          ],
          
          // الصور (إن وجدت)
          if (widget.originalPost.images.isNotEmpty) ...[
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: const Center(
                child: Icon(
                  Icons.image,
                  color: Colors.grey,
                  size: 32,
                ),
              ),
            ),
          ],
          
          // الفيديوهات (إن وجدت)
          if (widget.originalPost.videos.isNotEmpty) ...[
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: const Center(
                child: Icon(
                  Icons.play_circle_outline,
                  color: Colors.grey,
                  size: 32,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }

  /// إنشاء إعادة النشر
  void _createRepost() async {
    if (_isPosting) return;
    
    final comment = _commentController.text.trim();
    
    setState(() {
      _isPosting = true;
    });

    try {
      final socialService = Provider.of<SocialFeedService>(context, listen: false);
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;

      if (currentUser != null) {
        final success = await socialService.createRepost(
          originalPost: widget.originalPost,
          reposterUserId: currentUser.phoneNumber,
          reposterName: currentUser.name,
          comment: comment.isNotEmpty ? comment : null,
        );

        if (mounted) {
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('✅ Post reposted successfully!'),
                backgroundColor: const Color(0xFF42B883),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to repost'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPosting = false;
        });
      }
    }
  }
}

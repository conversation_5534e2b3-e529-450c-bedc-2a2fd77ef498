import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// تحسينات واجهة المستخدم الاحترافية مثل Facebook/TikTok/LinkedIn
class PremiumUIEnhancements {

  /// تأثير الضغط الاحترافي مثل TikTok
  static Widget premiumTapEffect({
    required Widget child,
    required VoidCallback onTap,
    double scaleDown = 0.95,
    Duration duration = const Duration(milliseconds: 100),
    Color? rippleColor,
    bool enableHaptic = true,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: 1.0, end: 1.0),
      builder: (context, scale, child) {
        return GestureDetector(
          onTapDown: (_) {
            if (enableHaptic) {
              HapticFeedback.lightImpact();
            }
          },
          onTap: onTap,
          child: AnimatedScale(
            scale: scale,
            duration: duration,
            curve: Curves.easeOutBack,
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// زر احترافي مثل Facebook
  static Widget facebookStyleButton({
    required String text,
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    bool isLoading = false,
    bool isDisabled = false,
    double borderRadius = 8.0,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
  }) {
    return premiumTapEffect(
      onTap: isDisabled || isLoading ? () {} : onPressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: padding,
        decoration: BoxDecoration(
          color: isDisabled
              ? Colors.grey[300]
              : backgroundColor ?? const Color(0xFF1877F2),
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: isDisabled ? null : [
            BoxShadow(
              color: (backgroundColor ?? const Color(0xFF1877F2)).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading) ...[
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    textColor ?? Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
            ] else if (icon != null) ...[
              Icon(
                icon,
                color: textColor ?? Colors.white,
                size: 18,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: TextStyle(
                color: isDisabled
                    ? Colors.grey[600]
                    : textColor ?? Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تأثير الموجة عند الضغط مثل Material Design
  static Widget rippleEffect({
    required Widget child,
    required VoidCallback onTap,
    Color? rippleColor,
    double borderRadius = 8.0,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          onTap();
        },
        borderRadius: BorderRadius.circular(borderRadius),
        splashColor: rippleColor ?? Colors.blue.withValues(alpha: 0.2),
        highlightColor: rippleColor ?? Colors.blue.withValues(alpha: 0.1),
        child: child,
      ),
    );
  }

  /// تأثير التحويم المتقدم
  static Widget hoverEffect({
    required Widget child,
    double hoverScale = 1.05,
    Color? hoverColor,
    Duration duration = const Duration(milliseconds: 200),
  }) {
    return MouseRegion(
      onEnter: (_) => HapticFeedback.selectionClick(),
      child: AnimatedContainer(
        duration: duration,
        curve: Curves.easeOutBack,
        child: child,
      ),
    );
  }

  /// تأثير الانزلاق السلس
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    Offset beginOffset = const Offset(0, 1),
    Offset endOffset = Offset.zero,
  }) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: beginOffset,
        end: endOffset,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeOutBack,
      )),
      child: child,
    );
  }

  /// تأثير التلاشي المتقدم
  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
    double beginOpacity = 0.0,
    double endOpacity = 1.0,
  }) {
    return FadeTransition(
      opacity: Tween<double>(
        begin: beginOpacity,
        end: endOpacity,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      )),
      child: child,
    );
  }

  /// تأثير الدوران المتقدم
  static Widget rotationTransition({
    required Widget child,
    required Animation<double> animation,
    double beginRotation = 0.0,
    double endRotation = 1.0,
  }) {
    return RotationTransition(
      turns: Tween<double>(
        begin: beginRotation,
        end: endRotation,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.elasticOut,
      )),
      child: child,
    );
  }

  /// تأثير النبض المستمر
  static Widget pulseEffect({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1000),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: minScale, end: maxScale),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      onEnd: () {
        // إعادة تشغيل الحركة
      },
      child: child,
    );
  }

  /// تأثير الاهتزاز
  static Widget shakeEffect({
    required Widget child,
    required Animation<double> animation,
    double shakeIntensity = 5.0,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final shake = math.sin(animation.value * 2 * math.pi * 3) * shakeIntensity;
        return Transform.translate(
          offset: Offset(shake, 0),
          child: child,
        );
      },
      child: child,
    );
  }

  /// تأثير الارتداد
  static Widget bounceEffect({
    required Widget child,
    required Animation<double> animation,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.elasticOut,
      )),
      child: child,
    );
  }

  /// تأثير التمدد
  static Widget stretchEffect({
    required Widget child,
    required Animation<double> animation,
    Axis direction = Axis.horizontal,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final scale = 1.0 + (animation.value * 0.1);
        return Transform.scale(
          scaleX: direction == Axis.horizontal ? scale : 1.0,
          scaleY: direction == Axis.vertical ? scale : 1.0,
          child: child,
        );
      },
      child: child,
    );
  }

  /// تأثير الجسيمات المتفجرة
  static Widget particleExplosion({
    required Widget child,
    required Animation<double> animation,
    Color particleColor = Colors.blue,
    int particleCount = 12,
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        child,
        ...List.generate(particleCount, (index) {
          final angle = (index / particleCount) * 2 * math.pi;
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              final distance = animation.value * 50;
              final x = math.cos(angle) * distance;
              final y = math.sin(angle) * distance;
              final opacity = 1.0 - animation.value;

              return Transform.translate(
                offset: Offset(x, y),
                child: Opacity(
                  opacity: opacity,
                  child: Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: particleColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ],
    );
  }

  /// ألوان Facebook الاحترافية
  static const Color facebookBlue = Color(0xFF1877F2);
  static const Color facebookGreen = Color(0xFF42B883);
  static const Color facebookRed = Color(0xFFE91E63);
  static const Color facebookOrange = Color(0xFFFF6B35);
  static const Color facebookPurple = Color(0xFF9C27B0);
  static const Color facebookGray = Color(0xFF65676B);

  /// منحنيات الحركة الاحترافية
  static const Curve elasticCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.easeOutBack;
  static const Curve quickCurve = Curves.easeOut;
  static const Curve bounceCurve = Curves.bounceOut;
}

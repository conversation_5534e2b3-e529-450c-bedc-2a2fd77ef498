import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/auth_service.dart';
import '../../services/chat_service.dart';
import '../../models/chat_model.dart';
import '../../models/user_model.dart';
import '../chat/chat_screen.dart';
import '../users/users_list_screen.dart';

class ChatsTab extends StatefulWidget {
  const ChatsTab({super.key});

  @override
  State<ChatsTab> createState() => _ChatsTabState();
}

class _ChatsTabState extends State<ChatsTab> {
  bool _showArchived = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer2<AuthService, ChatService>(
        builder: (context, authService, chatService, child) {
          final currentUser = authService.currentUser;
          if (currentUser == null) {
            return const Center(child: Text('User not found'));
          }

          return StreamBuilder<List<ChatModel>>(
            stream: chatService.getChats(currentUser.phoneNumber),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text('Error: ${snapshot.error}'),
                );
              }

              final allChats = snapshot.data ?? [];
              final chats = _showArchived 
                  ? allChats.where((chat) => chat.isArchived).toList()
                  : allChats.where((chat) => !chat.isArchived).toList();

              if (chats.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _showArchived ? Icons.archive : Icons.chat_bubble_outline,
                        size: 80,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _showArchived ? 'No archived chats' : 'No chats yet',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                      if (!_showArchived) ...[
                        const SizedBox(height: 8),
                        const Text(
                          'Start a new chat by tapping the add button',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  // Archive toggle
                  if (allChats.any((chat) => chat.isArchived))
                    Container(
                      color: Colors.grey[100],
                      child: ListTile(
                        leading: const Icon(Icons.archive),
                        title: Text(_showArchived ? 'Show Regular Chats' : 'Show Archived Chats'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          setState(() {
                            _showArchived = !_showArchived;
                          });
                        },
                      ),
                    ),
                  
                  // Chats list
                  Expanded(
                    child: ListView.builder(
                      itemCount: chats.length,
                      itemBuilder: (context, index) {
                        final chat = chats[index];
                        return _buildChatTile(chat, currentUser);
                      },
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const UsersListScreen(),
            ),
          );
        },
        backgroundColor: const Color(0xFFD32F2F),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildChatTile(ChatModel chat, UserModel currentUser) {
    final otherUserId = chat.getOtherUserId(currentUser.phoneNumber);

    return StreamBuilder<List<UserModel>>(
      stream: Provider.of<ChatService>(context, listen: false)
          .getAllUsers(currentUser.phoneNumber),
      builder: (context, userSnapshot) {
        UserModel? otherUser;
        if (userSnapshot.hasData) {
          try {
            otherUser = userSnapshot.data!
                .firstWhere((user) => user.phoneNumber == otherUserId);
          } catch (e) {
            // User not found
          }
        }

        return Dismissible(
          key: Key(chat.id),
          background: Container(
            color: Colors.green,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 20),
            child: const Icon(Icons.archive, color: Colors.white),
          ),
          secondaryBackground: Container(
            color: Colors.red,
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.only(right: 20),
            child: const Icon(Icons.delete, color: Colors.white),
          ),
          onDismissed: (direction) {
            if (direction == DismissDirection.startToEnd) {
              // Archive chat
              Provider.of<ChatService>(context, listen: false)
                  .toggleChatArchive(chat.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(chat.isArchived ? 'Chat unarchived' : 'Chat archived'),
                ),
              );
            } else {
              // Delete chat
              Provider.of<ChatService>(context, listen: false)
                  .deleteChat(chat.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Chat deleted')),
              );
            }
          },
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: const Color(0xFFD32F2F),
              backgroundImage: otherUser?.profileImageUrl != null
                  ? NetworkImage(otherUser!.profileImageUrl!)
                  : null,
              child: otherUser?.profileImageUrl == null
                  ? Text(
                      otherUser?.name.isNotEmpty == true
                          ? otherUser!.name[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    otherUser?.name ?? otherUserId,
                    style: TextStyle(
                      fontWeight: chat.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
                if (chat.isMuted)
                  const Icon(Icons.volume_off, size: 16, color: Colors.grey),
                if (chat.isArchived)
                  const Icon(Icons.archive, size: 16, color: Colors.grey),
              ],
            ),
            subtitle: Text(
              chat.lastMessage?.message ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: chat.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  _formatTime(chat.lastMessageTime),
                  style: TextStyle(
                    color: chat.unreadCount > 0 ? const Color(0xFFD32F2F) : Colors.grey[500],
                    fontSize: 12,
                    fontWeight: chat.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (chat.unreadCount > 0)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFD32F2F),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      chat.unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            onTap: () {
              if (otherUser != null) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ChatScreen(otherUser: otherUser!),
                  ),
                );
              }
            },
            onLongPress: () {
              _showChatOptions(context, chat);
            },
          ),
        );
      },
    );
  }

  void _showChatOptions(BuildContext context, ChatModel chat) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(chat.isArchived ? Icons.unarchive : Icons.archive),
              title: Text(chat.isArchived ? 'Unarchive' : 'Archive'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<ChatService>(context, listen: false)
                    .toggleChatArchive(chat.id);
              },
            ),
            ListTile(
              leading: Icon(chat.isMuted ? Icons.volume_up : Icons.volume_off),
              title: Text(chat.isMuted ? 'Unmute' : 'Mute'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<ChatService>(context, listen: false)
                    .toggleChatMute(chat.id);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Delete Chat'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<ChatService>(context, listen: false)
                    .deleteChat(chat.id);
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear),
              title: const Text('Clear Messages'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<ChatService>(context, listen: false)
                    .clearChatMessages(chat.id);
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE').format(dateTime);
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }
}

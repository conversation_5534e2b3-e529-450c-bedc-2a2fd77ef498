import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:vibration/vibration.dart';

enum SoundType {
  messageSent,
  messageReceived,
  messageDeleted,
  messageEdited,
  notification,
  callIncoming,
  callOutgoing,
  callConnected,
  callEnded,
  typing,
  cameraShutter,
  recordingStart,
  recordingStop,
  voiceMessageSent,
  groupNotification,
  error,
  success,
  warning,
}

class SoundService {
  static final SoundService _instance = SoundService._internal();
  factory SoundService() => _instance;
  SoundService._internal();

  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;

  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }

  void setVibrationEnabled(bool enabled) {
    _vibrationEnabled = enabled;
  }

  // Play sound based on type
  Future<void> playSound(SoundType soundType) async {
    if (!_soundEnabled) return;

    try {
      switch (soundType) {
        case SoundType.messageSent:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.messageReceived:
          await _playSystemSound(SystemSoundType.alert);
          break;
        case SoundType.messageDeleted:
          await _playSystemSound(SystemSoundType.click);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.short);
          }
          break;
        case SoundType.messageEdited:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.notification:
          await _playSystemSound(SystemSoundType.alert);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.medium);
          }
          break;
        case SoundType.callIncoming:
          await _playSystemSound(SystemSoundType.alert);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.long);
          }
          break;
        case SoundType.callOutgoing:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.callConnected:
          await _playSystemSound(SystemSoundType.click);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.short);
          }
          break;
        case SoundType.callEnded:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.typing:
          // No sound for typing, just visual indicator
          break;
        case SoundType.cameraShutter:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.recordingStart:
          await _playSystemSound(SystemSoundType.click);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.short);
          }
          break;
        case SoundType.recordingStop:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.voiceMessageSent:
          await _playSystemSound(SystemSoundType.click);
          break;
        case SoundType.groupNotification:
          await _playSystemSound(SystemSoundType.alert);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.medium);
          }
          break;
        case SoundType.error:
          await _playSystemSound(SystemSoundType.alert);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.error);
          }
          break;
        case SoundType.success:
          await _playSystemSound(SystemSoundType.click);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.success);
          }
          break;
        case SoundType.warning:
          await _playSystemSound(SystemSoundType.alert);
          if (_vibrationEnabled) {
            await _vibrate(VibrationPattern.warning);
          }
          break;
      }
    } catch (e) {
      debugPrint('Error playing sound: $e');
    }
  }

  Future<void> _playSystemSound(SystemSoundType sound) async {
    try {
      await SystemSound.play(sound);
      debugPrint('Playing sound: $sound');
    } catch (e) {
      debugPrint('Error playing system sound: $e');
    }
  }

  Future<void> _vibrate(VibrationPattern pattern) async {
    if (!_vibrationEnabled) return;

    try {
      // التحقق من دعم الاهتزاز
      bool? hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator == true) {
        switch (pattern) {
          case VibrationPattern.short:
            await Vibration.vibrate(duration: 100);
            break;
          case VibrationPattern.medium:
            await Vibration.vibrate(duration: 300);
            break;
          case VibrationPattern.long:
            await Vibration.vibrate(duration: 500);
            break;
          case VibrationPattern.success:
            await Vibration.vibrate(pattern: [0, 100, 100, 100]);
            break;
          case VibrationPattern.error:
            await Vibration.vibrate(pattern: [0, 100, 50, 100, 50, 100]);
            break;
          case VibrationPattern.warning:
            await Vibration.vibrate(pattern: [0, 200, 100, 200]);
            break;
          case VibrationPattern.typing:
            await Vibration.vibrate(duration: 50);
            break;
        }
      } else {
        // استخدام HapticFeedback كبديل
        switch (pattern) {
          case VibrationPattern.short:
            await HapticFeedback.lightImpact();
            break;
          case VibrationPattern.medium:
            await HapticFeedback.mediumImpact();
            break;
          case VibrationPattern.long:
            await HapticFeedback.heavyImpact();
            break;
          case VibrationPattern.success:
            await HapticFeedback.lightImpact();
            await Future.delayed(const Duration(milliseconds: 100));
            await HapticFeedback.lightImpact();
            break;
          case VibrationPattern.error:
            await HapticFeedback.heavyImpact();
            await Future.delayed(const Duration(milliseconds: 100));
            await HapticFeedback.heavyImpact();
            await Future.delayed(const Duration(milliseconds: 100));
            await HapticFeedback.heavyImpact();
            break;
          case VibrationPattern.warning:
            await HapticFeedback.mediumImpact();
            await Future.delayed(const Duration(milliseconds: 150));
            await HapticFeedback.mediumImpact();
            break;
          case VibrationPattern.typing:
            await HapticFeedback.selectionClick();
            break;
        }
      }
    } catch (e) {
      debugPrint('Error vibrating: $e');
    }
  }

  // Play notification sound with custom vibration
  Future<void> playNotificationSound({
    bool withVibration = true,
    VibrationPattern vibrationPattern = VibrationPattern.medium,
  }) async {
    await playSound(SoundType.notification);
    if (withVibration && _vibrationEnabled) {
      await _vibrate(vibrationPattern);
    }
  }

  // Play message sound
  Future<void> playMessageSound({required bool isOutgoing}) async {
    if (isOutgoing) {
      await playSound(SoundType.messageSent);
    } else {
      await playSound(SoundType.messageReceived);
    }
  }

  // Play call sound
  Future<void> playCallSound({required CallSoundType type}) async {
    switch (type) {
      case CallSoundType.incoming:
        await playSound(SoundType.callIncoming);
        break;
      case CallSoundType.outgoing:
        await playSound(SoundType.callOutgoing);
        break;
      case CallSoundType.connected:
        await playSound(SoundType.callConnected);
        break;
      case CallSoundType.ended:
        await playSound(SoundType.callEnded);
        break;
    }
  }

  // Play typing sound
  Future<void> playTypingSound() async {
    if (_vibrationEnabled) {
      await _vibrate(VibrationPattern.typing);
    }
  }

  // Play recording sound
  Future<void> playRecordingSound({required bool isStart}) async {
    if (isStart) {
      await playSound(SoundType.recordingStart);
    } else {
      await playSound(SoundType.recordingStop);
    }
  }

  // Play camera sound
  Future<void> playCameraSound() async {
    await playSound(SoundType.cameraShutter);
  }

  // Play feedback sounds
  Future<void> playSuccessSound() async {
    await playSound(SoundType.success);
  }

  Future<void> playErrorSound() async {
    await playSound(SoundType.error);
  }

  Future<void> playWarningSound() async {
    await playSound(SoundType.warning);
  }

  // Play group notification
  Future<void> playGroupNotificationSound() async {
    await playSound(SoundType.groupNotification);
  }

  // Play message action sounds
  Future<void> playMessageDeletedSound() async {
    await playSound(SoundType.messageDeleted);
  }

  Future<void> playMessageEditedSound() async {
    await playSound(SoundType.messageEdited);
  }

  Future<void> playVoiceMessageSentSound() async {
    await playSound(SoundType.voiceMessageSent);
  }
}

enum VibrationPattern {
  short,
  medium,
  long,
  success,
  error,
  warning,
  typing,
}

enum CallSoundType {
  incoming,
  outgoing,
  connected,
  ended,
}

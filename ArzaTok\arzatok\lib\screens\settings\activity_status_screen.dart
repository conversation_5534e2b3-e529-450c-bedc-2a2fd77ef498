import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// شاشة إعدادات حالة النشاط
class ActivityStatusScreen extends StatefulWidget {
  const ActivityStatusScreen({super.key});

  @override
  State<ActivityStatusScreen> createState() => _ActivityStatusScreenState();
}

class _ActivityStatusScreenState extends State<ActivityStatusScreen> {
  bool _showActivityStatus = true;
  bool _showLastSeen = true;
  bool _showOnlineStatus = true;
  bool _showTypingIndicator = true;
  bool _showReadReceipts = true;
  String _customStatus = '';
  String _statusVisibility = 'Everyone';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _showActivityStatus = prefs.getBool('show_activity_status') ?? true;
      _showLastSeen = prefs.getBool('show_last_seen') ?? true;
      _showOnlineStatus = prefs.getBool('show_online_status') ?? true;
      _showTypingIndicator = prefs.getBool('show_typing_indicator') ?? true;
      _showReadReceipts = prefs.getBool('show_read_receipts') ?? true;
      _customStatus = prefs.getString('custom_status') ?? '';
      _statusVisibility = prefs.getString('status_visibility') ?? 'Everyone';
    });
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('show_activity_status', _showActivityStatus);
    await prefs.setBool('show_last_seen', _showLastSeen);
    await prefs.setBool('show_online_status', _showOnlineStatus);
    await prefs.setBool('show_typing_indicator', _showTypingIndicator);
    await prefs.setBool('show_read_receipts', _showReadReceipts);
    await prefs.setString('custom_status', _customStatus);
    await prefs.setString('status_visibility', _statusVisibility);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Activity Status',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // معاينة حالة النشاط
            _buildStatusPreview(),

            const SizedBox(height: 16),

            // إعدادات الرؤية
            _buildSectionCard(
              title: 'Visibility Settings',
              icon: Icons.visibility,
              children: [
                _buildSwitchTile(
                  title: 'Show Activity Status',
                  subtitle: 'Let others see when you\'re active',
                  value: _showActivityStatus,
                  onChanged: (value) {
                    setState(() {
                      _showActivityStatus = value;
                      if (!value) {
                        _showLastSeen = false;
                        _showOnlineStatus = false;
                      }
                    });
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Show Last Seen',
                  subtitle: 'Show when you were last active',
                  value: _showLastSeen && _showActivityStatus,
                  onChanged: _showActivityStatus ? (value) {
                    setState(() => _showLastSeen = value);
                    _saveSettings();
                  } : null,
                ),
                _buildSwitchTile(
                  title: 'Show Online Status',
                  subtitle: 'Show green dot when online',
                  value: _showOnlineStatus && _showActivityStatus,
                  onChanged: _showActivityStatus ? (value) {
                    setState(() => _showOnlineStatus = value);
                    _saveSettings();
                  } : null,
                ),
                _buildListTile(
                  title: 'Who can see my status',
                  subtitle: _statusVisibility,
                  onTap: _showActivityStatus ? _selectStatusVisibility : null,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات التفاعل
            _buildSectionCard(
              title: 'Interaction Settings',
              icon: Icons.chat,
              children: [
                _buildSwitchTile(
                  title: 'Typing Indicator',
                  subtitle: 'Show when you\'re typing',
                  value: _showTypingIndicator,
                  onChanged: (value) {
                    setState(() => _showTypingIndicator = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Read Receipts',
                  subtitle: 'Show when you\'ve read messages',
                  value: _showReadReceipts,
                  onChanged: (value) {
                    setState(() => _showReadReceipts = value);
                    _saveSettings();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // حالة مخصصة
            _buildSectionCard(
              title: 'Custom Status',
              icon: Icons.edit,
              children: [
                _buildCustomStatusTile(),
              ],
            ),

            const SizedBox(height: 16),

            // معلومات إضافية
            _buildInfoCard(),
          ],
        ),
      ),
    );
  }

  /// بناء معاينة حالة النشاط
  Widget _buildStatusPreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.preview, color: const Color(0xFF4CAF50)),
              const SizedBox(width: 12),
              const Text(
                'Preview',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // معاينة كيف ستظهر للآخرين
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: const Color(0xFF4CAF50),
                    child: const Text(
                      'You',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_showOnlineStatus && _showActivityStatus)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: const Color(0xFF4CAF50),
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Your Name',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (_customStatus.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Text(
                        _customStatus,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                    if (_showLastSeen && _showActivityStatus) ...[
                      const SizedBox(height: 2),
                      Text(
                        'Last seen just now',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF4CAF50)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// بناء مفتاح تبديل
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: onChanged != null ? Colors.black87 : Colors.grey,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: onChanged != null ? Colors.grey[600] : Colors.grey[400],
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: const Color(0xFF4CAF50),
    );
  }

  /// بناء عنصر قائمة
  Widget _buildListTile({
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: onTap != null ? Colors.black87 : Colors.grey,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: onTap != null ? Colors.grey[600] : Colors.grey[400],
        ),
      ),
      trailing: onTap != null ? Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ) : null,
      onTap: onTap,
    );
  }

  /// بناء حقل الحالة المخصصة
  Widget _buildCustomStatusTile() {
    return ListTile(
      title: const Text(
        'Custom Status Message',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        _customStatus.isEmpty ? 'Tap to add a status' : _customStatus,
        style: TextStyle(
          fontSize: 14,
          color: _customStatus.isEmpty ? Colors.grey[500] : Colors.grey[600],
          fontStyle: _customStatus.isEmpty ? FontStyle.italic : FontStyle.normal,
        ),
      ),
      trailing: Icon(
        Icons.edit,
        color: Colors.grey[400],
        size: 20,
      ),
      onTap: _editCustomStatus,
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue[700]),
              const SizedBox(width: 8),
              Text(
                'About Activity Status',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Your activity status helps friends know when you\'re available to chat. You can control who sees your status and what information is shared.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue[600],
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار رؤية الحالة
  void _selectStatusVisibility() {
    final options = ['Everyone', 'Friends Only', 'Close Friends', 'No One'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Who can see your status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) => RadioListTile<String>(
            title: Text(option),
            value: option,
            groupValue: _statusVisibility,
            onChanged: (value) {
              setState(() => _statusVisibility = value!);
              _saveSettings();
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF4CAF50),
          )).toList(),
        ),
      ),
    );
  }

  /// تحرير الحالة المخصصة
  void _editCustomStatus() {
    final controller = TextEditingController(text: _customStatus);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Custom Status'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'What\'s on your mind?',
            border: OutlineInputBorder(),
          ),
          maxLength: 100,
          maxLines: 2,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _customStatus = '');
              _saveSettings();
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() => _customStatus = controller.text);
              _saveSettings();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/story_model.dart';
import '../../services/story_service.dart';
import '../../services/auth_service.dart';
import 'story_viewer_screen.dart';
import 'add_story_screen.dart';

/// شاشة قصصي
class MyStoriesScreen extends StatefulWidget {
  const MyStoriesScreen({super.key});

  @override
  State<MyStoriesScreen> createState() => _MyStoriesScreenState();
}

class _MyStoriesScreenState extends State<MyStoriesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'My Stories',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFFD32F2F),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AddStoryScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add, color: Colors.white),
          ),
        ],
      ),
      body: Consumer<StoryService>(
        builder: (context, storyService, child) {
          final myStories = storyService.getCurrentUserStories();
          
          if (myStories.isEmpty) {
            return _buildEmptyState();
          }
          
          return Column(
            children: [
              // إحصائيات القصص
              _buildStatsSection(myStories),
              
              // قائمة القصص
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: myStories.length,
                  itemBuilder: (context, index) {
                    final story = myStories[index];
                    return _buildStoryItem(context, story, index);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.auto_stories_outlined,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No stories yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Share your first story with friends',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AddStoryScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Story'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD32F2F),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection(List<StoryModel> stories) {
    final totalViews = stories.fold<int>(0, (sum, story) => sum + story.viewCount);
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFD32F2F).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFD32F2F).withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            icon: Icons.auto_stories,
            label: 'Stories',
            value: '${stories.length}',
          ),
          _buildStatItem(
            icon: Icons.visibility,
            label: 'Total Views',
            value: '$totalViews',
          ),
          _buildStatItem(
            icon: Icons.schedule,
            label: 'Active',
            value: '${stories.where((s) => !s.isExpired).length}',
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFFD32F2F),
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFFD32F2F),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// بناء عنصر قصة
  Widget _buildStoryItem(BuildContext context, StoryModel story, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: _buildStoryThumbnail(story),
        title: Text(
          _getStoryTitle(story),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              _getStoryPreview(story),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 13,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.visibility,
                  size: 16,
                  color: Colors.grey[500],
                ),
                const SizedBox(width: 4),
                Text(
                  '${story.viewCount} views',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: story.isExpired ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  story.isExpired ? 'Expired' : story.timeRemainingText,
                  style: TextStyle(
                    color: story.isExpired ? Colors.red : Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(context, value, story),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility, size: 20),
                  SizedBox(width: 8),
                  Text('View'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _viewStory(context, story, index),
      ),
    );
  }

  /// بناء صورة مصغرة للقصة
  Widget _buildStoryThumbnail(StoryModel story) {
    switch (story.type) {
      case StoryType.text:
        return Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: story.backgroundColor ?? const Color(0xFFD32F2F),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.text_fields,
            color: Colors.white,
            size: 24,
          ),
        );
        
      case StoryType.image:
        return Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: story.mediaUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    story.mediaUrl!,
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.broken_image,
                        color: Colors.grey,
                        size: 24,
                      );
                    },
                  ),
                )
              : const Icon(
                  Icons.image,
                  color: Colors.grey,
                  size: 24,
                ),
        );
        
      case StoryType.video:
        return Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.play_circle_outline,
            color: Colors.grey,
            size: 24,
          ),
        );
    }
  }

  /// الحصول على عنوان القصة
  String _getStoryTitle(StoryModel story) {
    switch (story.type) {
      case StoryType.text:
        return 'Text Story';
      case StoryType.image:
        return 'Photo Story';
      case StoryType.video:
        return 'Video Story';
    }
  }

  /// الحصول على معاينة القصة
  String _getStoryPreview(StoryModel story) {
    switch (story.type) {
      case StoryType.text:
        return story.content ?? '';
      case StoryType.image:
        return 'Shared a photo';
      case StoryType.video:
        return 'Shared a video';
    }
  }

  /// عرض القصة
  void _viewStory(BuildContext context, StoryModel story, int index) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;
    
    if (currentUser != null) {
      final userStories = UserStoriesModel(
        userId: currentUser.phoneNumber,
        userName: currentUser.name,
        userProfileImage: currentUser.profileImageUrl,
        stories: Provider.of<StoryService>(context, listen: false).getCurrentUserStories(),
      );
      
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => StoryViewerScreen(
            userStories: userStories,
            initialIndex: index,
          ),
        ),
      );
    }
  }

  /// التعامل مع إجراءات القائمة
  void _handleMenuAction(BuildContext context, String action, StoryModel story) {
    switch (action) {
      case 'view':
        final stories = Provider.of<StoryService>(context, listen: false).getCurrentUserStories();
        final index = stories.indexWhere((s) => s.id == story.id);
        if (index != -1) {
          _viewStory(context, story, index);
        }
        break;
        
      case 'delete':
        _showDeleteConfirmation(context, story);
        break;
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(BuildContext context, StoryModel story) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Story'),
        content: const Text('Are you sure you want to delete this story?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              final success = await Provider.of<StoryService>(context, listen: false)
                  .deleteStory(story.id);
              
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Story deleted successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import '../models/reaction_model.dart';

/// خدمة الرسائل المثبتة
class PinnedMessagesService extends ChangeNotifier {
  static final PinnedMessagesService _instance = PinnedMessagesService._internal();
  factory PinnedMessagesService() => _instance;
  PinnedMessagesService._internal();

  // قائمة الرسائل المثبتة
  final List<PinnedMessage> _pinnedMessages = [];
  
  // Controllers للبيانات المباشرة
  final StreamController<List<PinnedMessage>> _pinnedController = StreamController.broadcast();

  // الحد الأقصى للرسائل المثبتة لكل دردشة
  static const int maxPinnedPerChat = 5;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadPinnedMessages();
    _startExpirationTimer();
  }

  /// تثبيت رسالة
  Future<bool> pinMessage({
    required String messageId,
    required String chatId,
    required String content,
    required String pinnedBy,
    required String pinnedByName,
    Duration? duration,
  }) async {
    try {
      // التحقق من عدم تثبيت الرسالة مسبقاً
      if (isMessagePinned(messageId)) {
        debugPrint('📌 Message already pinned: $messageId');
        return false;
      }

      // التحقق من الحد الأقصى للرسائل المثبتة
      final chatPinnedMessages = getPinnedMessagesForChat(chatId);
      if (chatPinnedMessages.length >= maxPinnedPerChat) {
        debugPrint('📌 Maximum pinned messages reached for chat: $chatId');
        return false;
      }

      // إنشاء رسالة مثبتة
      final pinnedMessage = PinnedMessage(
        id: 'pinned_${DateTime.now().millisecondsSinceEpoch}',
        messageId: messageId,
        chatId: chatId,
        content: content,
        pinnedBy: pinnedBy,
        pinnedByName: pinnedByName,
        pinnedAt: DateTime.now(),
        expiresAt: duration != null ? DateTime.now().add(duration) : null,
      );

      _pinnedMessages.add(pinnedMessage);
      await _savePinnedMessages();
      _updateStreams();

      debugPrint('📌 Message pinned successfully: $messageId');
      return true;
    } catch (e) {
      debugPrint('❌ Error pinning message: $e');
      return false;
    }
  }

  /// إلغاء تثبيت رسالة
  Future<bool> unpinMessage(String messageId) async {
    try {
      final index = _pinnedMessages.indexWhere((pm) => pm.messageId == messageId);
      if (index == -1) return false;

      _pinnedMessages.removeAt(index);
      await _savePinnedMessages();
      _updateStreams();

      debugPrint('📌 Message unpinned successfully: $messageId');
      return true;
    } catch (e) {
      debugPrint('❌ Error unpinning message: $e');
      return false;
    }
  }

  /// إلغاء تثبيت رسالة بالمعرف
  Future<bool> unpinById(String pinnedId) async {
    try {
      final index = _pinnedMessages.indexWhere((pm) => pm.id == pinnedId);
      if (index == -1) return false;

      _pinnedMessages.removeAt(index);
      await _savePinnedMessages();
      _updateStreams();

      debugPrint('📌 Pinned message removed successfully: $pinnedId');
      return true;
    } catch (e) {
      debugPrint('❌ Error removing pinned message: $e');
      return false;
    }
  }

  /// الحصول على الرسائل المثبتة لدردشة معينة
  List<PinnedMessage> getPinnedMessagesForChat(String chatId) {
    return _pinnedMessages
        .where((pm) => pm.chatId == chatId && pm.isActive && !pm.isExpired)
        .toList()
      ..sort((a, b) => b.pinnedAt.compareTo(a.pinnedAt)); // الأحدث أولاً
  }

  /// الحصول على جميع الرسائل المثبتة
  List<PinnedMessage> getAllPinnedMessages() {
    return _pinnedMessages
        .where((pm) => pm.isActive && !pm.isExpired)
        .toList()
      ..sort((a, b) => b.pinnedAt.compareTo(a.pinnedAt));
  }

  /// التحقق من تثبيت رسالة
  bool isMessagePinned(String messageId) {
    return _pinnedMessages.any((pm) => 
        pm.messageId == messageId && pm.isActive && !pm.isExpired);
  }

  /// الحصول على رسالة مثبتة
  PinnedMessage? getPinnedMessage(String messageId) {
    try {
      return _pinnedMessages.firstWhere((pm) => 
          pm.messageId == messageId && pm.isActive && !pm.isExpired);
    } catch (e) {
      return null;
    }
  }

  /// تحديث مدة انتهاء الصلاحية
  Future<bool> updateExpiration(String pinnedId, Duration? newDuration) async {
    try {
      final index = _pinnedMessages.indexWhere((pm) => pm.id == pinnedId);
      if (index == -1) return false;

      final expiresAt = newDuration != null 
          ? DateTime.now().add(newDuration) 
          : null;

      _pinnedMessages[index] = _pinnedMessages[index].copyWith(expiresAt: expiresAt);
      await _savePinnedMessages();
      _updateStreams();

      debugPrint('📌 Pinned message expiration updated: $pinnedId');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating pinned message expiration: $e');
      return false;
    }
  }

  /// إزالة الرسائل المنتهية الصلاحية
  Future<void> removeExpiredMessages() async {
    final expiredMessages = _pinnedMessages.where((pm) => pm.isExpired).toList();
    
    for (final expired in expiredMessages) {
      _pinnedMessages.remove(expired);
    }

    if (expiredMessages.isNotEmpty) {
      await _savePinnedMessages();
      _updateStreams();
      debugPrint('📌 Removed ${expiredMessages.length} expired pinned messages');
    }
  }

  /// حذف جميع الرسائل المثبتة لدردشة
  Future<void> clearPinnedMessagesForChat(String chatId) async {
    final chatMessages = _pinnedMessages.where((pm) => pm.chatId == chatId).toList();
    
    for (final message in chatMessages) {
      _pinnedMessages.remove(message);
    }

    await _savePinnedMessages();
    _updateStreams();
    debugPrint('📌 Cleared ${chatMessages.length} pinned messages for chat: $chatId');
  }

  /// حذف جميع الرسائل المثبتة
  Future<void> clearAllPinnedMessages() async {
    _pinnedMessages.clear();
    await _savePinnedMessages();
    _updateStreams();
    debugPrint('📌 Cleared all pinned messages');
  }

  /// الحصول على عدد الرسائل المثبتة لدردشة
  int getPinnedCountForChat(String chatId) {
    return getPinnedMessagesForChat(chatId).length;
  }

  /// التحقق من إمكانية تثبيت رسائل إضافية
  bool canPinMoreMessages(String chatId) {
    return getPinnedCountForChat(chatId) < maxPinnedPerChat;
  }

  /// الحصول على الرسائل المثبتة المنتهية الصلاحية قريباً
  List<PinnedMessage> getExpiringMessages({Duration? within}) {
    final threshold = within ?? const Duration(hours: 1);
    final cutoffTime = DateTime.now().add(threshold);

    return _pinnedMessages
        .where((pm) => 
            pm.isActive && 
            !pm.isExpired && 
            pm.expiresAt != null && 
            pm.expiresAt!.isBefore(cutoffTime))
        .toList()
      ..sort((a, b) => a.expiresAt!.compareTo(b.expiresAt!));
  }

  /// بدء مؤقت إزالة الرسائل المنتهية الصلاحية
  void _startExpirationTimer() {
    Timer.periodic(const Duration(minutes: 5), (timer) {
      removeExpiredMessages();
    });
  }

  /// تحديث البيانات المباشرة
  void _updateStreams() {
    _pinnedController.add(List.from(_pinnedMessages));
    notifyListeners();
  }

  /// تحميل الرسائل المثبتة
  Future<void> _loadPinnedMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pinnedJson = prefs.getString('pinned_messages');

      if (pinnedJson != null) {
        final pinnedList = json.decode(pinnedJson) as List;
        _pinnedMessages.clear();
        for (final pinnedMap in pinnedList) {
          _pinnedMessages.add(PinnedMessage.fromMap(pinnedMap));
        }
      }

      // إزالة الرسائل المنتهية الصلاحية عند التحميل
      await removeExpiredMessages();

      debugPrint('📌 Loaded ${_pinnedMessages.length} pinned messages');
    } catch (e) {
      debugPrint('❌ Error loading pinned messages: $e');
    }
  }

  /// حفظ الرسائل المثبتة
  Future<void> _savePinnedMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pinnedList = _pinnedMessages.map((pm) => pm.toMap()).toList();
      final pinnedJson = json.encode(pinnedList);
      await prefs.setString('pinned_messages', pinnedJson);
    } catch (e) {
      debugPrint('❌ Error saving pinned messages: $e');
    }
  }

  /// إحصائيات الرسائل المثبتة
  Map<String, dynamic> getPinnedStats() {
    final stats = <String, dynamic>{};
    
    stats['total'] = _pinnedMessages.where((pm) => pm.isActive && !pm.isExpired).length;
    stats['expired'] = _pinnedMessages.where((pm) => pm.isExpired).length;
    stats['withExpiration'] = _pinnedMessages.where((pm) => pm.expiresAt != null).length;
    
    // إحصائيات حسب الدردشة
    final chatStats = <String, int>{};
    for (final pm in _pinnedMessages.where((pm) => pm.isActive && !pm.isExpired)) {
      chatStats[pm.chatId] = (chatStats[pm.chatId] ?? 0) + 1;
    }
    stats['byChat'] = chatStats;
    
    // الرسائل المنتهية الصلاحية قريباً
    stats['expiringSoon'] = getExpiringMessages().length;
    
    return stats;
  }

  // Getters
  Stream<List<PinnedMessage>> get pinnedMessagesStream => _pinnedController.stream;
  int get totalPinnedMessages => _pinnedMessages.where((pm) => pm.isActive && !pm.isExpired).length;
  int get maxPinnedPerChatLimit => maxPinnedPerChat;

  @override
  void dispose() {
    _pinnedController.close();
    super.dispose();
  }
}

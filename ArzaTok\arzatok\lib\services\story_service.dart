import 'package:flutter/material.dart';
import 'dart:async';
import '../models/story_model.dart';
import '../models/user_model.dart';

import 'storage_service.dart';
import 'media_service.dart';

/// خدمة إدارة القصص
class StoryService extends ChangeNotifier {
  // قوائم محلية لتخزين القصص
  static final List<StoryModel> _localStories = [];
  static final List<UserStoriesModel> _userStories = [];

  // Controllers للبيانات المباشرة
  final StreamController<List<StoryModel>> _storiesController = StreamController.broadcast();
  final StreamController<List<UserStoriesModel>> _userStoriesController = StreamController.broadcast();

  // خدمة التخزين
  final StorageService _storageService = StorageService();

  // Timer لحذف القصص المنتهية الصلاحية
  Timer? _cleanupTimer;

  StoryService() {
    _initializeService();
  }

  /// تهيئة الخدمة
  void _initializeService() {
    // إضافة قصص تجريبية
    _addDemoStories();

    // بدء timer لتنظيف القصص المنتهية
    _startCleanupTimer();

    // تحديث البيانات
    _updateStreams();
  }

  /// بدء timer لحذف القصص المنتهية
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _removeExpiredStories();
    });
  }

  /// إضافة قصص تجريبية
  void _addDemoStories() {
    final now = DateTime.now();

    // قصص تجريبية
    final demoStories = [
      StoryModel.text(
        id: 'story_1',
        userId: '+1234567890',
        userName: 'John Smith',
        content: 'Good morning everyone! 🌅',
        backgroundColor: const Color(0xFF4CAF50),
      ),
      StoryModel.text(
        id: 'story_2',
        userId: '+1987654321',
        userName: 'Sarah Johnson',
        content: 'Having a great day! ☀️',
        backgroundColor: const Color(0xFF2196F3),
      ),
      StoryModel.text(
        id: 'story_3',
        userId: '+1555123456',
        userName: 'Mike Davis',
        content: 'Working from home today 💻',
        backgroundColor: const Color(0xFF9C27B0),
      ),
      StoryModel.text(
        id: 'story_4',
        userId: '+1777888999',
        userName: 'Emily Wilson',
        content: 'Beautiful sunset! 🌅',
        backgroundColor: const Color(0xFFFF9800),
      ),
    ];

    _localStories.addAll(demoStories);
    _groupStoriesByUser();
  }

  /// تجميع القصص حسب المستخدم
  void _groupStoriesByUser() {
    _userStories.clear();

    // تجميع القصص حسب userId
    final Map<String, List<StoryModel>> groupedStories = {};

    for (final story in _localStories) {
      if (!story.isExpired) {
        groupedStories.putIfAbsent(story.userId, () => []).add(story);
      }
    }

    // إنشاء UserStoriesModel لكل مستخدم
    for (final entry in groupedStories.entries) {
      final userId = entry.key;
      final stories = entry.value;

      if (stories.isNotEmpty) {
        // ترتيب القصص حسب التاريخ
        stories.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        final userStories = UserStoriesModel(
          userId: userId,
          userName: stories.first.userName,
          userProfileImage: stories.first.userProfileImage,
          stories: stories,
          hasUnviewedStories: stories.any((s) => !s.isViewed),
        );

        _userStories.add(userStories);
      }
    }

    // ترتيب المستخدمين: غير المشاهدة أولاً، ثم حسب آخر قصة
    _userStories.sort((a, b) {
      // القصص غير المشاهدة أولاً
      if (a.hasUnviewedStories && !b.hasUnviewedStories) return -1;
      if (!a.hasUnviewedStories && b.hasUnviewedStories) return 1;

      // ثم حسب آخر قصة
      final aLatest = a.latestStory?.createdAt ?? DateTime(0);
      final bLatest = b.latestStory?.createdAt ?? DateTime(0);
      return bLatest.compareTo(aLatest);
    });
  }

  /// تحديث البيانات المباشرة
  void _updateStreams() {
    _groupStoriesByUser();
    _storiesController.add(List.from(_localStories));
    _userStoriesController.add(List.from(_userStories));
    notifyListeners();
  }

  /// إضافة قصة نصية
  Future<bool> addTextStory({
    required String content,
    Color? backgroundColor,
    Color? textColor,
    String? fontFamily,
  }) async {
    try {
      // الحصول على المستخدم الحالي (محاكاة)
      final currentUser = UserModel(
        phoneNumber: '+1234567890',
        name: 'Current User',
        lastSeen: DateTime.now(),
        isOnline: true,
        gender: Gender.male,
        age: 25,
        country: 'Country',
        city: 'City',
        registrationDate: DateTime.now(),
      );

      final story = StoryModel.text(
        id: 'story_${DateTime.now().millisecondsSinceEpoch}',
        userId: currentUser.phoneNumber,
        userName: currentUser.name,
        userProfileImage: currentUser.profileImageUrl,
        content: content,
        backgroundColor: backgroundColor,
        textColor: textColor,
        fontFamily: fontFamily,
      );

      _localStories.add(story);
      _updateStreams();

      debugPrint('✅ Text story added successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding text story: $e');
      return false;
    }
  }

  /// إضافة قصة صورة مع Firebase Storage
  Future<bool> addImageStory(String? currentUserPhone, String? currentUserName) async {
    try {
      if (currentUserPhone == null || currentUserName == null) {
        debugPrint('❌ User not authenticated');
        return false;
      }

      // استخدام MediaService لاختيار ورفع الصورة
      final mediaService = MediaService();
      final imageUrl = await mediaService.pickImage();

      if (imageUrl == null) {
        debugPrint('❌ No image selected or upload failed');
        return false;
      }

      final story = StoryModel.image(
        id: 'story_${DateTime.now().millisecondsSinceEpoch}',
        userId: currentUserPhone,
        userName: currentUserName,
        userProfileImage: '',
        imageUrl: imageUrl,
      );

      _localStories.add(story);
      _updateStreams();

      debugPrint('✅ Image story added successfully with Firebase URL: $imageUrl');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding image story: $e');
      return false;
    }
  }

  /// إضافة قصة فيديو مع Firebase Storage
  Future<bool> addVideoStory(String? currentUserPhone, String? currentUserName) async {
    try {
      if (currentUserPhone == null || currentUserName == null) {
        debugPrint('❌ User not authenticated');
        return false;
      }

      // استخدام MediaService لاختيار ورفع الفيديو
      final mediaService = MediaService();
      final videoUrl = await mediaService.pickVideo();

      if (videoUrl == null) {
        debugPrint('❌ No video selected or upload failed');
        return false;
      }

      final story = StoryModel.video(
        id: 'story_${DateTime.now().millisecondsSinceEpoch}',
        userId: currentUserPhone,
        userName: currentUserName,
        userProfileImage: '',
        videoUrl: videoUrl,
      );

      _localStories.add(story);
      _updateStreams();

      debugPrint('✅ Video story added successfully with Firebase URL: $videoUrl');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding video story: $e');
      return false;
    }
  }

  /// حذف قصة
  Future<bool> deleteStory(String storyId) async {
    try {
      final storyIndex = _localStories.indexWhere((s) => s.id == storyId);
      if (storyIndex == -1) return false;

      final story = _localStories[storyIndex];

      // التأكد من أن المستخدم يملك القصة (محاكاة)
      const currentUserPhone = '+1234567890';
      if (story.userId != currentUserPhone) {
        return false;
      }

      _localStories.removeAt(storyIndex);
      _updateStreams();

      debugPrint('✅ Story deleted successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting story: $e');
      return false;
    }
  }

  /// تحديد قصة كمشاهدة
  Future<void> markStoryAsViewed(String storyId, String viewerId) async {
    try {
      final storyIndex = _localStories.indexWhere((s) => s.id == storyId);
      if (storyIndex == -1) return;

      final story = _localStories[storyIndex];

      // إضافة المشاهد إذا لم يكن موجود
      if (!story.viewedBy.contains(viewerId)) {
        final updatedViewedBy = List<String>.from(story.viewedBy)..add(viewerId);
        _localStories[storyIndex] = story.copyWith(
          viewedBy: updatedViewedBy,
          isViewed: true,
        );

        _updateStreams();
        debugPrint('✅ Story marked as viewed');
      }
    } catch (e) {
      debugPrint('❌ Error marking story as viewed: $e');
    }
  }

  /// حذف القصص المنتهية الصلاحية
  void _removeExpiredStories() {
    final initialCount = _localStories.length;
    _localStories.removeWhere((story) => story.isExpired);

    if (_localStories.length != initialCount) {
      _updateStreams();
      debugPrint('🗑️ Removed ${initialCount - _localStories.length} expired stories');
    }
  }

  /// الحصول على قصص المستخدم الحالي
  List<StoryModel> getCurrentUserStories() {
    const currentUserPhone = '+1234567890';

    return _localStories
        .where((story) => story.userId == currentUserPhone && !story.isExpired)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// الحصول على قصص مستخدم معين
  List<StoryModel> getUserStories(String userId) {
    return _localStories
        .where((story) => story.userId == userId && !story.isExpired)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Getters للبيانات
  List<StoryModel> get allStories => List.from(_localStories.where((s) => !s.isExpired));
  List<UserStoriesModel> get userStories => List.from(_userStories);
  Stream<List<StoryModel>> get storiesStream => _storiesController.stream;
  Stream<List<UserStoriesModel>> get userStoriesStream => _userStoriesController.stream;

  @override
  void dispose() {
    _cleanupTimer?.cancel();
    _storiesController.close();
    _userStoriesController.close();
    super.dispose();
  }
}

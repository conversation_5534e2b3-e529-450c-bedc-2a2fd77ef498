import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:convert';
import 'dart:async';
import 'dart:io';
import '../models/post_model.dart';
import '../models/link_preview_model.dart';

/// خدمة الشبكة الاجتماعية
class SocialFeedService extends ChangeNotifier {
  static final SocialFeedService _instance = SocialFeedService._internal();
  factory SocialFeedService() => _instance;
  SocialFeedService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // قائمة المنشورات
  final List<PostModel> _posts = [];
  final List<CommentModel> _comments = [];

  // Controllers للبيانات المباشرة
  final StreamController<List<PostModel>> _postsController = StreamController.broadcast();
  final StreamController<List<CommentModel>> _commentsController = StreamController.broadcast();

  // التفاعلات الستة للفيسبوك
  final List<Map<String, dynamic>> _facebookReactions = [
    {'emoji': '👍', 'name': 'Like', 'color': 0xFF1877F2},
    {'emoji': '❤️', 'name': 'Love', 'color': 0xFFE91E63},
    {'emoji': '😂', 'name': 'Haha', 'color': 0xFFFFC107},
    {'emoji': '😮', 'name': 'Wow', 'color': 0xFFFFC107},
    {'emoji': '😢', 'name': 'Sad', 'color': 0xFFFFC107},
    {'emoji': '😡', 'name': 'Angry', 'color': 0xFFFF5722},
  ];

  /// تهيئة الخدمة - Firebase أولاً
  Future<void> initialize() async {
    debugPrint('🔄 Initializing SocialFeedService...');

    // إضافة المستخدمين التجريبيين أولاً
    await _addDemoUsers();

    // تحميل من Firebase أولاً
    await _loadPostsFromFirestore();

    // إذا لم توجد منشورات في Firebase، حمل من التخزين المحلي
    if (_posts.isEmpty) {
      debugPrint('📱 No posts in Firebase, loading from local storage...');
      await _loadPosts();
    }

    // إذا لم توجد منشورات محلية، أضف المنشورات التجريبية
    if (_posts.isEmpty) {
      debugPrint('📱 No posts found, adding demo posts...');
      await _addSamplePosts();
    }

    await _loadComments();

    debugPrint('✅ SocialFeedService initialized with ${_posts.length} posts');
    _updateStreams();
  }

  /// إعادة تحميل البيانات مع المستخدمين التجريبيين (للاختبار)
  Future<void> resetWithDemoData() async {
    debugPrint('🔄 Resetting with demo data...');

    // مسح البيانات الحالية
    _posts.clear();

    // إضافة المستخدمين التجريبيين
    await _addDemoUsers();

    // إضافة المنشورات التجريبية
    await _addSamplePosts();

    debugPrint('✅ Reset completed with ${_posts.length} demo posts');
    _updateStreams();
  }

  /// تحميل المنشورات من Firestore
  Future<void> _loadPostsFromFirestore() async {
    try {
      debugPrint('🔄 Loading posts from Firestore...');
      debugPrint('🔗 Firebase project: ${_firestore.app.options.projectId}');

      final snapshot = await _firestore.collection('posts')
          .orderBy('createdAt', descending: true)
          .get();

      debugPrint('📊 Firestore query returned ${snapshot.docs.length} documents');

      _posts.clear();
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id; // إضافة المعرف
          debugPrint('📄 Document ${doc.id}: $data');
          final post = PostModel.fromMap(data);
          _posts.add(post);
          debugPrint('✅ Loaded post: ${post.id} - "${post.content}"');
          debugPrint('👍 Reactions: ${post.reactions}');
          debugPrint('🖼️ Images: ${post.images.length}');
          debugPrint('🎥 Videos: ${post.videos.length}');
        } catch (e) {
          debugPrint('❌ Error parsing post ${doc.id}: $e');
          debugPrint('📄 Raw data: ${doc.data()}');
        }
      }

      debugPrint('✅ Successfully loaded ${_posts.length} posts from Firestore');
      _updateStreams();
    } catch (e) {
      debugPrint('❌ CRITICAL ERROR loading posts from Firestore: $e');
      debugPrint('📱 Falling back to local storage...');
      await _loadPosts();
    }
  }

  /// إضافة مستخدمين تجريبيين حقيقيين
  Future<void> _addDemoUsers() async {
    final prefs = await SharedPreferences.getInstance();
    final usersJson = prefs.getString('registered_users');

    List<Map<String, dynamic>> existingUsers = [];
    if (usersJson != null) {
      final usersList = json.decode(usersJson) as List;
      existingUsers = usersList.cast<Map<String, dynamic>>();
    }

    // إضافة 3 مستخدمين تجريبيين إذا لم يكونوا موجودين
    final demoUsers = [
      {
        'phoneNumber': '+212600123456',
        'name': 'أحمد المغربي',
        'profileImageUrl': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        'gender': 'ذكر',
        'age': 28,
        'country': 'المغرب',
        'city': 'الدار البيضاء',
        'isOnline': true,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
      },
      {
        'phoneNumber': '+212600789012',
        'name': 'فاطمة الزهراء',
        'profileImageUrl': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        'gender': 'أنثى',
        'age': 25,
        'country': 'المغرب',
        'city': 'الرباط',
        'isOnline': false,
        'lastSeen': DateTime.now().subtract(const Duration(hours: 2)).millisecondsSinceEpoch,
      },
      {
        'phoneNumber': '+212600345678',
        'name': 'يوسف التطواني',
        'profileImageUrl': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        'gender': 'ذكر',
        'age': 32,
        'country': 'المغرب',
        'city': 'تطوان',
        'isOnline': true,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
      },
    ];

    // التحقق من عدم وجود المستخدمين التجريبيين مسبق<|im_start|>
    bool needsUpdate = false;
    for (final demoUser in demoUsers) {
      final exists = existingUsers.any((user) => user['phoneNumber'] == demoUser['phoneNumber']);
      if (!exists) {
        existingUsers.add(demoUser);
        needsUpdate = true;
      }
    }

    if (needsUpdate) {
      await prefs.setString('registered_users', json.encode(existingUsers));
      debugPrint('✅ تم إضافة ${demoUsers.length} مستخدمين تجريبيين');
    }
  }

  /// إضافة منشورات تجريبية مع بيانات المستخدمين الحقيقية
  Future<void> _addSamplePosts() async {
    if (_posts.isEmpty) {
      // إضافة المستخدمين التجريبيين أولاً
      await _addDemoUsers();

      // الحصول على المستخدمين المسجلين
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString('registered_users');

      List<Map<String, dynamic>> registeredUsers = [];
      if (usersJson != null) {
        final usersList = json.decode(usersJson) as List;
        registeredUsers = usersList.cast<Map<String, dynamic>>();
      }

      // إنشاء منشورات بأسماء المستخدمين الحقيقية
      final samplePosts = <PostModel>[];

      if (registeredUsers.length >= 3) {
        // استخدام آخر 3 مستخدمين (المستخدمين التجريبيين)
        final demoUsersData = registeredUsers.take(3).toList();

        for (int i = 0; i < demoUsersData.length; i++) {
          final user = demoUsersData[i];

          // إضافة منشورات متنوعة لكل مستخدم
          final userPosts = _getDemoPostsForUser(i, user);
          samplePosts.addAll(userPosts);
        }
      } else {
        // منشورات افتراضية إذا لم يكن هناك مستخدمين
        samplePosts.addAll([
          PostModel(
            id: 'post_1',
            authorId: 'demo_user_1',
            authorName: 'ArzaTalk Demo',
            authorAvatar: '',
            content: 'مرحباً بكم في ArzaTalk! 🎉 تطبيق الدردشة والشبكة الاجتماعية الجديد',
            createdAt: DateTime.now().subtract(const Duration(hours: 2)),
            type: PostType.text,
            reactions: {'👍': 15, '❤️': 8, '😂': 3},
            commentsCount: 5,
            sharesCount: 2,
          ),
        ]);
      }

      _posts.addAll(samplePosts);
      await _savePosts();
      _updateStreams();
    }
  }

  /// الحصول على منشورات تجريبية لمستخدم معين
  List<PostModel> _getDemoPostsForUser(int userIndex, Map<String, dynamic> user) {
    final now = DateTime.now();
    final posts = <PostModel>[];

    // محتويات مختلفة لكل مستخدم
    final userContents = [
      // أحمد المغربي
      [
        'مرحباً بكم في ArzaTalk! 🎉 تطبيق رائع للدردشة والتواصل الاجتماعي',
        'يوم جميل في الدار البيضاء! ☀️ أتمنى لكم جميعاً يوماً سعيداً',
        'شاهدوا هذا الفيديو الرائع! 🎬',
      ],
      // فاطمة الزهراء
      [
        'أحب هذا التطبيق الجديد! سهل الاستخدام ومليء بالميزات المفيدة 💖',
        'صور جميلة من الرباط 📸',
        'تجربة رائعة مع الميزات الجديدة! 🚀',
      ],
      // يوسف التطواني
      [
        'شكراً لفريق التطوير على هذا العمل الرائع! 👏',
        'مناظر خلابة من تطوان 🏔️',
        'فيديو تعليمي مفيد للجميع! 📚',
      ],
    ];

    final contents = userContents[userIndex % userContents.length];

    for (int i = 0; i < contents.length; i++) {
      final postId = 'demo_${user['phoneNumber']}_$i';

      // تحديد نوع المنشور
      PostType postType = PostType.text;
      List<String> images = [];
      List<String> videos = [];

      if (i == 1) {
        // منشور بصور
        postType = PostType.image;
        images = ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'];
      } else if (i == 2) {
        // منشور بفيديو
        postType = PostType.video;
        videos = ['https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'];
      }

      posts.add(PostModel(
        id: postId,
        authorId: user['phoneNumber'],
        authorName: user['name'],
        authorAvatar: user['profileImageUrl'] ?? '',
        content: contents[i],
        createdAt: now.subtract(Duration(hours: (userIndex * 3) + i + 1)),
        type: postType,
        reactions: _getSampleReactions(userIndex + i),
        commentsCount: (userIndex + i + 1) * 2,
        sharesCount: userIndex + i,
        repostsCount: (userIndex + i) * 2,
        images: images,
        videos: videos,
      ));
    }

    return posts;
  }

  /// الحصول على محتوى تجريبي
  String _getSampleContent(int index) {
    final contents = [
      'مرحباً بكم في ArzaTalk! 🎉 تطبيق رائع للدردشة والتواصل الاجتماعي',
      'يوم جميل اليوم! ☀️ أتمنى لكم جميعاً يوماً سعيداً ومليئاً بالإنجازات',
      'تجربة رائعة مع التطبيق الجديد! الميزات والتفاعلات مذهلة حقاً 🚀',
      'أحب هذا التطبيق! سهل الاستخدام ومليء بالميزات المفيدة 💖',
      'شكراً لفريق التطوير على هذا العمل الرائع! 👏',
    ];
    return contents[index % contents.length];
  }

  /// الحصول على تفاعلات تجريبية
  Map<String, int> _getSampleReactions(int index) {
    final reactions = [
      {'👍': 15, '❤️': 8, '😂': 3},
      {'👍': 12, '❤️': 20, '😮': 2},
      {'👍': 25, '❤️': 10, '😂': 5, '😮': 8},
      {'👍': 18, '❤️': 15, '😂': 7, '😢': 2},
      {'👍': 30, '❤️': 25, '😂': 10, '😮': 5, '😡': 1},
    ];
    return reactions[index % reactions.length];
  }

  /// إنشاء منشور جديد
  Future<bool> createPost({
    required String authorId,
    required String authorName,
    required String content,
    List<String> images = const [],
    PostType type = PostType.text,
    PostPrivacy privacy = PostPrivacy.public,
    String? location,
    List<String> tags = const [],
  }) async {
    try {
      final post = PostModel(
        id: 'post_${DateTime.now().millisecondsSinceEpoch}',
        authorId: authorId,
        authorName: authorName,
        authorAvatar: '',
        content: content,
        images: images,
        createdAt: DateTime.now(),
        type: type,
        privacy: privacy,
        location: location,
        tags: tags,
      );

      // حفظ المنشور في Firestore أولاً
      await _firestore.collection('posts').doc(post.id).set(post.toMap());
      debugPrint('✅ Post saved to Firestore: ${post.id}');

      // ثم إضافة للقائمة المحلية
      _posts.insert(0, post); // إضافة في المقدمة
      await _savePosts();
      _updateStreams();

      debugPrint('📝 Post added to local list: ${_posts.length} total posts');

      debugPrint('📝 Post created successfully: ${post.id}');
      return true;
    } catch (e) {
      debugPrint('❌ Error creating post: $e');
      return false;
    }
  }

  /// إنشاء منشور متقدم مع جميع الميزات
  Future<bool> createPostAdvanced({
    required String authorId,
    required String authorName,
    required String content,
    List<String> images = const [],
    List<String> videos = const [],
    String? musicUrl,
    String? musicTitle,
    String? location,
    String? feeling,
    String? backgroundColor,
    List<String> taggedUsers = const [],
    bool isLiveStream = false,
    PostType type = PostType.text,
    PostPrivacy privacy = PostPrivacy.public,
    List<LinkPreviewModel> linkPreviews = const [],
  }) async {
    try {
      final post = PostModel(
        id: 'post_${DateTime.now().millisecondsSinceEpoch}',
        authorId: authorId,
        authorName: authorName,
        authorAvatar: '',
        content: content,
        images: images,
        videos: videos,
        musicUrl: musicUrl,
        musicTitle: musicTitle,
        location: location,
        feeling: feeling,
        backgroundColor: backgroundColor,
        taggedUsers: taggedUsers,
        isLiveStream: isLiveStream,
        createdAt: DateTime.now(),
        type: type,
        privacy: privacy,
        linkPreviews: linkPreviews,
      );

      // حفظ المنشور في Firestore أولاً
      try {
        final postData = post.toMap();
        debugPrint('📝 Saving post data to Firestore: $postData');
        debugPrint('🔗 Firebase project: ${_firestore.app.options.projectId}');
        debugPrint('📊 Post size: ${postData.toString().length} characters');

        // محاولة الحفظ مع timeout
        await _firestore.collection('posts').doc(post.id).set(postData).timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            throw Exception('Firebase save timeout after 10 seconds');
          },
        );

        debugPrint('✅ Advanced post saved to Firestore: ${post.id}');

        // التحقق من الحفظ مع timeout
        final savedDoc = await _firestore.collection('posts').doc(post.id).get().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            throw Exception('Firebase read timeout after 5 seconds');
          },
        );

        if (savedDoc.exists) {
          debugPrint('✅ Post verified in Firestore: ${savedDoc.data()}');
          debugPrint('🎯 SUCCESS: Post is permanently saved in Firebase!');
        } else {
          debugPrint('❌ Post not found in Firestore after save!');
          throw Exception('Post not found after save');
        }
      } catch (firestoreError) {
        debugPrint('❌ CRITICAL: Firestore save error: $firestoreError');
        debugPrint('🔍 Error type: ${firestoreError.runtimeType}');
        debugPrint('🔍 Error details: ${firestoreError.toString()}');
        debugPrint('🔍 Post ID: ${post.id}');
        debugPrint('🔍 Author: ${post.authorName}');
        debugPrint('🔍 Content: ${post.content}');
        debugPrint('🔍 Images: ${post.images.length}');
        debugPrint('🔍 Videos: ${post.videos.length}');
        debugPrint('🔍 Firebase Project: ${_firestore.app.options.projectId}');

        // طباعة تفاصيل الخطأ
        if (firestoreError.toString().contains('permission')) {
          debugPrint('🚨 PERMISSION DENIED - Check Firebase Rules!');
        } else if (firestoreError.toString().contains('network')) {
          debugPrint('🌐 NETWORK ERROR - Check internet connection!');
        } else if (firestoreError.toString().contains('quota')) {
          debugPrint('💰 QUOTA EXCEEDED - Check Firebase billing!');
        }

        // لا نفشل العملية، نحفظ محلي<|im_start|> فقط
        debugPrint('⚠️ Continuing with local save only...');
      }

      _posts.insert(0, post);
      await _savePosts();
      _updateStreams();

      debugPrint('✅ Advanced post created: ${post.id}');
      debugPrint('   Type: $type');
      debugPrint('   Videos: ${videos.length}');
      debugPrint('   Music: $musicTitle');
      debugPrint('   Location: $location');
      debugPrint('   Feeling: $feeling');
      debugPrint('   Tagged: ${taggedUsers.length} users');
      debugPrint('   Live Stream: $isLiveStream');

      return true;
    } catch (e) {
      debugPrint('❌ Error creating advanced post: $e');
      return false;
    }
  }

  /// تحديث منشور
  Future<bool> updatePost({
    required String postId,
    String? content,
    List<String>? images,
    PostPrivacy? privacy,
    String? location,
    List<String>? tags,
  }) async {
    try {
      final index = _posts.indexWhere((p) => p.id == postId);
      if (index == -1) return false;

      _posts[index] = _posts[index].copyWith(
        content: content,
        images: images,
        privacy: privacy,
        location: location,
        tags: tags,
        updatedAt: DateTime.now(),
        isEdited: true,
      );

      await _savePosts();
      _updateStreams();

      debugPrint('📝 Post updated successfully: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating post: $e');
      return false;
    }
  }

  /// إعادة نشر منشور
  Future<bool> repostPost({
    required PostModel originalPost,
    required String reposterName,
    required String reposterId,
  }) async {
    try {
      final repost = PostModel(
        id: 'repost_${DateTime.now().millisecondsSinceEpoch}',
        authorId: reposterId,
        authorName: reposterName,
        authorAvatar: '',
        content: 'Reposted from ${originalPost.authorName}',
        images: originalPost.images,
        videos: originalPost.videos,
        musicUrl: originalPost.musicUrl,
        musicTitle: originalPost.musicTitle,
        location: originalPost.location,
        feeling: originalPost.feeling,
        backgroundColor: originalPost.backgroundColor,
        taggedUsers: originalPost.taggedUsers,
        isLiveStream: false, // إعادة النشر لا تكون بث مباشر
        createdAt: DateTime.now(),
        type: originalPost.type,
        privacy: PostPrivacy.public,
        originalPostId: originalPost.id, // ربط بالمنشور الأصلي
        isRepost: true,
      );

      _posts.insert(0, repost);
      await _savePosts();
      _updateStreams();

      debugPrint('✅ Post reposted: ${repost.id}');
      debugPrint('   Original post: ${originalPost.id}');
      debugPrint('   Reposter: $reposterName');

      return true;
    } catch (e) {
      debugPrint('❌ Error reposting post: $e');
      return false;
    }
  }

  /// حذف منشور
  Future<bool> deletePost(String postId) async {
    try {
      final index = _posts.indexWhere((p) => p.id == postId);
      if (index == -1) return false;

      _posts[index] = _posts[index].copyWith(
        isDeleted: true,
        updatedAt: DateTime.now(),
      );

      await _savePosts();
      _updateStreams();

      debugPrint('🗑️ Post deleted successfully: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting post: $e');
      return false;
    }
  }

  /// إضافة تفاعل على منشور مع حفظ في Firebase
  Future<bool> addPostReaction({
    required String postId,
    required String emoji,
    required String userId,
  }) async {
    try {
      debugPrint('👍 Adding reaction: $emoji to post $postId by user $userId');

      final index = _posts.indexWhere((p) => p.id == postId);
      if (index == -1) {
        debugPrint('❌ Post not found: $postId');
        return false;
      }

      final post = _posts[index];
      final reactions = Map<String, int>.from(post.reactions);

      // إزالة التفاعلات السابقة للمستخدم
      final userReactionKey = '${userId}_reaction';
      final prefs = await SharedPreferences.getInstance();
      final previousReaction = prefs.getString('${postId}_$userReactionKey');

      if (previousReaction != null && reactions.containsKey(previousReaction)) {
        reactions[previousReaction] = (reactions[previousReaction]! - 1).clamp(0, double.infinity).toInt();
        if (reactions[previousReaction] == 0) {
          reactions.remove(previousReaction);
        }
      }

      // إضافة التفاعل الجديد
      if (previousReaction != emoji) {
        reactions[emoji] = (reactions[emoji] ?? 0) + 1;
        await prefs.setString('${postId}_$userReactionKey', emoji);
      } else {
        // إزالة التفاعل إذا كان نفس التفاعل السابق
        await prefs.remove('${postId}_$userReactionKey');
      }

      _posts[index] = post.copyWith(reactions: reactions);

      // حفظ التفاعلات في Firebase
      try {
        debugPrint('🔄 Saving reaction to Firebase: $emoji for post $postId');
        debugPrint('📊 Reactions data: $reactions');
        debugPrint('🔗 Firebase project: ${_firestore.app.options.projectId}');

        // محاولة الحفظ مع timeout
        await _firestore.collection('posts').doc(postId).update({
          'reactions': reactions,
          'updatedAt': Timestamp.now(),
        }).timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            throw Exception('Firebase reaction save timeout after 10 seconds');
          },
        );

        debugPrint('✅ Reaction saved to Firebase successfully: $emoji for post $postId');

        // التحقق من الحفظ مع timeout
        final updatedDoc = await _firestore.collection('posts').doc(postId).get().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            throw Exception('Firebase reaction read timeout after 5 seconds');
          },
        );

        if (updatedDoc.exists) {
          final savedReactions = updatedDoc.data()?['reactions'];
          debugPrint('✅ Verified reactions in Firebase: $savedReactions');
          debugPrint('🎯 SUCCESS: Reaction is permanently saved in Firebase!');
        } else {
          debugPrint('❌ Post not found when verifying reaction!');
          throw Exception('Post not found when verifying reaction');
        }
      } catch (firestoreError) {
        debugPrint('❌ CRITICAL ERROR saving reaction to Firebase: $firestoreError');
        debugPrint('🔍 Error type: ${firestoreError.runtimeType}');
        debugPrint('🔍 Post ID: $postId');
        debugPrint('🔍 User ID: $userId');
        debugPrint('🔍 Emoji: $emoji');
        // لا نفشل العملية إذا فشل Firebase، نحتفظ بالتفاعل محلياً
      }

      await _savePosts();
      _updateStreams();

      debugPrint('✅ Reaction added locally: $emoji to post $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding reaction: $e');
      return false;
    }
  }

  /// إضافة تعليق أو رد
  Future<bool> addComment({
    required String postId,
    required String authorId,
    required String authorName,
    required String content,
    String? authorAvatar,
    String? parentCommentId, // للردود على التعليقات
  }) async {
    try {
      debugPrint('🔥 SERVICE DEBUG: Starting addComment');
      debugPrint('🔥 SERVICE DEBUG: postId: $postId');
      debugPrint('🔥 SERVICE DEBUG: authorName: $authorName');
      debugPrint('🔥 SERVICE DEBUG: content: $content');

      final comment = CommentModel(
        id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
        postId: postId,
        authorId: authorId,
        authorName: authorName,
        authorAvatar: authorAvatar ?? '',
        content: content,
        createdAt: DateTime.now(),
        parentCommentId: parentCommentId,
      );

      debugPrint('🔥 SERVICE DEBUG: Comment created with ID: ${comment.id}');

      _comments.add(comment);
      debugPrint('🔥 SERVICE DEBUG: Comment added to list. Total comments: ${_comments.length}');

      // تحديث عدد التعليقات في المنشور
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      debugPrint('🔥 SERVICE DEBUG: Post index: $postIndex');
      if (postIndex != -1) {
        _posts[postIndex] = _posts[postIndex].copyWith(
          commentsCount: _posts[postIndex].commentsCount + 1,
        );
        debugPrint('🔥 SERVICE DEBUG: Post comments count updated to: ${_posts[postIndex].commentsCount}');
      }

      debugPrint('🔥 SERVICE DEBUG: Saving comments...');
      await _saveComments();
      debugPrint('🔥 SERVICE DEBUG: Saving posts...');
      await _savePosts();
      debugPrint('🔥 SERVICE DEBUG: Updating streams...');
      _updateStreams();

      debugPrint('💬 Comment added to post: $postId');
      debugPrint('🔥 SERVICE DEBUG: addComment completed successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding comment: $e');
      debugPrint('🔥 SERVICE DEBUG: addComment failed with error: $e');
      return false;
    }
  }

  /// إضافة تعليق مع وسائط (صور وفيديوهات)
  Future<bool> addCommentWithMedia({
    required String postId,
    required String authorId,
    required String authorName,
    required String content,
    String? authorAvatar,
    String? parentCommentId,
    List<XFile>? images,
    List<XFile>? videos,
  }) async {
    try {
      debugPrint('📸 Adding comment with media...');

      // رفع الصور والفيديوهات إلى Firebase Storage
      List<String> imageUrls = [];
      List<String> videoUrls = [];

      if (images != null && images.isNotEmpty) {
        for (final image in images) {
          final url = await _uploadFileToStorage(image, 'comment_images');
          if (url != null) {
            imageUrls.add(url);
          }
        }
      }

      if (videos != null && videos.isNotEmpty) {
        for (final video in videos) {
          final url = await _uploadFileToStorage(video, 'comment_videos');
          if (url != null) {
            videoUrls.add(url);
          }
        }
      }

      final comment = CommentModel(
        id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
        postId: postId,
        authorId: authorId,
        authorName: authorName,
        authorAvatar: authorAvatar ?? '',
        content: content,
        createdAt: DateTime.now(),
        parentCommentId: parentCommentId,
        images: imageUrls,
        videos: videoUrls,
      );

      _comments.add(comment);

      // تحديث عدد التعليقات في المنشور
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        _posts[postIndex] = _posts[postIndex].copyWith(
          commentsCount: _posts[postIndex].commentsCount + 1,
        );
      }

      await _saveComments();
      await _savePosts();
      _updateStreams();

      debugPrint('✅ Comment with media added successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding comment with media: $e');
      return false;
    }
  }

  /// إضافة تفاعل لتعليق
  Future<bool> addCommentReaction({
    required String commentId,
    required String emoji,
    required String userId,
  }) async {
    try {
      final commentIndex = _comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) return false;

      final comment = _comments[commentIndex];
      final reactions = Map<String, String>.from(comment.reactions);

      // إذا كان المستخدم قد تفاعل بنفس الإيموجي، إزالة التفاعل
      if (reactions[userId] == emoji) {
        reactions.remove(userId);
      } else {
        // إضافة أو تحديث التفاعل
        reactions[userId] = emoji;
      }

      _comments[commentIndex] = CommentModel(
        id: comment.id,
        postId: comment.postId,
        authorId: comment.authorId,
        authorName: comment.authorName,
        authorAvatar: comment.authorAvatar,
        content: comment.content,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
        isEdited: comment.isEdited,
        isDeleted: comment.isDeleted,
        reactions: reactions,
        replies: comment.replies,
        likesCount: comment.likesCount,
        reactionsCount: reactions.length,
      );

      await _saveComments();
      notifyListeners();

      debugPrint('✅ Comment reaction $emoji ${reactions.containsKey(userId) ? 'added' : 'removed'} for comment $commentId');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding comment reaction: $e');
      return false;
    }
  }

  /// حذف تعليق
  Future<bool> deleteComment(String commentId) async {
    try {
      final commentIndex = _comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) return false;

      final comment = _comments[commentIndex];

      // تحديث التعليق كمحذوف بدلاً من حذفه نهائياً
      _comments[commentIndex] = CommentModel(
        id: comment.id,
        postId: comment.postId,
        authorId: comment.authorId,
        authorName: comment.authorName,
        authorAvatar: comment.authorAvatar,
        content: '[This comment has been deleted]',
        createdAt: comment.createdAt,
        updatedAt: DateTime.now(),
        isEdited: comment.isEdited,
        isDeleted: true,
        reactions: comment.reactions,
        replies: comment.replies,
        likesCount: comment.likesCount,
        reactionsCount: comment.reactionsCount,
        parentCommentId: comment.parentCommentId,
      );

      // تحديث عدد التعليقات في المنشور
      final postIndex = _posts.indexWhere((p) => p.id == comment.postId);
      if (postIndex != -1) {
        _posts[postIndex] = _posts[postIndex].copyWith(
          commentsCount: _posts[postIndex].commentsCount - 1,
        );
      }

      await _saveComments();
      await _savePosts();
      _updateStreams();

      debugPrint('🗑️ Comment deleted: $commentId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting comment: $e');
      return false;
    }
  }

  /// تعديل تعليق
  Future<bool> editComment(String commentId, String newContent) async {
    try {
      final commentIndex = _comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) return false;

      final comment = _comments[commentIndex];

      _comments[commentIndex] = CommentModel(
        id: comment.id,
        postId: comment.postId,
        authorId: comment.authorId,
        authorName: comment.authorName,
        authorAvatar: comment.authorAvatar,
        content: newContent,
        createdAt: comment.createdAt,
        updatedAt: DateTime.now(),
        isEdited: true,
        isDeleted: comment.isDeleted,
        reactions: comment.reactions,
        replies: comment.replies,
        likesCount: comment.likesCount,
        reactionsCount: comment.reactionsCount,
        parentCommentId: comment.parentCommentId,
      );

      await _saveComments();
      _updateStreams();

      debugPrint('✏️ Comment edited: $commentId');
      return true;
    } catch (e) {
      debugPrint('❌ Error editing comment: $e');
      return false;
    }
  }

  /// مشاركة منشور
  Future<bool> sharePost({
    required String postId,
    required String userId,
    String? shareContent,
  }) async {
    try {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex == -1) return false;

      _posts[postIndex] = _posts[postIndex].copyWith(
        sharesCount: _posts[postIndex].sharesCount + 1,
      );

      await _savePosts();
      _updateStreams();

      debugPrint('🔄 Post shared: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error sharing post: $e');
      return false;
    }
  }

  /// الحصول على جميع المنشورات
  List<PostModel> getAllPosts() {
    return _posts.where((p) => !p.isDeleted).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// الحصول على منشورات مستخدم معين
  List<PostModel> getUserPosts(String userId) {
    return _posts.where((p) => p.authorId == userId && !p.isDeleted).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// الحصول على تعليقات منشور مع الردود المتداخلة والفرز
  List<CommentModel> getPostComments(String postId, {String sortBy = 'newest'}) {
    // الحصول على التعليقات الرئيسية فقط (ليس الردود)
    var mainComments = _comments
        .where((c) => c.postId == postId && !c.isDeleted && c.parentCommentId == null)
        .toList();

    // فرز التعليقات حسب النوع المطلوب
    switch (sortBy) {
      case 'newest':
        mainComments.sort((a, b) => b.createdAt.compareTo(a.createdAt)); // الأحدث أولاً
        break;
      case 'most_relevant':
        mainComments.sort((a, b) {
          // الأكثر ملائمة: حسب التفاعلات ثم الوقت
          final aScore = a.reactionsCount + a.replies.length;
          final bScore = b.reactionsCount + b.replies.length;
          if (aScore != bScore) {
            return bScore.compareTo(aScore);
          }
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'all_comments':
        mainComments.sort((a, b) => a.createdAt.compareTo(b.createdAt)); // الأقدم أولاً
        break;
    }

    // إضافة الردود المتداخلة لكل تعليق رئيسي
    for (int i = 0; i < mainComments.length; i++) {
      mainComments[i] = _buildCommentWithReplies(mainComments[i]);
    }

    return mainComments;
  }

  /// بناء تعليق مع ردوده المتسلسلة (مثل Facebook)
  CommentModel _buildCommentWithReplies(CommentModel comment) {
    // الحصول على جميع الردود المباشرة لهذا التعليق
    final allReplies = _comments
        .where((c) => c.parentCommentId == comment.id && !c.isDeleted)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt)); // الردود بالترتيب الزمني

    // تحويل الردود إلى قائمة مسطحة (متسلسلة) بدلاً من متداخلة
    final flattenedReplies = <CommentModel>[];

    for (final reply in allReplies) {
      // إضافة الرد الحالي
      flattenedReplies.add(reply);

      // إضافة ردود هذا الرد بشكل متسلسل
      final nestedReplies = _getAllNestedReplies(reply.id);
      flattenedReplies.addAll(nestedReplies);
    }

    return CommentModel(
      id: comment.id,
      postId: comment.postId,
      authorId: comment.authorId,
      authorName: comment.authorName,
      authorAvatar: comment.authorAvatar,
      content: comment.content,
      createdAt: comment.createdAt,
      updatedAt: comment.updatedAt,
      isEdited: comment.isEdited,
      isDeleted: comment.isDeleted,
      reactions: comment.reactions,
      replies: flattenedReplies, // الردود المسطحة المتسلسلة
      likesCount: comment.likesCount,
      reactionsCount: comment.reactionsCount,
      parentCommentId: comment.parentCommentId,
    );
  }

  /// الحصول على جميع الردود المتداخلة بشكل متسلسل
  List<CommentModel> _getAllNestedReplies(String parentId) {
    final replies = _comments
        .where((c) => c.parentCommentId == parentId && !c.isDeleted)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    final allReplies = <CommentModel>[];

    for (final reply in replies) {
      allReplies.add(reply);
      // إضافة ردود هذا الرد بشكل متسلسل
      allReplies.addAll(_getAllNestedReplies(reply.id));
    }

    return allReplies;
  }

  /// الحصول على منشور بالمعرف
  PostModel? getPostById(String postId) {
    try {
      return _posts.firstWhere((p) => p.id == postId && !p.isDeleted);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على تعليق بالمعرف
  CommentModel? getCommentById(String commentId) {
    try {
      return _comments.firstWhere((c) => c.id == commentId && !c.isDeleted);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على التفاعلات المتاحة
  List<Map<String, dynamic>> get availableReactions => _facebookReactions;

  /// الحصول على تفاعل المستخدم على منشور من Firebase
  Future<String?> getUserReaction(String postId, String userId) async {
    try {
      // أولاً، جرب من SharedPreferences للسرعة
      final prefs = await SharedPreferences.getInstance();
      final localReaction = prefs.getString('${postId}_${userId}_reaction');

      if (localReaction != null) {
        debugPrint('👍 Found local reaction: $localReaction for post $postId');
        return localReaction;
      }

      // إذا لم توجد محلياً، جرب من Firebase
      try {
        final postDoc = await _firestore.collection('posts').doc(postId).get();
        if (postDoc.exists) {
          final postData = postDoc.data();
          final reactions = postData?['reactions'] as Map<String, dynamic>?;

          if (reactions != null) {
            // البحث عن تفاعل المستخدم في Firebase
            // هذا يتطلب تحسين هيكل البيانات في المستقبل
            debugPrint('👍 Post reactions from Firebase: $reactions');
          }
        }
      } catch (firestoreError) {
        debugPrint('❌ Error getting reaction from Firebase: $firestoreError');
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting user reaction: $e');
      return null;
    }
  }

  /// الحصول على تفاصيل التفاعلات للمنشور
  List<Map<String, dynamic>> getPostReactionDetails(String postId) {
    final post = _posts.firstWhere(
      (p) => p.id == postId,
      orElse: () => PostModel(
        id: '',
        authorId: '',
        authorName: '',
        authorAvatar: '',
        content: '',
        createdAt: DateTime.now(),
        images: [],
        videos: [],
        reactions: {},
      ),
    );

    if (post.id.isEmpty) return [];

    final List<Map<String, dynamic>> reactionDetails = [];

    // تحويل التفاعلات إلى قائمة مفصلة
    post.reactions.forEach((emoji, reactionCount) {
      // إنشاء تفاعلات تجريبية للعرض
      for (int i = 0; i < reactionCount; i++) {
        final userName = _getDemoUserName(i, emoji);
        final userId = 'user_${i}_${emoji.hashCode}';

        reactionDetails.add({
          'userId': userId,
          'userName': userName,
          'userPhone': '+212${600000000 + i}',
          'emoji': emoji,
          'timestamp': DateTime.now().subtract(Duration(minutes: i * 5)),
        });
      }
    });

    // ترتيب حسب الوقت (الأحدث أولاً)
    reactionDetails.sort((a, b) {
      final timeA = a['timestamp'] as DateTime;
      final timeB = b['timestamp'] as DateTime;
      return timeB.compareTo(timeA);
    });

    return reactionDetails;
  }

  /// الحصول على اسم مستخدم تجريبي
  String _getDemoUserName(int index, String emoji) {
    final names = [
      'Ahmed Hassan', 'Fatima Zahra', 'Omar Benali', 'Aicha Alami',
      'Youssef Tazi', 'Khadija Idrissi', 'Mohamed Berrada', 'Zineb Fassi',
      'Karim Benjelloun', 'Salma Chraibi', 'Rachid Alaoui', 'Nadia Benkirane',
      'Hamza Kettani', 'Laila Benali', 'Abdellatif Tazi', 'Samira Idrissi'
    ];

    // اختيار اسم بناءً على الفهرس والتفاعل
    final nameIndex = (index + emoji.hashCode) % names.length;
    return names[nameIndex];
  }

  /// إنشاء إعادة نشر (Repost)
  Future<bool> createRepost({
    required PostModel originalPost,
    required String reposterUserId,
    required String reposterName,
    String? comment,
  }) async {
    try {
      // إنشاء منشور إعادة نشر جديد
      final repost = PostModel(
        id: 'repost_${DateTime.now().millisecondsSinceEpoch}',
        authorId: reposterUserId,
        authorName: reposterName,
        authorAvatar: '',
        content: comment ?? '', // التعليق الشخصي (إن وجد)
        createdAt: DateTime.now(),
        images: [],
        videos: [],
        reactions: {},
        isRepost: true,
        originalPost: originalPost, // المنشور الأصلي
        repostComment: comment, // التعليق على إعادة النشر
      );

      // إضافة إلى القائمة
      _posts.insert(0, repost);
      notifyListeners();

      // حفظ في Firebase
      try {
        await _firestore.collection('posts').doc(repost.id).set(repost.toMap());
        debugPrint('✅ Repost saved to Firebase: ${repost.id}');
      } catch (e) {
        debugPrint('❌ Error saving repost to Firebase: $e');
      }

      // حفظ محلياً
      await _savePosts();

      // تحديث إحصائيات المنشور الأصلي
      await _incrementRepostCount(originalPost.id);

      debugPrint('✅ Repost created successfully: ${repost.id}');
      return true;
    } catch (e) {
      debugPrint('❌ Error creating repost: $e');
      return false;
    }
  }

  /// زيادة عداد إعادة النشر للمنشور الأصلي
  Future<void> _incrementRepostCount(String originalPostId) async {
    try {
      final postIndex = _posts.indexWhere((p) => p.id == originalPostId);
      if (postIndex != -1) {
        final post = _posts[postIndex];

        // زيادة عداد إعادة النشر
        final updatedPost = PostModel(
          id: post.id,
          authorId: post.authorId,
          authorName: post.authorName,
          authorAvatar: post.authorAvatar,
          content: post.content,
          createdAt: post.createdAt,
          images: post.images,
          videos: post.videos,
          reactions: post.reactions,
          repostCount: (post.repostCount ?? 0) + 1,
          isRepost: post.isRepost,
          originalPost: post.originalPost,
          repostComment: post.repostComment,
        );

        _posts[postIndex] = updatedPost;
        notifyListeners();

        // حفظ في Firebase
        try {
          await _firestore.collection('posts').doc(updatedPost.id).set(updatedPost.toMap());
          debugPrint('✅ Updated post saved to Firebase: ${updatedPost.id}');
        } catch (e) {
          debugPrint('❌ Error saving updated post to Firebase: $e');
        }

        // حفظ محلياً
        await _savePosts();
      }
    } catch (e) {
      debugPrint('❌ Error incrementing repost count: $e');
    }
  }

  /// التحقق من إعادة نشر المستخدم للمنشور
  bool hasUserReposted(String postId, String userId) {
    return _posts.any((post) =>
      post.isRepost == true &&
      post.originalPost?.id == postId &&
      post.authorId == userId
    );
  }

  /// الحصول على عدد إعادات النشر للمنشور
  int getRepostCount(String postId) {
    final post = _posts.firstWhere(
      (p) => p.id == postId,
      orElse: () => PostModel(
        id: '',
        authorId: '',
        authorName: '',
        authorAvatar: '',
        content: '',
        createdAt: DateTime.now(),
        images: [],
        videos: [],
        reactions: {},
      ),
    );

    return post.repostCount ?? 0;
  }



  /// البحث في المنشورات
  List<PostModel> searchPosts(String query) {
    if (query.trim().isEmpty) return getAllPosts();

    final lowerQuery = query.toLowerCase();
    return _posts.where((post) {
      return !post.isDeleted &&
             (post.content.toLowerCase().contains(lowerQuery) ||
              post.authorName.toLowerCase().contains(lowerQuery) ||
              post.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)));
    }).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// إعادة تحميل المنشورات من Firebase
  Future<void> refreshFromFirebase() async {
    debugPrint('🔄 Refreshing posts from Firebase...');
    _posts.clear();
    await _loadPostsFromFirestore();
    _updateStreams();
    debugPrint('✅ Refresh completed. Total posts: ${_posts.length}');
  }

  /// تحديث البيانات المباشرة
  void _updateStreams() {
    _postsController.add(List.from(_posts));
    _commentsController.add(List.from(_comments));
    notifyListeners();
  }

  /// تحميل المنشورات
  Future<void> _loadPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsJson = prefs.getString('social_posts');

      if (postsJson != null) {
        final postsList = json.decode(postsJson) as List;
        _posts.clear();
        for (final postMap in postsList) {
          _posts.add(PostModel.fromMap(postMap));
        }
      }

      debugPrint('📱 Loaded ${_posts.length} posts');
    } catch (e) {
      debugPrint('❌ Error loading posts: $e');
    }
  }

  /// حفظ المنشورات
  Future<void> _savePosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsList = _posts.map((p) => p.toMap()).toList();
      final postsJson = json.encode(postsList);
      await prefs.setString('social_posts', postsJson);
    } catch (e) {
      debugPrint('❌ Error saving posts: $e');
    }
  }

  /// تحميل التعليقات
  Future<void> _loadComments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commentsJson = prefs.getString('social_comments');

      if (commentsJson != null) {
        final commentsList = json.decode(commentsJson) as List;
        _comments.clear();
        for (final commentMap in commentsList) {
          _comments.add(CommentModel.fromMap(commentMap));
        }
      }

      debugPrint('💬 Loaded ${_comments.length} comments');
    } catch (e) {
      debugPrint('❌ Error loading comments: $e');
    }
  }

  /// حفظ التعليقات
  Future<void> _saveComments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commentsList = _comments.map((c) => c.toMap()).toList();
      final commentsJson = json.encode(commentsList);
      await prefs.setString('social_comments', commentsJson);
    } catch (e) {
      debugPrint('❌ Error saving comments: $e');
    }
  }

  /// إحصائيات الشبكة الاجتماعية
  Map<String, dynamic> getSocialStats() {
    final stats = <String, dynamic>{};

    stats['totalPosts'] = _posts.where((p) => !p.isDeleted).length;
    stats['totalComments'] = _comments.where((c) => !c.isDeleted).length;
    stats['totalReactions'] = _posts.fold(0, (total, post) => total + post.totalReactions);
    stats['totalShares'] = _posts.fold(0, (total, post) => total + post.sharesCount);

    return stats;
  }

  // Getters
  Stream<List<PostModel>> get postsStream => _postsController.stream;
  Stream<List<CommentModel>> get commentsStream => _commentsController.stream;
  List<PostModel> get allPosts => List.from(_posts);
  List<CommentModel> get allComments => List.from(_comments);
  int get totalPosts => _posts.where((p) => !p.isDeleted).length;
  int get totalComments => _comments.where((c) => !c.isDeleted).length;

  /// تحديث عداد المشاركات
  Future<bool> incrementShareCount(String postId) async {
    try {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final updatedPost = _posts[postIndex].copyWith(
          sharesCount: _posts[postIndex].sharesCount + 1,
        );
        _posts[postIndex] = updatedPost;

        // تحديث في Firebase
        try {
          await _firestore.collection('posts').doc(postId).update({
            'sharesCount': updatedPost.sharesCount,
          });
        } catch (e) {
          debugPrint('⚠️ Firebase update failed for share count: $e');
        }

        await _savePosts();
        _updateStreams();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ Error incrementing share count: $e');
      return false;
    }
  }

  /// تحديث عداد إعادة النشر
  Future<bool> incrementRepostCount(String postId) async {
    try {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        final updatedPost = _posts[postIndex].copyWith(
          repostsCount: _posts[postIndex].repostsCount + 1,
        );
        _posts[postIndex] = updatedPost;

        // تحديث في Firebase
        try {
          await _firestore.collection('posts').doc(postId).update({
            'repostsCount': updatedPost.repostsCount,
          });
        } catch (e) {
          debugPrint('⚠️ Firebase update failed for repost count: $e');
        }

        await _savePosts();
        _updateStreams();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ Error incrementing repost count: $e');
      return false;
    }
  }

  /// رفع ملف إلى Firebase Storage
  Future<String?> _uploadFileToStorage(XFile file, String folder) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.name}';
      final ref = FirebaseStorage.instance.ref().child('$folder/$fileName');

      final uploadTask = ref.putFile(File(file.path));
      final snapshot = await uploadTask;

      if (snapshot.state == TaskState.success) {
        final downloadUrl = await ref.getDownloadURL();
        debugPrint('✅ File uploaded successfully: $downloadUrl');
        return downloadUrl;
      } else {
        debugPrint('❌ Upload failed with state: ${snapshot.state}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error uploading file: $e');
      return null;
    }
  }

  @override
  void dispose() {
    _postsController.close();
    _commentsController.close();
    super.dispose();
  }
}

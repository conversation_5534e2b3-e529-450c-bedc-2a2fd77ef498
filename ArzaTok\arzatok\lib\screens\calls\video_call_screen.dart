import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/user_model.dart';

/// شاشة مكالمة الفيديو الحقيقية
class VideoCallScreen extends StatefulWidget {
  final UserModel otherUser;

  const VideoCallScreen({
    Key? key,
    required this.otherUser,
  }) : super(key: key);

  @override
  State<VideoCallScreen> createState() => _VideoCallScreenState();
}

class _VideoCallScreenState extends State<VideoCallScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  bool _isMuted = false;
  bool _isCameraOn = true;
  bool _isConnected = false;
  bool _isControlsVisible = true;
  String _callStatus = 'Connecting...';
  int _callDuration = 0;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);
    
    _fadeController.forward();
    
    // محاكاة الاتصال
    _simulateCall();
    
    // إخفاء أزرار التحكم تلقائياً
    _autoHideControls();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  /// محاكاة عملية الاتصال
  void _simulateCall() async {
    await Future.delayed(const Duration(seconds: 3));
    
    if (mounted) {
      setState(() {
        _isConnected = true;
        _callStatus = 'Connected';
      });
      
      _startCallTimer();
    }
  }

  /// بدء عداد وقت المكالمة
  void _startCallTimer() {
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted && _isConnected) {
        setState(() {
          _callDuration++;
        });
        return true;
      }
      return false;
    });
  }

  /// إخفاء أزرار التحكم تلقائياً
  void _autoHideControls() {
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && _isConnected) {
        setState(() {
          _isControlsVisible = false;
        });
      }
    });
  }

  /// إظهار أزرار التحكم عند اللمس
  void _showControls() {
    setState(() {
      _isControlsVisible = true;
    });
    
    // إخفاء مرة أخرى بعد 3 ثواني
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isControlsVisible = false;
        });
      }
    });
  }

  /// تنسيق وقت المكالمة
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _showControls,
        child: Stack(
          children: [
            // فيديو المتصل (محاكاة)
            _buildRemoteVideo(),
            
            // فيديو المستخدم الحالي (صغير)
            _buildLocalVideo(),
            
            // أزرار التحكم
            if (_isControlsVisible) _buildControls(),
            
            // معلومات المكالمة
            if (!_isConnected || _isControlsVisible) _buildCallInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء فيديو المتصل
  Widget _buildRemoteVideo() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.withOpacity(0.3),
              Colors.purple.withOpacity(0.5),
              Colors.black,
            ],
          ),
        ),
        child: _isConnected
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // محاكاة فيديو المتصل
                    Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                      ),
                      child: CircleAvatar(
                        radius: 97,
                        backgroundColor: const Color(0xFFD32F2F),
                        backgroundImage: widget.otherUser.profileImageUrl != null
                            ? NetworkImage(widget.otherUser.profileImageUrl!)
                            : null,
                        child: widget.otherUser.profileImageUrl == null
                            ? Text(
                                widget.otherUser.name.isNotEmpty
                                    ? widget.otherUser.name[0].toUpperCase()
                                    : '?',
                                style: const TextStyle(
                                  fontSize: 60,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '📹 Video simulation - ${widget.otherUser.name}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            : Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      _callStatus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  /// بناء فيديو المستخدم الحالي
  Widget _buildLocalVideo() {
    return Positioned(
      top: 60,
      right: 20,
      child: Container(
        width: 120,
        height: 160,
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Stack(
            children: [
              // محاكاة فيديو المستخدم
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.green.withOpacity(0.3),
                      Colors.blue.withOpacity(0.5),
                    ],
                  ),
                ),
                child: Center(
                  child: _isCameraOn
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 40,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'You',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        )
                      : Icon(
                          Icons.videocam_off,
                          color: Colors.white,
                          size: 40,
                        ),
                ),
              ),
              
              // زر تبديل الكاميرا
              Positioned(
                bottom: 8,
                right: 8,
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('🔄 Camera switched'),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  },
                  child: Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.flip_camera_ios,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء معلومات المكالمة
  Widget _buildCallInfo() {
    return Positioned(
      top: 60,
      left: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.videocam,
              color: _isConnected ? Colors.green : Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              _isConnected ? _formatDuration(_callDuration) : _callStatus,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControls() {
    return Positioned(
      bottom: 60,
      left: 0,
      right: 0,
      child: AnimatedOpacity(
        opacity: _isControlsVisible ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // زر كتم الصوت
              _buildControlButton(
                icon: _isMuted ? Icons.mic_off : Icons.mic,
                color: _isMuted ? Colors.red : Colors.grey[700]!,
                onPressed: () {
                  setState(() {
                    _isMuted = !_isMuted;
                  });
                  
                  HapticFeedback.lightImpact();
                  
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_isMuted ? '🔇 Microphone muted' : '🎤 Microphone unmuted'),
                      duration: const Duration(seconds: 1),
                    ),
                  );
                },
              ),
              
              // زر إنهاء المكالمة
              _buildControlButton(
                icon: Icons.call_end,
                color: Colors.red,
                size: 80,
                onPressed: () {
                  HapticFeedback.heavyImpact();
                  Navigator.of(context).pop();
                  
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('📹 Video call with ${widget.otherUser.name} ended'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              ),
              
              // زر الكاميرا
              _buildControlButton(
                icon: _isCameraOn ? Icons.videocam : Icons.videocam_off,
                color: _isCameraOn ? Colors.grey[700]! : Colors.red,
                onPressed: () {
                  setState(() {
                    _isCameraOn = !_isCameraOn;
                  });
                  
                  HapticFeedback.lightImpact();
                  
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_isCameraOn ? '📷 Camera on' : '📷 Camera off'),
                      duration: const Duration(seconds: 1),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر تحكم
  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
    double size = 70,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Icon(
            icon,
            color: Colors.white,
            size: size * 0.4,
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/privacy_settings_model.dart';
import '../../services/auth_service.dart';

/// شاشة إعدادات خصوصية المحتوى مثل TikTok
class ContentPrivacySettingsScreen extends StatefulWidget {
  const ContentPrivacySettingsScreen({Key? key}) : super(key: key);

  @override
  State<ContentPrivacySettingsScreen> createState() => _ContentPrivacySettingsScreenState();
}

class _ContentPrivacySettingsScreenState extends State<ContentPrivacySettingsScreen> {
  late PrivacySettings _privacySettings;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadPrivacySettings();
  }

  /// تحميل إعدادات الخصوصية
  void _loadPrivacySettings() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;
    
    if (currentUser != null) {
      _privacySettings = currentUser.privacySettings;
    } else {
      _privacySettings = const PrivacySettings();
    }
    
    setState(() {
      _isLoading = false;
    });
  }

  /// حفظ إعدادات الخصوصية
  Future<void> _savePrivacySettings() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.updatePrivacySettings(_privacySettings);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('✅ Privacy settings updated successfully!'),
            backgroundColor: const Color(0xFF42B883),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error updating settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF1C1E21)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Content Privacy',
          style: TextStyle(
            color: Color(0xFF1C1E21),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          // زر الحفظ
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ElevatedButton(
              onPressed: _isSaving ? null : _savePrivacySettings,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD32F2F),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              ),
              child: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Save',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // مقدمة
          _buildIntroCard(),
          
          const SizedBox(height: 16),
          
          // إعدادات خصوصية المحتوى
          _buildContentPrivacySection(),
        ],
      ),
    );
  }

  /// بناء بطاقة المقدمة
  Widget _buildIntroCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFD32F2F).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.security,
                  color: Color(0xFFD32F2F),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Content Privacy Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Control who can see different types of content you share. These settings help you manage your privacy and decide what\'s visible to others.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم إعدادات خصوصية المحتوى
  Widget _buildContentPrivacySection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان القسم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFFF0F2F5),
              borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.visibility,
                  color: Color(0xFF65676B),
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Who can see your content',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ],
            ),
          ),
          
          // إعدادات كل نوع محتوى
          _buildContentPrivacyItem(
            icon: Icons.photo,
            title: 'Photos',
            subtitle: 'Photos you\'ve shared',
            currentValue: _privacySettings.photosVisibility,
            onChanged: (value) {
              setState(() {
                _privacySettings = _privacySettings.copyWith(photosVisibility: value);
              });
            },
          ),
          
          _buildDivider(),
          
          _buildContentPrivacyItem(
            icon: Icons.videocam,
            title: 'Videos',
            subtitle: 'Videos you\'ve posted',
            currentValue: _privacySettings.videosVisibility,
            onChanged: (value) {
              setState(() {
                _privacySettings = _privacySettings.copyWith(videosVisibility: value);
              });
            },
          ),
          
          _buildDivider(),
          
          _buildContentPrivacyItem(
            icon: Icons.link,
            title: 'Links',
            subtitle: 'Links you\'ve shared',
            currentValue: _privacySettings.linksVisibility,
            onChanged: (value) {
              setState(() {
                _privacySettings = _privacySettings.copyWith(linksVisibility: value);
              });
            },
          ),
          
          _buildDivider(),
          
          _buildContentPrivacyItem(
            icon: Icons.text_fields,
            title: 'Text Posts',
            subtitle: 'Text posts you\'ve written',
            currentValue: _privacySettings.textPostsVisibility,
            onChanged: (value) {
              setState(() {
                _privacySettings = _privacySettings.copyWith(textPostsVisibility: value);
              });
            },
          ),
          
          _buildDivider(),
          
          _buildContentPrivacyItem(
            icon: Icons.share,
            title: 'Shared Posts',
            subtitle: 'Posts you\'ve reposted',
            currentValue: _privacySettings.sharedPostsVisibility,
            onChanged: (value) {
              setState(() {
                _privacySettings = _privacySettings.copyWith(sharedPostsVisibility: value);
              });
            },
          ),
          
          _buildDivider(),
          
          _buildContentPrivacyItem(
            icon: Icons.live_tv,
            title: 'Live Streams',
            subtitle: 'Live streams you\'ve hosted',
            currentValue: _privacySettings.liveStreamsVisibility,
            onChanged: (value) {
              setState(() {
                _privacySettings = _privacySettings.copyWith(liveStreamsVisibility: value);
              });
            },
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إعدادات خصوصية المحتوى
  Widget _buildContentPrivacyItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required ContentVisibility currentValue,
    required Function(ContentVisibility) onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFD32F2F).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: const Color(0xFFD32F2F),
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1C1E21),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: GestureDetector(
        onTap: () => _showVisibilityOptions(currentValue, onChanged),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFFF0F2F5),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFFE4E6EA),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                currentValue.displayName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1C1E21),
                ),
              ),
              const SizedBox(width: 4),
              const Icon(
                Icons.keyboard_arrow_down,
                color: Color(0xFF65676B),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء فاصل
  Widget _buildDivider() {
    return Divider(
      color: Colors.grey[200],
      height: 1,
      indent: 16,
      endIndent: 16,
    );
  }

  /// عرض خيارات الرؤية
  void _showVisibilityOptions(
    ContentVisibility currentValue,
    Function(ContentVisibility) onChanged,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                'Who can see this content?',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1C1E21),
                ),
              ),
            ),

            // خيارات الرؤية
            ...ContentVisibility.values.map((visibility) => ListTile(
              leading: Radio<ContentVisibility>(
                value: visibility,
                groupValue: currentValue,
                onChanged: (value) {
                  if (value != null) {
                    onChanged(value);
                    Navigator.of(context).pop();
                  }
                },
                activeColor: const Color(0xFFD32F2F),
              ),
              title: Text(
                visibility.displayName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1C1E21),
                ),
              ),
              subtitle: Text(
                visibility.description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              onTap: () {
                onChanged(visibility);
                Navigator.of(context).pop();
              },
            )),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

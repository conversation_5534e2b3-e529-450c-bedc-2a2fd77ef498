import 'package:flutter/material.dart';
import 'dart:math' as math;

/// ويدجت التأثيرات البصرية المتقدمة للتفاعلات
class ReactionEffectsWidget extends StatefulWidget {
  final String emoji;
  final Color color;
  final VoidCallback? onComplete;

  const ReactionEffectsWidget({
    super.key,
    required this.emoji,
    required this.color,
    this.onComplete,
  });

  @override
  State<ReactionEffectsWidget> createState() => _ReactionEffectsWidgetState();
}

class _ReactionEffectsWidgetState extends State<ReactionEffectsWidget>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particlesController;
  late AnimationController _pulseController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _pulseAnimation;
  
  List<Particle> _particles = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateParticles();
    _startAnimations();
  }

  void _initializeAnimations() {
    // التحكم الرئيسي للتأثير
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // التحكم في الجسيمات
    _particlesController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // التحكم في النبض
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // تأثير التكبير والتصغير
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    // تأثير الدوران
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeInOut),
    ));

    // تأثير الشفافية
    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));

    // تأثير النبض
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _generateParticles() {
    final random = math.Random();
    _particles = List.generate(15, (index) {
      return Particle(
        x: 0.0,
        y: 0.0,
        vx: (random.nextDouble() - 0.5) * 4,
        vy: (random.nextDouble() - 0.5) * 4,
        size: random.nextDouble() * 8 + 4,
        color: widget.color.withOpacity(random.nextDouble() * 0.8 + 0.2),
        life: 1.0,
      );
    });
  }

  void _startAnimations() {
    _mainController.forward();
    _particlesController.forward();
    _pulseController.repeat(reverse: true);

    _mainController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particlesController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainController,
        _particlesController,
        _pulseController,
      ]),
      builder: (context, child) {
        return CustomPaint(
          size: const Size(200, 200),
          painter: ReactionEffectsPainter(
            emoji: widget.emoji,
            color: widget.color,
            scale: _scaleAnimation.value,
            rotation: _rotationAnimation.value,
            opacity: _opacityAnimation.value,
            pulse: _pulseAnimation.value,
            particles: _particles,
            particlesProgress: _particlesController.value,
          ),
        );
      },
    );
  }
}

/// رسام التأثيرات المتقدمة
class ReactionEffectsPainter extends CustomPainter {
  final String emoji;
  final Color color;
  final double scale;
  final double rotation;
  final double opacity;
  final double pulse;
  final List<Particle> particles;
  final double particlesProgress;

  ReactionEffectsPainter({
    required this.emoji,
    required this.color,
    required this.scale,
    required this.rotation,
    required this.opacity,
    required this.pulse,
    required this.particles,
    required this.particlesProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    // رسم الجسيمات
    _drawParticles(canvas, center);

    // رسم الدوائر المتموجة
    _drawRipples(canvas, center, size);

    // رسم الإيموجي الرئيسي
    _drawMainEmoji(canvas, center);
  }

  void _drawParticles(Canvas canvas, Offset center) {
    for (final particle in particles) {
      final progress = particlesProgress;
      final x = center.dx + particle.x + particle.vx * progress * 50;
      final y = center.dy + particle.y + particle.vy * progress * 50;
      
      final paint = Paint()
        ..color = particle.color.withOpacity(
          particle.color.opacity * (1.0 - progress),
        )
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(x, y),
        particle.size * (1.0 - progress * 0.5),
        paint,
      );
    }
  }

  void _drawRipples(Canvas canvas, Offset center, Size size) {
    final ripplePaint = Paint()
      ..color = color.withOpacity(0.3 * opacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    // رسم 3 دوائر متموجة
    for (int i = 0; i < 3; i++) {
      final rippleRadius = (scale * 30) + (i * 15);
      final rippleOpacity = opacity * (1.0 - i * 0.3);
      
      ripplePaint.color = color.withOpacity(rippleOpacity * 0.5);
      
      canvas.drawCircle(center, rippleRadius, ripplePaint);
    }
  }

  void _drawMainEmoji(Canvas canvas, Offset center) {
    canvas.save();
    
    // تطبيق التحويلات
    canvas.translate(center.dx, center.dy);
    canvas.rotate(rotation);
    canvas.scale(scale * pulse);

    // رسم خلفية دائرية
    final backgroundPaint = Paint()
      ..color = color.withOpacity(0.2 * opacity)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset.zero, 25, backgroundPaint);

    // رسم الإيموجي
    final textPainter = TextPainter(
      text: TextSpan(
        text: emoji,
        style: TextStyle(
          fontSize: 32,
          color: Colors.white.withOpacity(opacity),
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(-textPainter.width / 2, -textPainter.height / 2),
    );

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// جسيم للتأثيرات
class Particle {
  double x;
  double y;
  double vx;
  double vy;
  double size;
  Color color;
  double life;

  Particle({
    required this.x,
    required this.y,
    required this.vx,
    required this.vy,
    required this.size,
    required this.color,
    required this.life,
  });
}

/// ويدجت تأثير الانفجار للتفاعلات
class ReactionBurstWidget extends StatefulWidget {
  final String emoji;
  final Color color;
  final VoidCallback? onComplete;

  const ReactionBurstWidget({
    super.key,
    required this.emoji,
    required this.color,
    this.onComplete,
  });

  @override
  State<ReactionBurstWidget> createState() => _ReactionBurstWidgetState();
}

class _ReactionBurstWidgetState extends State<ReactionBurstWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<BurstParticle> _burstParticles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _generateBurstParticles();
    _controller.forward();

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onComplete?.call();
      }
    });
  }

  void _generateBurstParticles() {
    final random = math.Random();
    _burstParticles = List.generate(20, (index) {
      final angle = (index / 20) * 2 * math.pi;
      final speed = random.nextDouble() * 3 + 2;
      
      return BurstParticle(
        angle: angle,
        speed: speed,
        size: random.nextDouble() * 6 + 3,
        color: widget.color,
        emoji: widget.emoji,
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(150, 150),
          painter: BurstPainter(
            particles: _burstParticles,
            progress: _controller.value,
            centerEmoji: widget.emoji,
            centerColor: widget.color,
          ),
        );
      },
    );
  }
}

/// رسام تأثير الانفجار
class BurstPainter extends CustomPainter {
  final List<BurstParticle> particles;
  final double progress;
  final String centerEmoji;
  final Color centerColor;

  BurstPainter({
    required this.particles,
    required this.progress,
    required this.centerEmoji,
    required this.centerColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    // رسم الجسيمات المنفجرة
    for (final particle in particles) {
      final distance = particle.speed * progress * 40;
      final x = center.dx + math.cos(particle.angle) * distance;
      final y = center.dy + math.sin(particle.angle) * distance;

      final opacity = 1.0 - progress;
      final particleSize = particle.size * (1.0 - progress * 0.5);

      final paint = Paint()
        ..color = particle.color.withOpacity(opacity)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(x, y), particleSize, paint);
    }

    // رسم الإيموجي المركزي
    final centerScale = 1.0 + (progress * 0.5);
    final centerOpacity = 1.0 - (progress * 0.7);

    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.scale(centerScale);

    final textPainter = TextPainter(
      text: TextSpan(
        text: centerEmoji,
        style: TextStyle(
          fontSize: 24,
          color: Colors.white.withOpacity(centerOpacity),
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(-textPainter.width / 2, -textPainter.height / 2),
    );

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// جسيم الانفجار
class BurstParticle {
  final double angle;
  final double speed;
  final double size;
  final Color color;
  final String emoji;

  BurstParticle({
    required this.angle,
    required this.speed,
    required this.size,
    required this.color,
    required this.emoji,
  });
}

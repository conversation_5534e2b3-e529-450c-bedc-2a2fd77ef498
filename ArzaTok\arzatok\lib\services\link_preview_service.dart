import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/link_preview_model.dart';

/// خدمة معاينة الروابط
class LinkPreviewService {
  static const String _apiKey = 'your_api_key_here'; // يمكن استخدام خدمة مجانية

  /// استخراج الروابط من النص
  static List<String> extractUrls(String text) {
    final urlRegex = RegExp(
      r'https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?',
      caseSensitive: false,
    );

    final matches = urlRegex.allMatches(text);
    return matches.map((match) => match.group(0)!).toList();
  }

  /// التحقق من صحة الرابط
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// جلب معاينة الرابط
  static Future<LinkPreviewModel?> fetchLinkPreview(String url) async {
    try {
      debugPrint('🔗 Fetching link preview for: $url');

      if (!isValidUrl(url)) {
        debugPrint('❌ Invalid URL: $url');
        return null;
      }

      // محاولة جلب البيانات مباشرة من الموقع
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final html = response.body;
        final preview = _parseHtmlMetaTags(url, html);

        debugPrint('✅ Link preview fetched: ${preview.title}');
        return preview;
      } else {
        debugPrint('❌ HTTP Error: ${response.statusCode}');
        return _createBasicPreview(url);
      }
    } catch (e) {
      debugPrint('❌ Error fetching link preview: $e');
      return _createBasicPreview(url);
    }
  }

  /// تحليل HTML واستخراج Meta Tags
  static LinkPreviewModel _parseHtmlMetaTags(String url, String html) {
    String? title;
    String? description;
    String? imageUrl;
    String? siteName;

    // استخراج العنوان
    final titleMatch = RegExp(r'<title[^>]*>([^<]+)</title>', caseSensitive: false)
        .firstMatch(html);
    title = titleMatch?.group(1)?.trim();

    // استخراج Open Graph tags
    final ogTitleMatch = RegExp(r'<meta[^>]*property=["\x27]og:title["\x27][^>]*content=["\x27]([^"\x27]+)["\x27]', caseSensitive: false)
        .firstMatch(html);
    if (ogTitleMatch != null) {
      title = ogTitleMatch.group(1)?.trim();
    }

    final ogDescMatch = RegExp(r'<meta[^>]*property=["\x27]og:description["\x27][^>]*content=["\x27]([^"\x27]+)["\x27]', caseSensitive: false)
        .firstMatch(html);
    description = ogDescMatch?.group(1)?.trim();

    final ogImageMatch = RegExp(r'<meta[^>]*property=["\x27]og:image["\x27][^>]*content=["\x27]([^"\x27]+)["\x27]', caseSensitive: false)
        .firstMatch(html);
    imageUrl = ogImageMatch?.group(1)?.trim();

    final ogSiteMatch = RegExp(r'<meta[^>]*property=["\x27]og:site_name["\x27][^>]*content=["\x27]([^"\x27]+)["\x27]', caseSensitive: false)
        .firstMatch(html);
    siteName = ogSiteMatch?.group(1)?.trim();

    // استخراج meta description إذا لم توجد og:description
    if (description == null || description.isEmpty) {
      final metaDescMatch = RegExp(r'<meta[^>]*name=["\x27]description["\x27][^>]*content=["\x27]([^"\x27]+)["\x27]', caseSensitive: false)
          .firstMatch(html);
      description = metaDescMatch?.group(1)?.trim();
    }

    // تحويل الروابط النسبية إلى مطلقة
    if (imageUrl != null && imageUrl.isNotEmpty && !imageUrl.startsWith('http')) {
      final uri = Uri.parse(url);
      if (imageUrl.startsWith('/')) {
        imageUrl = '${uri.scheme}://${uri.host}$imageUrl';
      } else {
        imageUrl = '${uri.scheme}://${uri.host}/${imageUrl}';
      }
    }

    return LinkPreviewModel(
      url: url,
      title: title,
      description: description,
      imageUrl: imageUrl,
      siteName: siteName,
      domain: Uri.parse(url).host,
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء معاينة أساسية
  static LinkPreviewModel _createBasicPreview(String url) {
    final uri = Uri.parse(url);
    return LinkPreviewModel(
      url: url,
      title: uri.host,
      description: 'Link preview',
      domain: uri.host,
      createdAt: DateTime.now(),
    );
  }

  /// جلب معاينات متعددة
  static Future<List<LinkPreviewModel>> fetchMultiplePreviews(List<String> urls) async {
    final previews = <LinkPreviewModel>[];

    for (final url in urls) {
      final preview = await fetchLinkPreview(url);
      if (preview != null) {
        previews.add(preview);
      }
    }

    return previews;
  }

  /// تنظيف HTML من العلامات
  static String cleanHtml(String html) {
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .trim();
  }
}

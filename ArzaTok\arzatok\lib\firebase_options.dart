// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD9UajheGdVV42BU3hb8hIdyo5j8VvGCGY',
    appId: '1:696830863896:web:cebcc0ff9b752b9556716d',
    messagingSenderId: '696830863896',
    projectId: 'arzatalk-chat',
    authDomain: 'arzatalk-chat.firebaseapp.com',
    storageBucket: 'arzatalk-chat.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD9UajheGdVV42BU3hb8hIdyo5j8VvGCGY',
    appId: '1:696830863896:android:cebcc0ff9b752b9556716d',
    messagingSenderId: '696830863896',
    projectId: 'arzatalk-chat',
    storageBucket: 'arzatalk-chat.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD9UajheGdVV42BU3hb8hIdyo5j8VvGCGY',
    appId: '1:696830863896:ios:cebcc0ff9b752b9556716d',
    messagingSenderId: '696830863896',
    projectId: 'arzatalk-chat',
    storageBucket: 'arzatalk-chat.firebasestorage.app',
    iosBundleId: 'com.arzapress.arzatalk',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyD9UajheGdVV42BU3hb8hIdyo5j8VvGCGY',
    appId: '1:696830863896:macos:cebcc0ff9b752b9556716d',
    messagingSenderId: '696830863896',
    projectId: 'arzatalk-chat',
    storageBucket: 'arzatalk-chat.firebasestorage.app',
    iosBundleId: 'com.arzapress.arzatalk',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD9UajheGdVV42BU3hb8hIdyo5j8VvGCGY',
    appId: '1:696830863896:windows:cebcc0ff9b752b9556716d',
    messagingSenderId: '696830863896',
    projectId: 'arzatalk-chat',
    authDomain: 'arzatalk-chat.firebaseapp.com',
    storageBucket: 'arzatalk-chat.firebasestorage.app',
  );
}
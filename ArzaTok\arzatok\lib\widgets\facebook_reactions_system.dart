import 'package:flutter/material.dart';
import 'dart:math' as math;

/// نظام تفاعلات Facebook الحقيقي مع الملصقات المتحركة
class FacebookReactionsSystem extends StatefulWidget {
  final Function(String) onReactionSelected;
  final String? currentReaction;
  final VoidCallback? onClose;

  const FacebookReactionsSystem({
    super.key,
    required this.onReactionSelected,
    this.currentReaction,
    this.onClose,
  });

  @override
  State<FacebookReactionsSystem> createState() => _FacebookReactionsSystemState();
}

class _FacebookReactionsSystemState extends State<FacebookReactionsSystem>
    with TickerProviderStateMixin {
  late AnimationController _containerController;
  late AnimationController _reactionsController;
  late Animation<double> _containerAnimation;
  late Animation<double> _reactionsAnimation;
  
  int _hoveredIndex = -1;
  bool _isVisible = false;

  // تعريف التفاعلات مع الملصقات المتحركة
  final List<FacebookReaction> _reactions = [
    FacebookReaction(
      id: 'like',
      name: 'Like',
      color: const Color(0xFF1877F2),
      asset: 'assets/reactions/like.json', // Lottie animation
      staticAsset: 'assets/reactions/like_static.png',
    ),
    FacebookReaction(
      id: 'love',
      name: 'Love',
      color: const Color(0xFFE91E63),
      asset: 'assets/reactions/love.json',
      staticAsset: 'assets/reactions/love_static.png',
    ),
    FacebookReaction(
      id: 'haha',
      name: 'Haha',
      color: const Color(0xFFFFC107),
      asset: 'assets/reactions/haha.json',
      staticAsset: 'assets/reactions/haha_static.png',
    ),
    FacebookReaction(
      id: 'wow',
      name: 'Wow',
      color: const Color(0xFFFFC107),
      asset: 'assets/reactions/wow.json',
      staticAsset: 'assets/reactions/wow_static.png',
    ),
    FacebookReaction(
      id: 'sad',
      name: 'Sad',
      color: const Color(0xFFFFC107),
      asset: 'assets/reactions/sad.json',
      staticAsset: 'assets/reactions/sad_static.png',
    ),
    FacebookReaction(
      id: 'angry',
      name: 'Angry',
      color: const Color(0xFFFF5722),
      asset: 'assets/reactions/angry.json',
      staticAsset: 'assets/reactions/angry_static.png',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _showReactions();
  }

  void _initializeAnimations() {
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _reactionsController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _containerAnimation = CurvedAnimation(
      parent: _containerController,
      curve: Curves.easeOutBack,
    );

    _reactionsAnimation = CurvedAnimation(
      parent: _reactionsController,
      curve: Curves.elasticOut,
    );
  }

  void _showReactions() {
    setState(() {
      _isVisible = true;
    });
    
    _containerController.forward();
    
    // تأخير ظهور التفاعلات للحصول على تأثير متتالي
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _reactionsController.forward();
      }
    });
  }

  void _hideReactions() {
    _reactionsController.reverse();
    _containerController.reverse();
    
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _isVisible = false;
        });
        widget.onClose?.call();
      }
    });
  }

  @override
  void dispose() {
    _containerController.dispose();
    _reactionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return GestureDetector(
      onTap: _hideReactions,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // الخلفية الشفافة
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.1),
              ),
            ),
            
            // حاوية التفاعلات
            Positioned(
              bottom: 80,
              left: 20,
              right: 20,
              child: _buildReactionsContainer(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReactionsContainer() {
    return AnimatedBuilder(
      animation: _containerAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _containerAnimation.value,
          child: Container(
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: _reactions.asMap().entries.map((entry) {
                final index = entry.key;
                final reaction = entry.value;
                return _buildReactionItem(reaction, index);
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReactionItem(FacebookReaction reaction, int index) {
    final isHovered = _hoveredIndex == index;
    final isSelected = widget.currentReaction == reaction.id;

    return AnimatedBuilder(
      animation: _reactionsAnimation,
      builder: (context, child) {
        // تأثير الظهور المتتالي
        final delay = index * 0.1;
        final animationValue = (_reactionsAnimation.value - delay).clamp(0.0, 1.0);
        
        return Transform.translate(
          offset: Offset(0, (1 - animationValue) * 20),
          child: Transform.scale(
            scale: animationValue,
            child: GestureDetector(
              onTap: () => _selectReaction(reaction),
              child: MouseRegion(
                onEnter: (_) => _setHoveredIndex(index),
                onExit: (_) => _setHoveredIndex(-1),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: isHovered ? 70 : 50,
                  height: isHovered ? 70 : 50,
                  margin: EdgeInsets.symmetric(
                    horizontal: isHovered ? 5 : 8,
                    vertical: isHovered ? 0 : 10,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? reaction.color.withOpacity(0.1) 
                        : Colors.transparent,
                    shape: BoxShape.circle,
                    border: isSelected 
                        ? Border.all(color: reaction.color, width: 2)
                        : null,
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // الملصق المتحرك
                      _buildAnimatedSticker(reaction, isHovered),
                      
                      // اسم التفاعل عند التحويم
                      if (isHovered)
                        Positioned(
                          bottom: -25,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              reaction.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedSticker(FacebookReaction reaction, bool isHovered) {
    // استخدام ملصقات مخصصة بدلاً من Lottie للبساطة
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: isHovered ? 200 : 100),
      tween: Tween(begin: 1.0, end: isHovered ? 1.3 : 1.0),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: _buildCustomReactionSticker(reaction, isHovered),
        );
      },
    );
  }

  Widget _buildCustomReactionSticker(FacebookReaction reaction, bool isAnimated) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: _getReactionGradient(reaction.id),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: reaction.color.withOpacity(0.3),
            blurRadius: isAnimated ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الخلفية المتحركة
          if (isAnimated)
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 1000),
              tween: Tween(begin: 0.0, end: 2 * math.pi),
              builder: (context, rotation, child) {
                return Transform.rotate(
                  angle: rotation,
                  child: Container(
                    width: 35,
                    height: 35,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withOpacity(0.5),
                        width: 2,
                      ),
                    ),
                  ),
                );
              },
            ),
          
          // الأيقونة الرئيسية
          Icon(
            _getReactionIcon(reaction.id),
            color: Colors.white,
            size: 20,
          ),
        ],
      ),
    );
  }

  LinearGradient _getReactionGradient(String reactionId) {
    switch (reactionId) {
      case 'like':
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'love':
        return const LinearGradient(
          colors: [Color(0xFFE91E63), Color(0xFFFF6B9D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'haha':
        return const LinearGradient(
          colors: [Color(0xFFFFC107), Color(0xFFFFD54F)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'wow':
        return const LinearGradient(
          colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'sad':
        return const LinearGradient(
          colors: [Color(0xFF607D8B), Color(0xFF90A4AE)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'angry':
        return const LinearGradient(
          colors: [Color(0xFFFF5722), Color(0xFFFF7043)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  IconData _getReactionIcon(String reactionId) {
    switch (reactionId) {
      case 'like':
        return Icons.thumb_up;
      case 'love':
        return Icons.favorite;
      case 'haha':
        return Icons.sentiment_very_satisfied;
      case 'wow':
        return Icons.sentiment_very_satisfied;
      case 'sad':
        return Icons.sentiment_very_dissatisfied;
      case 'angry':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.thumb_up;
    }
  }

  void _setHoveredIndex(int index) {
    setState(() {
      _hoveredIndex = index;
    });
  }

  void _selectReaction(FacebookReaction reaction) {
    widget.onReactionSelected(reaction.id);
    _hideReactions();
  }
}

/// نموذج تفاعل Facebook
class FacebookReaction {
  final String id;
  final String name;
  final Color color;
  final String asset;
  final String staticAsset;

  const FacebookReaction({
    required this.id,
    required this.name,
    required this.color,
    required this.asset,
    required this.staticAsset,
  });
}

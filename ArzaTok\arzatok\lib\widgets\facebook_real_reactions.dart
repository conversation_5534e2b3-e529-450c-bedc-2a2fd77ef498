import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'dart:math' as math;

/// نظام تفاعلات Facebook الحقيقي مع Lottie
class FacebookRealReactions extends StatefulWidget {
  final Function(String) onReactionSelected;
  final String? currentReaction;
  final VoidCallback? onClose;
  final Offset? buttonPosition;
  final Size? buttonSize;

  const FacebookRealReactions({
    super.key,
    required this.onReactionSelected,
    this.currentReaction,
    this.onClose,
    this.buttonPosition,
    this.buttonSize,
  });

  @override
  State<FacebookRealReactions> createState() => _FacebookRealReactionsState();
}

class _FacebookRealReactionsState extends State<FacebookRealReactions>
    with TickerProviderStateMixin {
  late AnimationController _containerController;
  late AnimationController _reactionsController;
  late AnimationController _springController;

  late Animation<double> _containerScale;
  late Animation<double> _containerOpacity;
  late Animation<Offset> _containerSlide;

  final List<AnimationController> _reactionControllers = [];
  final List<Animation<double>> _reactionScales = [];
  final List<Animation<double>> _reactionRotations = [];

  int _hoveredIndex = -1;
  bool _isVisible = false;

  // تعريف التفاعلات الستة الجديدة فقط (مثل Facebook)
  final List<FacebookReactionData> _reactions = [
    FacebookReactionData(
      id: 'like',
      name: 'Like',
      color: const Color(0xFF1877F2),
      lottieAsset: 'assets/lottie/like.json',
      staticIcon: Icons.thumb_up,
    ),
    FacebookReactionData(
      id: 'love',
      name: 'Love',
      color: const Color(0xFFE91E63),
      lottieAsset: 'assets/lottie/love.json',
      staticIcon: Icons.favorite,
    ),
    FacebookReactionData(
      id: 'haha',
      name: 'Haha',
      color: const Color(0xFFFFC107),
      lottieAsset: 'assets/lottie/haha.json',
      staticIcon: Icons.sentiment_very_satisfied,
    ),
    FacebookReactionData(
      id: 'wow',
      name: 'Wow',
      color: const Color(0xFFFF9800),
      lottieAsset: 'assets/lottie/wow.json',
      staticIcon: Icons.sentiment_very_satisfied,
    ),
    FacebookReactionData(
      id: 'sad',
      name: 'Sad',
      color: const Color(0xFF607D8B),
      lottieAsset: 'assets/lottie/sad.json',
      staticIcon: Icons.sentiment_very_dissatisfied,
    ),
    FacebookReactionData(
      id: 'angry',
      name: 'Angry',
      color: const Color(0xFFFF5722),
      lottieAsset: 'assets/lottie/angry.json',
      staticIcon: Icons.sentiment_very_dissatisfied,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _showReactions();
  }

  void _initializeAnimations() {
    // تحكم الحاوية الرئيسية
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // تحكم التفاعلات
    _reactionsController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // تحكم الحركة المرنة
    _springController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // حركات الحاوية
    _containerScale = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _containerController,
      curve: Curves.elasticOut,
    ));

    _containerOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _containerController,
      curve: Curves.easeOut,
    ));

    _containerSlide = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _containerController,
      curve: Curves.easeOutBack,
    ));

    // تحكم كل تفاعل منفرد
    for (int i = 0; i < _reactions.length; i++) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (i * 100)),
        vsync: this,
      );

      _reactionControllers.add(controller);

      _reactionScales.add(
        Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: controller,
            curve: Curves.elasticOut,
          ),
        ),
      );

      _reactionRotations.add(
        Tween<double>(begin: -0.5, end: 0.0).animate(
          CurvedAnimation(
            parent: controller,
            curve: Curves.easeOutBack,
          ),
        ),
      );
    }
  }

  void _showReactions() {
    setState(() {
      _isVisible = true;
    });

    _containerController.forward();

    // تأخير ظهور التفاعلات للحصول على تأثير متتالي
    for (int i = 0; i < _reactionControllers.length; i++) {
      Future.delayed(Duration(milliseconds: 100 + (i * 80)), () {
        if (mounted) {
          _reactionControllers[i].forward();
        }
      });
    }

    _springController.repeat();
  }

  void _hideReactions() {
    for (var controller in _reactionControllers) {
      controller.reverse();
    }
    _containerController.reverse();
    _springController.stop();

    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _isVisible = false;
        });
        widget.onClose?.call();
      }
    });
  }

  @override
  void dispose() {
    _containerController.dispose();
    _reactionsController.dispose();
    _springController.dispose();
    for (var controller in _reactionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return GestureDetector(
      onTap: _hideReactions,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // الخلفية الشفافة مع تمويه
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.1),
              ),
            ),

            // حاوية التفاعلات قريبة من الزر
            _buildPositionedReactionsContainer(),
          ],
        ),
      ),
    );
  }

  /// بناء حاوية التفاعلات في الموضع الصحيح
  Widget _buildPositionedReactionsContainer() {
    // حساب الموضع بناءً على موضع الزر
    double left = 20;
    double top = 200; // استخدام top بدلاً من bottom

    if (widget.buttonPosition != null && widget.buttonSize != null) {
      // وضع التفاعلات فوق الزر مباشرة
      left = widget.buttonPosition!.dx - 150; // توسيط التفاعلات
      top = widget.buttonPosition!.dy - 80; // فوق الزر مباشرة

      // التأكد من أن التفاعلات لا تخرج من الشاشة
      if (left < 20) left = 20;
      if (left > MediaQuery.of(context).size.width - 320) {
        left = MediaQuery.of(context).size.width - 320;
      }

      // التأكد من أن التفاعلات لا تخرج من أعلى الشاشة
      if (top < 100) top = widget.buttonPosition!.dy + widget.buttonSize!.height + 10;
    }

    return Positioned(
      top: top,
      left: left,
      child: _buildReactionsContainer(),
    );
  }

  Widget _buildReactionsContainer() {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _containerController,
        _springController,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _containerScale.value,
          child: SlideTransition(
            position: _containerSlide,
            child: Opacity(
              opacity: _containerOpacity.value,
              child: Container(
                height: 70,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(35),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                      spreadRadius: 4,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 40,
                      offset: const Offset(0, 16),
                      spreadRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: _reactions.asMap().entries.map((entry) {
                    final index = entry.key;
                    final reaction = entry.value;
                    return _buildAdvancedReactionItem(reaction, index);
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAdvancedReactionItem(FacebookReactionData reaction, int index) {
    final isHovered = _hoveredIndex == index;
    final isSelected = widget.currentReaction == reaction.id;

    return AnimatedBuilder(
      animation: Listenable.merge([
        _reactionControllers[index],
        _springController,
      ]),
      builder: (context, child) {
        final springValue = math.sin(_springController.value * 2 * math.pi) * 0.1;
        final hoverScale = isHovered ? 1.4 : 1.0;
        final baseScale = _reactionScales[index].value;
        final rotation = _reactionRotations[index].value;

        return GestureDetector(
          onTap: () => _selectReaction(reaction),
          child: MouseRegion(
            onEnter: (_) => _setHoveredIndex(index),
            onExit: (_) => _setHoveredIndex(-1),
            child: Transform.scale(
              scale: baseScale * hoverScale,
              child: Transform.rotate(
                angle: rotation + (isHovered ? springValue : 0),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 50,
                  height: 50,
                  margin: EdgeInsets.symmetric(
                    horizontal: isHovered ? 2 : 4,
                    vertical: isHovered ? 5 : 10,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? reaction.color.withOpacity(0.1)
                        : Colors.transparent,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(color: reaction.color, width: 2)
                        : null,
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // الملصق المتحرك الحقيقي
                      _buildRealAnimatedSticker(reaction, isHovered, isSelected),

                      // اسم التفاعل عند التحويم
                      if (isHovered)
                        Positioned(
                          bottom: -30,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              reaction.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRealAnimatedSticker(FacebookReactionData reaction, bool isHovered, bool isSelected) {
    // محاولة استخدام Lottie أولاً، ثم العودة للأيقونات المخصصة
    return FutureBuilder<bool>(
      future: _checkLottieAsset(reaction.lottieAsset),
      builder: (context, snapshot) {
        final hasLottie = snapshot.data ?? false;

        if (hasLottie) {
          return _buildLottieSticker(reaction, isHovered);
        } else {
          return _buildCustomAnimatedSticker(reaction, isHovered, isSelected);
        }
      },
    );
  }

  Widget _buildLottieSticker(FacebookReactionData reaction, bool isHovered) {
    return Lottie.asset(
      reaction.lottieAsset,
      width: 40,
      height: 40,
      repeat: isHovered,
      animate: isHovered,
      fit: BoxFit.contain,
    );
  }

  Widget _buildCustomAnimatedSticker(FacebookReactionData reaction, bool isHovered, bool isSelected) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: isHovered ? 300 : 200),
      tween: Tween(begin: 1.0, end: isHovered ? 1.2 : 1.0),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: Container(
            width: 35,
            height: 35,
            decoration: BoxDecoration(
              gradient: _getAdvancedGradient(reaction.id, isHovered),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: reaction.color.withOpacity(isHovered ? 0.4 : 0.2),
                  blurRadius: isHovered ? 12 : 6,
                  offset: const Offset(0, 3),
                ),
                if (isHovered)
                  BoxShadow(
                    color: reaction.color.withOpacity(0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 6),
                  ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // حلقة متحركة للخلفية
                if (isHovered)
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1000),
                    tween: Tween(begin: 0.0, end: 2 * math.pi),
                    builder: (context, rotation, child) {
                      return Transform.rotate(
                        angle: rotation,
                        child: Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.6),
                              width: 2,
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                // الأيقونة الرئيسية مع تأثيرات
                Icon(
                  reaction.staticIcon,
                  color: Colors.white,
                  size: 18,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  LinearGradient _getAdvancedGradient(String reactionId, bool isHovered) {
    final intensity = isHovered ? 1.0 : 0.8;

    switch (reactionId) {
      case 'like':
        return LinearGradient(
          colors: [
            Color(0xFF1877F2).withOpacity(intensity),
            Color(0xFF42A5F5).withOpacity(intensity),
            Color(0xFF64B5F6).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'love':
        return LinearGradient(
          colors: [
            Color(0xFFE91E63).withOpacity(intensity),
            Color(0xFFFF6B9D).withOpacity(intensity),
            Color(0xFFFF8A95).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'haha':
        return LinearGradient(
          colors: [
            Color(0xFFFFC107).withOpacity(intensity),
            Color(0xFFFFD54F).withOpacity(intensity),
            Color(0xFFFFE082).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'wow':
        return LinearGradient(
          colors: [
            Color(0xFFFF9800).withOpacity(intensity),
            Color(0xFFFFB74D).withOpacity(intensity),
            Color(0xFFFFCC02).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'sad':
        return LinearGradient(
          colors: [
            Color(0xFF607D8B).withOpacity(intensity),
            Color(0xFF90A4AE).withOpacity(intensity),
            Color(0xFFB0BEC5).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'angry':
        return LinearGradient(
          colors: [
            Color(0xFFFF5722).withOpacity(intensity),
            Color(0xFFFF7043).withOpacity(intensity),
            Color(0xFFFF8A65).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return LinearGradient(
          colors: [
            Color(0xFF1877F2).withOpacity(intensity),
            Color(0xFF42A5F5).withOpacity(intensity),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  Future<bool> _checkLottieAsset(String assetPath) async {
    try {
      // محاولة تحميل الملف للتحقق من وجوده
      // في الواقع، سنعود false لأننا لم ننشئ ملفات Lottie بعد
      return false;
    } catch (e) {
      return false;
    }
  }

  void _setHoveredIndex(int index) {
    setState(() {
      _hoveredIndex = index;
    });
  }

  void _selectReaction(FacebookReactionData reaction) {
    widget.onReactionSelected(reaction.id);
    _hideReactions();
  }
}

/// نموذج بيانات تفاعل Facebook
class FacebookReactionData {
  final String id;
  final String name;
  final Color color;
  final String lottieAsset;
  final IconData staticIcon;

  const FacebookReactionData({
    required this.id,
    required this.name,
    required this.color,
    required this.lottieAsset,
    required this.staticIcon,
  });
}

/// زر التفاعل المتقدم مع فيزياء الحركة
class FacebookAdvancedReactionButton extends StatefulWidget {
  final String? currentReaction;
  final Function(String) onReactionChanged;
  final VoidCallback? onShowReactions;

  const FacebookAdvancedReactionButton({
    super.key,
    this.currentReaction,
    required this.onReactionChanged,
    this.onShowReactions,
  });

  @override
  State<FacebookAdvancedReactionButton> createState() => _FacebookAdvancedReactionButtonState();
}

class _FacebookAdvancedReactionButtonState extends State<FacebookAdvancedReactionButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late AnimationController _physicsController;

  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _physicsAnimation;

  bool _showingReactions = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _initializePhysicsAnimations();
  }

  void _initializePhysicsAnimations() {
    // نبض مستمر للتفاعل النشط
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // حركة الارتداد عند التفاعل
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // فيزياء الحركة المتقدمة
    _physicsController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.4,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    // فيزياء الحركة مع Spring
    _physicsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _physicsController,
      curve: const SpringCurve(),
    ));

    // تشغيل النبض إذا كان هناك تفاعل
    if (widget.currentReaction != null) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(FacebookAdvancedReactionButton oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.currentReaction != oldWidget.currentReaction) {
      if (widget.currentReaction != null) {
        _bounceController.forward().then((_) {
          _bounceController.reverse();
          _pulseController.repeat(reverse: true);
        });
        _physicsController.forward();
      } else {
        _pulseController.stop();
        _pulseController.reset();
        _physicsController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    _physicsController.dispose();
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _pulseAnimation,
          _bounceAnimation,
          _physicsAnimation,
        ]),
        builder: (context, child) {
          final scale = _bounceAnimation.value * _pulseAnimation.value;
          final physicsOffset = math.sin(_physicsAnimation.value * 2 * math.pi) * 2;

          return Transform.scale(
            scale: scale,
            child: Transform.translate(
              offset: Offset(0, physicsOffset),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: _getButtonColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: widget.currentReaction != null
                      ? Border.all(color: _getButtonColor().withOpacity(0.3))
                      : null,
                  boxShadow: widget.currentReaction != null ? [
                    BoxShadow(
                      color: _getButtonColor().withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ] : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة التفاعل المتحركة المتقدمة
                    _buildAdvancedReactionIcon(),

                    const SizedBox(width: 6),

                    // نص التفاعل مع تأثيرات
                    Text(
                      _getReactionText(),
                      style: TextStyle(
                        color: _getButtonColor(),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAdvancedReactionIcon() {
    if (widget.currentReaction == null) {
      return Icon(
        Icons.thumb_up_outlined,
        size: 18,
        color: _getButtonColor(),
      );
    }

    return AnimatedBuilder(
      animation: _physicsAnimation,
      builder: (context, child) {
        final rotation = math.sin(_physicsAnimation.value * 4 * math.pi) * 0.1;
        final scale = 1.0 + (math.sin(_physicsAnimation.value * 6 * math.pi) * 0.1);

        return Transform.rotate(
          angle: rotation,
          child: Transform.scale(
            scale: scale,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                gradient: _getAdvancedReactionGradient(),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: _getButtonColor().withOpacity(0.4),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                  BoxShadow(
                    color: _getButtonColor().withOpacity(0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // حلقة متحركة
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1000),
                    tween: Tween(begin: 0.0, end: 2 * math.pi),
                    builder: (context, rotation, child) {
                      return Transform.rotate(
                        angle: rotation,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.7),
                              width: 1.5,
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // الأيقونة الرئيسية
                  Icon(
                    _getReactionIcon(),
                    color: Colors.white,
                    size: 12,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getButtonColor() {
    if (widget.currentReaction == null) {
      return const Color(0xFF65676B);
    }

    switch (widget.currentReaction) {
      case 'like':
        return const Color(0xFF1877F2);
      case 'love':
        return const Color(0xFFE91E63);
      case 'haha':
        return const Color(0xFFFFC107);
      case 'wow':
        return const Color(0xFFFF9800);
      case 'sad':
        return const Color(0xFF607D8B);
      case 'angry':
        return const Color(0xFFFF5722);
      default:
        return const Color(0xFF65676B);
    }
  }

  String _getReactionText() {
    if (widget.currentReaction == null) {
      return 'Like';
    }

    switch (widget.currentReaction) {
      case 'like':
        return 'Like';
      case 'love':
        return 'Love';
      case 'haha':
        return 'Haha';
      case 'wow':
        return 'Wow';
      case 'sad':
        return 'Sad';
      case 'angry':
        return 'Angry';
      default:
        return 'Like';
    }
  }

  IconData _getReactionIcon() {
    switch (widget.currentReaction) {
      case 'like':
        return Icons.thumb_up;
      case 'love':
        return Icons.favorite;
      case 'haha':
        return Icons.sentiment_very_satisfied;
      case 'wow':
        return Icons.sentiment_very_satisfied;
      case 'sad':
        return Icons.sentiment_very_dissatisfied;
      case 'angry':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.thumb_up;
    }
  }

  LinearGradient _getAdvancedReactionGradient() {
    switch (widget.currentReaction) {
      case 'like':
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5), Color(0xFF64B5F6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'love':
        return const LinearGradient(
          colors: [Color(0xFFE91E63), Color(0xFFFF6B9D), Color(0xFFFF8A95)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'haha':
        return const LinearGradient(
          colors: [Color(0xFFFFC107), Color(0xFFFFD54F), Color(0xFFFFE082)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'wow':
        return const LinearGradient(
          colors: [Color(0xFFFF9800), Color(0xFFFFB74D), Color(0xFFFFCC02)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'sad':
        return const LinearGradient(
          colors: [Color(0xFF607D8B), Color(0xFF90A4AE), Color(0xFFB0BEC5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'angry':
        return const LinearGradient(
          colors: [Color(0xFFFF5722), Color(0xFFFF7043), Color(0xFFFF8A65)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  void _handleTap() {
    if (widget.currentReaction == null) {
      // إضافة Like افتراضي
      widget.onReactionChanged('like');
    } else {
      // إزالة التفاعل الحالي
      widget.onReactionChanged('');
    }
  }

  void _handleLongPress() {
    if (_showingReactions) return;

    _showReactionsOverlay();
    widget.onShowReactions?.call();
  }

  void _showReactionsOverlay() {
    if (_overlayEntry != null) return;

    _showingReactions = true;

    // الحصول على موضع الزر الحالي
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final buttonSize = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => FacebookRealReactions(
        currentReaction: widget.currentReaction,
        buttonPosition: buttonPosition,
        buttonSize: buttonSize,
        onReactionSelected: (reactionId) {
          widget.onReactionChanged(reactionId);
          _removeOverlay();
        },
        onClose: _removeOverlay,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _showingReactions = false;
    }
  }
}

/// منحنى الحركة المرنة مثل Facebook
class SpringCurve extends Curve {
  const SpringCurve();

  @override
  double transform(double t) {
    return 1.0 - math.pow(math.e, -6.0 * t) * math.cos(6.0 * t);
  }
}

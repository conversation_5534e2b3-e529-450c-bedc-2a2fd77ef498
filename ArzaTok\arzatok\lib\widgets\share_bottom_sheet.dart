import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/post_model.dart';
import '../services/social_feed_service.dart';
import '../services/auth_service.dart';

/// قائمة المشاركة الشاملة مثل Facebook
class ShareBottomSheet extends StatelessWidget {
  final PostModel post;

  const ShareBottomSheet({
    super.key,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  'Share Post',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ],
            ),
          ),

          // فاصل
          Divider(color: Colors.grey[200], height: 1),

          // خيارات المشاركة الداخلية
          _buildInternalShareSection(context),

          // فاصل
          Divider(color: Colors.grey[200], height: 1),

          // خيارات المشاركة الخارجية
          _buildExternalShareSection(context),

          // مساحة أسفل
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// بناء قسم المشاركة الداخلية
  Widget _buildInternalShareSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Share in ArzaTok',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1C1E21),
            ),
          ),
          const SizedBox(height: 12),

          // صف الخيارات الداخلية
          Row(
            children: [
              // مشاركة في القصة
              _buildShareOption(
                context: context,
                icon: Icons.add_circle,
                label: 'Your Story',
                color: const Color(0xFF1877F2),
                onTap: () => _shareToStory(context),
              ),

              const SizedBox(width: 20),

              // مشاركة في المجموعات
              _buildShareOption(
                context: context,
                icon: Icons.group,
                label: 'Groups',
                color: const Color(0xFF42B883),
                onTap: () => _shareToGroups(context),
              ),

              const SizedBox(width: 20),

              // إرسال في رسالة
              _buildShareOption(
                context: context,
                icon: Icons.send,
                label: 'Send Message',
                color: const Color(0xFF9C27B0),
                onTap: () => _sendInMessage(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم المشاركة الخارجية
  Widget _buildExternalShareSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Share to other apps',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1C1E21),
            ),
          ),
          const SizedBox(height: 12),

          // قائمة الخيارات الخارجية
          Column(
            children: [
              // Facebook
              _buildExternalShareTile(
                context: context,
                icon: Icons.facebook,
                label: 'Facebook',
                color: const Color(0xFF1877F2),
                onTap: () => _shareToFacebook(context),
              ),

              // Instagram
              _buildExternalShareTile(
                context: context,
                icon: Icons.camera_alt,
                label: 'Instagram',
                color: const Color(0xFFE4405F),
                onTap: () => _shareToInstagram(context),
              ),

              // WhatsApp
              _buildExternalShareTile(
                context: context,
                icon: Icons.chat,
                label: 'WhatsApp',
                color: const Color(0xFF25D366),
                onTap: () => _shareToWhatsApp(context),
              ),

              // Email
              _buildExternalShareTile(
                context: context,
                icon: Icons.email,
                label: 'Email',
                color: const Color(0xFF34A853),
                onTap: () => _shareViaEmail(context),
              ),

              // نسخ الرابط
              _buildExternalShareTile(
                context: context,
                icon: Icons.link,
                label: 'Copy Link',
                color: const Color(0xFF607D8B),
                onTap: () => _copyLink(context),
              ),

              // المزيد
              _buildExternalShareTile(
                context: context,
                icon: Icons.more_horiz,
                label: 'More options',
                color: const Color(0xFF9E9E9E),
                onTap: () => _shareToOtherApps(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء خيار مشاركة دائري
  Widget _buildShareOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر مشاركة خارجية
  Widget _buildExternalShareTile({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: color,
          size: 20,
        ),
      ),
      title: Text(
        label,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1C1E21),
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
    );
  }

  /// مشاركة في القصة
  void _shareToStory(BuildContext context) {
    Navigator.of(context).pop();

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('✅ Post shared to your story!'),
        backgroundColor: const Color(0xFF1877F2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );

    // هنا يمكن إضافة المنطق الفعلي لمشاركة القصة
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    // socialService.shareToStory(post);
  }

  /// مشاركة في المجموعات
  void _shareToGroups(BuildContext context) {
    Navigator.of(context).pop();

    // إظهار قائمة المجموعات
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildGroupsListSheet(context),
    );
  }

  /// بناء قائمة المجموعات
  Widget _buildGroupsListSheet(BuildContext context) {
    final demoGroups = [
      {'name': 'Family Group', 'members': 12, 'icon': Icons.family_restroom},
      {'name': 'Work Team', 'members': 25, 'icon': Icons.work},
      {'name': 'Friends Circle', 'members': 8, 'icon': Icons.group},
      {'name': 'Study Group', 'members': 15, 'icon': Icons.school},
    ];

    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  'Share to Groups',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ],
            ),
          ),

          // فاصل
          Divider(color: Colors.grey[200], height: 1),

          // قائمة المجموعات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: demoGroups.length,
              itemBuilder: (context, index) {
                final group = demoGroups[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: const Color(0xFF42B883),
                    child: Icon(
                      group['icon'] as IconData,
                      color: Colors.white,
                    ),
                  ),
                  title: Text(
                    group['name'] as String,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text('${group['members']} members'),
                  trailing: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _shareToSpecificGroup(context, group['name'] as String);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1877F2),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: const Text('Share'),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// مشاركة في مجموعة محددة
  void _shareToSpecificGroup(BuildContext context, String groupName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('✅ Post shared to $groupName!'),
        backgroundColor: const Color(0xFF42B883),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  /// إرسال في رسالة
  void _sendInMessage(BuildContext context) {
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('✅ Opening message composer...'),
        backgroundColor: Color(0xFF9C27B0),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// مشاركة على Facebook
  void _shareToFacebook(BuildContext context) {
    Navigator.of(context).pop();
    _launchUrl('https://www.facebook.com/sharer/sharer.php?u=${_getPostUrl()}');
  }

  /// مشاركة على Instagram
  void _shareToInstagram(BuildContext context) {
    Navigator.of(context).pop();
    _shareToOtherApps(context);
  }

  /// مشاركة على WhatsApp
  void _shareToWhatsApp(BuildContext context) {
    Navigator.of(context).pop();
    final text = 'Check out this post from ArzaTok: ${post.content}\n\n${_getPostUrl()}';
    _launchUrl('https://wa.me/?text=${Uri.encodeComponent(text)}');
  }

  /// مشاركة عبر البريد الإلكتروني
  void _shareViaEmail(BuildContext context) {
    Navigator.of(context).pop();
    final subject = 'Check out this post from ArzaTok';
    final body = 'Hi,\n\nI wanted to share this interesting post with you:\n\n"${post.content}"\n\nView it here: ${_getPostUrl()}\n\nShared via ArzaTok';
    _launchUrl('mailto:?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}');
  }

  /// نسخ الرابط
  void _copyLink(BuildContext context) {
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('✅ Link copied to clipboard!'),
        backgroundColor: Color(0xFF607D8B),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// مشاركة في تطبيقات أخرى
  void _shareToOtherApps(BuildContext context) {
    Navigator.of(context).pop();

    final shareText = 'Check out this post from ArzaTok:\n\n"${post.content}"\n\n${_getPostUrl()}';
    Share.share(shareText, subject: 'Post from ArzaTok');
  }

  /// الحصول على رابط المنشور
  String _getPostUrl() {
    return 'https://arzatok.app/post/${post.id}';
  }

  /// فتح رابط
  void _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
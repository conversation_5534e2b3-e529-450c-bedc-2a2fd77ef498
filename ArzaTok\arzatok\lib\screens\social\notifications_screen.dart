import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/notifications_service.dart';
import '../../models/notification_model.dart';

/// شاشة الإشعارات للشبكة الاجتماعية (مثل Facebook)
class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  void initState() {
    super.initState();
    // إضافة إشعارات تجريبية عند فتح الشاشة لأول مرة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notificationsService = Provider.of<NotificationsService>(context, listen: false);
      if (notificationsService.notifications.isEmpty) {
        notificationsService.addDemoNotifications();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        iconTheme: const IconThemeData(color: Color(0xFF1C1E21)),
        actions: [
          // زر تحديد الكل كمقروء
          Consumer<NotificationsService>(
            builder: (context, notificationsService, child) {
              if (notificationsService.unreadCount > 0) {
                return IconButton(
                  onPressed: () {
                    notificationsService.markAllAsRead();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('All notifications marked as read'),
                        backgroundColor: Color(0xFF42B883),
                      ),
                    );
                  },
                  icon: const Icon(Icons.done_all),
                  tooltip: 'Mark all as read',
                );
              }
              return const SizedBox.shrink();
            },
          ),
          
          // قائمة الخيارات
          PopupMenuButton<String>(
            onSelected: (value) {
              final notificationsService = Provider.of<NotificationsService>(context, listen: false);
              switch (value) {
                case 'clear_all':
                  _showClearAllDialog(notificationsService);
                  break;
                case 'settings':
                  _showNotificationSettings();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Color(0xFF65676B)),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<NotificationsService>(
        builder: (context, notificationsService, child) {
          final notifications = notificationsService.notifications;
          
          if (notifications.isEmpty) {
            return _buildEmptyState();
          }

          return RefreshIndicator(
            onRefresh: () async {
              // يمكن إضافة تحديث الإشعارات من الخادم هنا
              await Future.delayed(const Duration(seconds: 1));
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return _buildNotificationTile(notification, notificationsService);
              },
            ),
          );
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: Color(0xFF65676B),
          ),
          SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1C1E21),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'When you get notifications, they\'ll show up here',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF65676B),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الإشعار
  Widget _buildNotificationTile(NotificationModel notification, NotificationsService service) {
    final isUnread = !notification.isRead;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isUnread ? const Color(0xFFE3F2FD) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isUnread ? Border.all(color: const Color(0xFF1877F2), width: 1) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Stack(
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFF1877F2),
              backgroundImage: notification.fromUserAvatar.isNotEmpty
                  ? NetworkImage(notification.fromUserAvatar)
                  : null,
              child: notification.fromUserAvatar.isEmpty
                  ? Text(
                      notification.fromUserName.isNotEmpty
                          ? notification.fromUserName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            
            // أيقونة نوع الإشعار
            Positioned(
              bottom: -2,
              right: -2,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification.type),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: Center(
                  child: Text(
                    notification.iconEmoji,
                    style: const TextStyle(fontSize: 10),
                  ),
                ),
              ),
            ),
          ],
        ),
        
        title: RichText(
          text: TextSpan(
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1C1E21),
            ),
            children: [
              TextSpan(
                text: notification.fromUserName,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextSpan(
                text: ' ${_getActionText(notification.type)}',
                style: const TextStyle(
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
        
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.timeAgo,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF65676B),
              ),
            ),
            
            // نقطة عدم القراءة
            if (isUnread) ...[
              const SizedBox(height: 8),
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Color(0xFF1877F2),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
        
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'mark_read':
                service.markAsRead(notification.id);
                break;
              case 'delete':
                service.deleteNotification(notification.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Notification deleted'),
                    backgroundColor: Colors.red,
                  ),
                );
                break;
            }
          },
          itemBuilder: (context) => [
            if (isUnread)
              const PopupMenuItem(
                value: 'mark_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read, size: 20),
                    SizedBox(width: 8),
                    Text('Mark as read'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          child: const Icon(
            Icons.more_vert,
            color: Color(0xFF65676B),
          ),
        ),
        
        onTap: () {
          // تحديد كمقروء عند النقر
          if (isUnread) {
            service.markAsRead(notification.id);
          }
          
          // الانتقال للمنشور أو التعليق
          _handleNotificationTap(notification);
        },
      ),
    );
  }

  /// الحصول على لون الإشعار
  Color _getNotificationColor(String type) {
    switch (type) {
      case 'like':
        return const Color(0xFF1877F2);
      case 'love':
        return const Color(0xFFE91E63);
      case 'comment':
      case 'reply':
        return const Color(0xFF42B883);
      case 'share':
        return const Color(0xFFFF9800);
      case 'post':
      case 'story':
        return const Color(0xFF9C27B0);
      default:
        return const Color(0xFF65676B);
    }
  }

  /// الحصول على نص الإجراء
  String _getActionText(String type) {
    switch (type) {
      case 'like':
        return 'liked your post';
      case 'love':
        return 'loved your post';
      case 'comment':
        return 'commented on your post';
      case 'reply':
        return 'replied to your comment';
      case 'share':
        return 'shared your post';
      case 'post':
        return 'shared a new post';
      case 'story':
        return 'added a new story';
      default:
        return 'interacted with your content';
    }
  }

  /// التعامل مع النقر على الإشعار
  void _handleNotificationTap(NotificationModel notification) {
    // يمكن إضافة التنقل للمنشور أو التعليق هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${notification.type} notification'),
        backgroundColor: const Color(0xFF1877F2),
      ),
    );
  }

  /// عرض حوار مسح جميع الإشعارات
  void _showClearAllDialog(NotificationsService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to delete all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              service.clearAllNotifications();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All notifications cleared'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات الإشعارات
  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: const Text('Notification settings will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

/// ألوان الخلفيات المميزة مثل Facebook
class FacebookBackgroundColors {
  static const List<BackgroundColorOption> colors = [
    // بدون خلفية (افتراضي)
    BackgroundColorOption(
      id: 'none',
      name: 'Default',
      primaryColor: Colors.transparent,
      secondaryColor: Colors.transparent,
      textColor: Colors.black,
      isGradient: false,
    ),
    
    // أحمر كلاسيكي
    BackgroundColorOption(
      id: 'red_classic',
      name: 'Red Classic',
      primaryColor: Color(0xFFE53E3E),
      secondaryColor: Color(0xFFFC8181),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // أحمر داكن
    BackgroundColorOption(
      id: 'red_dark',
      name: 'Red Dark',
      primaryColor: Color(0xFF9B2C2C),
      secondaryColor: Color(0xFFE53E3E),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // أزرق فيسبوك
    BackgroundColorOption(
      id: 'facebook_blue',
      name: 'Facebook Blue',
      primaryColor: Color(0xFF1877F2),
      secondaryColor: Color(0xFF42A5F5),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // أزرق داكن
    BackgroundColorOption(
      id: 'blue_dark',
      name: 'Blue Dark',
      primaryColor: Color(0xFF1A365D),
      secondaryColor: Color(0xFF2B77AD),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // أخضر طبيعي
    BackgroundColorOption(
      id: 'green_nature',
      name: 'Green Nature',
      primaryColor: Color(0xFF38A169),
      secondaryColor: Color(0xFF68D391),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // بنفسجي ملكي
    BackgroundColorOption(
      id: 'purple_royal',
      name: 'Purple Royal',
      primaryColor: Color(0xFF553C9A),
      secondaryColor: Color(0xFF9F7AEA),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // وردي رومانسي
    BackgroundColorOption(
      id: 'pink_romantic',
      name: 'Pink Romantic',
      primaryColor: Color(0xFFD53F8C),
      secondaryColor: Color(0xFFF687B3),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // برتقالي غروب
    BackgroundColorOption(
      id: 'orange_sunset',
      name: 'Orange Sunset',
      primaryColor: Color(0xFFDD6B20),
      secondaryColor: Color(0xFFFBD38D),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // أصفر مشمس
    BackgroundColorOption(
      id: 'yellow_sunny',
      name: 'Yellow Sunny',
      primaryColor: Color(0xFFD69E2E),
      secondaryColor: Color(0xFFF6E05E),
      textColor: Colors.black,
      isGradient: true,
    ),
    
    // رمادي أنيق
    BackgroundColorOption(
      id: 'gray_elegant',
      name: 'Gray Elegant',
      primaryColor: Color(0xFF4A5568),
      secondaryColor: Color(0xFF718096),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // أسود كلاسيكي
    BackgroundColorOption(
      id: 'black_classic',
      name: 'Black Classic',
      primaryColor: Color(0xFF1A202C),
      secondaryColor: Color(0xFF2D3748),
      textColor: Colors.white,
      isGradient: true,
    ),
    
    // تدرج قوس قزح
    BackgroundColorOption(
      id: 'rainbow_gradient',
      name: 'Rainbow',
      primaryColor: Color(0xFFE53E3E),
      secondaryColor: Color(0xFF9F7AEA),
      textColor: Colors.white,
      isGradient: true,
      customGradient: LinearGradient(
        colors: [
          Color(0xFFE53E3E), // أحمر
          Color(0xFFDD6B20), // برتقالي
          Color(0xFFD69E2E), // أصفر
          Color(0xFF38A169), // أخضر
          Color(0xFF1877F2), // أزرق
          Color(0xFF553C9A), // بنفسجي
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
  ];

  /// الحصول على لون بالمعرف
  static BackgroundColorOption? getById(String id) {
    try {
      return colors.firstWhere((color) => color.id == id);
    } catch (e) {
      return colors.first; // العودة للافتراضي
    }
  }
}

/// نموذج خيار لون الخلفية
class BackgroundColorOption {
  final String id;
  final String name;
  final Color primaryColor;
  final Color secondaryColor;
  final Color textColor;
  final bool isGradient;
  final LinearGradient? customGradient;

  const BackgroundColorOption({
    required this.id,
    required this.name,
    required this.primaryColor,
    required this.secondaryColor,
    required this.textColor,
    this.isGradient = false,
    this.customGradient,
  });

  /// الحصول على التدرج
  LinearGradient get gradient {
    if (customGradient != null) {
      return customGradient!;
    }
    
    if (isGradient) {
      return LinearGradient(
        colors: [primaryColor, secondaryColor],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
    
    return LinearGradient(
      colors: [primaryColor, primaryColor],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  /// الحصول على الخلفية كـ BoxDecoration
  BoxDecoration get decoration {
    if (id == 'none') {
      return const BoxDecoration(
        color: Colors.transparent,
      );
    }
    
    return BoxDecoration(
      gradient: gradient,
      borderRadius: BorderRadius.circular(12),
    );
  }
}

/// ويدجت اختيار لون الخلفية
class FacebookStyleBackgroundSelector extends StatefulWidget {
  final String? selectedColorId;
  final Function(BackgroundColorOption) onColorSelected;
  final bool showTitle;

  const FacebookStyleBackgroundSelector({
    super.key,
    this.selectedColorId,
    required this.onColorSelected,
    this.showTitle = true,
  });

  @override
  State<FacebookStyleBackgroundSelector> createState() => _FacebookStyleBackgroundSelectorState();
}

class _FacebookStyleBackgroundSelectorState extends State<FacebookStyleBackgroundSelector> {
  String? _selectedColorId;

  @override
  void initState() {
    super.initState();
    _selectedColorId = widget.selectedColorId ?? 'none';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) ...[
          const Text(
            'Background Color',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
        ],
        
        // شبكة الألوان
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: FacebookBackgroundColors.colors.length,
          itemBuilder: (context, index) {
            final colorOption = FacebookBackgroundColors.colors[index];
            final isSelected = _selectedColorId == colorOption.id;
            
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedColorId = colorOption.id;
                });
                widget.onColorSelected(colorOption);
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? const Color(0xFFD32F2F) : Colors.grey[300]!,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    decoration: colorOption.id == 'none' 
                        ? BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Colors.grey[300]!),
                          )
                        : colorOption.decoration,
                    child: colorOption.id == 'none'
                        ? const Center(
                            child: Text(
                              'Aa',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black54,
                              ),
                            ),
                          )
                        : Center(
                            child: Text(
                              'Aa',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: colorOption.textColor,
                              ),
                            ),
                          ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// ويدجت معاينة النص مع الخلفية
class BackgroundPreviewWidget extends StatelessWidget {
  final String text;
  final BackgroundColorOption backgroundOption;
  final double? height;
  final TextAlign textAlign;
  final double fontSize;

  const BackgroundPreviewWidget({
    super.key,
    required this.text,
    required this.backgroundOption,
    this.height,
    this.textAlign = TextAlign.center,
    this.fontSize = 18,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: height ?? 120,
      decoration: backgroundOption.decoration,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            text.isEmpty ? 'What\'s on your mind?' : text,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w500,
              color: backgroundOption.textColor,
              height: 1.4,
            ),
            textAlign: textAlign,
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../services/users_service.dart';
import '../services/auth_service.dart';
import '../screens/profile/simple_user_profile_screen.dart';
import 'premium_ui_enhancements.dart';

/// نظام التنقل الشامل للملفات الشخصية
class UniversalProfileNavigator {

  /// التنقل إلى ملف شخصي من أي مكان في التطبيق
  static Future<void> navigateToProfile({
    required BuildContext context,
    required String userId,
    String? userName,
    String? userAvatar,
    bool showLoadingIndicator = true,
  }) async {
    if (showLoadingIndicator) {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1877F2)),
          ),
        ),
      );
    }

    try {
      final usersService = Provider.of<UsersService>(context, listen: false);
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;

      // الحصول على بيانات المستخدم الحقيقية
      UserModel? user = await usersService.getUserByPhone(userId);

      if (showLoadingIndicator && context.mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      }

      if (!context.mounted) return;

      user ??= UserModel(
          phoneNumber: userId,
          name: userName ?? 'Unknown User',
          gender: Gender.male,
          age: 25,
          country: 'Unknown',
          city: 'Unknown',
          isOnline: false,
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
          profileImageUrl: userAvatar,
        );

      final isCurrentUser = currentUser?.phoneNumber == userId;

      // التنقل إلى الملف الشخصي
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SimpleUserProfileScreen(
            user: user!,
          ),
        ),
      );
    } catch (e) {
      if (showLoadingIndicator && context.mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error loading profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// ويدجت قابل للنقر للملف الشخصي
class ClickableProfileWidget extends StatelessWidget {
  final String userId;
  final String? userName;
  final String? userAvatar;
  final Widget child;
  final bool enableNavigation;

  const ClickableProfileWidget({
    super.key,
    required this.userId,
    this.userName,
    this.userAvatar,
    required this.child,
    this.enableNavigation = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!enableNavigation) {
      return child;
    }

    return PremiumUIEnhancements.premiumTapEffect(
      onTap: () => UniversalProfileNavigator.navigateToProfile(
        context: context,
        userId: userId,
        userName: userName,
        userAvatar: userAvatar,
      ),
      child: child,
    );
  }
}

/// صورة ملف شخصي قابلة للنقر
class ClickableProfileAvatar extends StatelessWidget {
  final String userId;
  final String? userName;
  final String? userAvatar;
  final double radius;
  final bool enableNavigation;

  const ClickableProfileAvatar({
    super.key,
    required this.userId,
    this.userName,
    this.userAvatar,
    this.radius = 20,
    this.enableNavigation = true,
  });

  @override
  Widget build(BuildContext context) {
    final avatar = CircleAvatar(
      radius: radius,
      backgroundImage: userAvatar != null && userAvatar!.isNotEmpty
          ? NetworkImage(userAvatar!)
          : null,
      backgroundColor: const Color(0xFF1877F2),
      child: userAvatar == null || userAvatar!.isEmpty
          ? Icon(
              Icons.person,
              size: radius * 0.8,
              color: Colors.white,
            )
          : null,
    );

    if (!enableNavigation) {
      return avatar;
    }

    return ClickableProfileWidget(
      userId: userId,
      userName: userName,
      userAvatar: userAvatar,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: avatar,
      ),
    );
  }
}

/// اسم مستخدم قابل للنقر
class ClickableUserName extends StatelessWidget {
  final String userId;
  final String userName;
  final String? userAvatar;
  final TextStyle? textStyle;
  final bool enableNavigation;

  const ClickableUserName({
    super.key,
    required this.userId,
    required this.userName,
    this.userAvatar,
    this.textStyle,
    this.enableNavigation = true,
  });

  @override
  Widget build(BuildContext context) {
    final nameWidget = Text(
      userName,
      style: textStyle ?? const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Color(0xFF1877F2),
      ),
    );

    if (!enableNavigation) {
      return nameWidget;
    }

    return ClickableProfileWidget(
      userId: userId,
      userName: userName,
      userAvatar: userAvatar,
      child: nameWidget,
    );
  }
}

/// رأس ملف شخصي قابل للنقر (صورة + اسم)
class ClickableProfileHeader extends StatelessWidget {
  final String userId;
  final String userName;
  final String? userAvatar;
  final String? subtitle;
  final double avatarRadius;
  final bool enableNavigation;
  final Widget? trailing;

  const ClickableProfileHeader({
    super.key,
    required this.userId,
    required this.userName,
    this.userAvatar,
    this.subtitle,
    this.avatarRadius = 20,
    this.enableNavigation = true,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    final headerWidget = Row(
      children: [
        ClickableProfileAvatar(
          userId: userId,
          userName: userName,
          userAvatar: userAvatar,
          radius: avatarRadius,
          enableNavigation: enableNavigation,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClickableUserName(
                userId: userId,
                userName: userName,
                userAvatar: userAvatar,
                enableNavigation: enableNavigation,
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1C1E21),
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        if (trailing != null) trailing!,
      ],
    );

    return headerWidget;
  }
}

/// مؤشر الحالة (متصل/غير متصل)
class OnlineStatusIndicator extends StatelessWidget {
  final bool isOnline;
  final DateTime? lastSeen;

  const OnlineStatusIndicator({
    super.key,
    required this.isOnline,
    this.lastSeen,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isOnline ? Colors.green : Colors.grey,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          isOnline ? 'Online' : _getLastSeenText(),
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _getLastSeenText() {
    if (lastSeen == null) return 'Offline';

    final now = DateTime.now();
    final difference = now.difference(lastSeen!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return 'Long time ago';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'firebase_options.dart';

/// صفحة اختبار Firebase
class TestFirebasePage extends StatefulWidget {
  const TestFirebasePage({super.key});

  @override
  State<TestFirebasePage> createState() => _TestFirebasePageState();
}

class _TestFirebasePageState extends State<TestFirebasePage> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  String _status = 'جاري التحقق...';
  final List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _testFirebase();
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    debugPrint(message);
  }

  Future<void> _testFirebase() async {
    try {
      _addLog('🔄 بدء اختبار Firebase...');

      // اختبار الاتصال
      _addLog('🔗 اختبار الاتصال بـ Firestore...');
      final testDoc = await _firestore.collection('test').doc('connection').get();
      _addLog('✅ الاتصال بـ Firestore نجح');

      // اختبار الكتابة
      _addLog('📝 اختبار الكتابة...');
      await _firestore.collection('test').doc('write_test').set({
        'message': 'Test write from ArzaTalk',
        'timestamp': FieldValue.serverTimestamp(),
        'app': 'ArzaTalk',
      });
      _addLog('✅ الكتابة نجحت');

      // اختبار القراءة
      _addLog('📖 اختبار القراءة...');
      final writeTestDoc = await _firestore.collection('test').doc('write_test').get();
      if (writeTestDoc.exists) {
        _addLog('✅ القراءة نجحت: ${writeTestDoc.data()}');
      } else {
        _addLog('❌ فشل في القراءة');
      }

      // اختبار مجموعة posts
      _addLog('📝 اختبار مجموعة posts...');
      final postsSnapshot = await _firestore.collection('posts').get();
      _addLog('📊 عدد المنشورات الموجودة: ${postsSnapshot.docs.length}');

      for (final doc in postsSnapshot.docs) {
        _addLog('📄 منشور: ${doc.id} - ${doc.data()}');
      }

      // اختبار إنشاء منشور جديد
      _addLog('🆕 اختبار إنشاء منشور جديد...');
      final testPostId = 'test_${DateTime.now().millisecondsSinceEpoch}';
      await _firestore.collection('posts').doc(testPostId).set({
        'id': testPostId,
        'authorId': '+212638813823',
        'authorName': 'مستخدم اختبار',
        'authorAvatar': '',
        'content': 'هذا منشور اختبار من Firebase',
        'images': [],
        'videos': [],
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'type': 'text',
        'privacy': 'public',
        'reactions': {},
        'commentsCount': 0,
        'sharesCount': 0,
        'isDeleted': false,
        'isEdited': false,
        'location': null,
        'tags': [],
        'musicTitle': null,
        'musicUrl': null,
        'feeling': null,
        'backgroundColor': null,
        'taggedUsers': [],
        'isLiveStream': false,
        'originalPostId': null,
        'isRepost': false,
      });
      _addLog('✅ تم إنشاء منشور اختبار: $testPostId');

      // التحقق من المنشور الجديد
      final newPostDoc = await _firestore.collection('posts').doc(testPostId).get();
      if (newPostDoc.exists) {
        _addLog('✅ تم التحقق من المنشور الجديد');
        final postData = newPostDoc.data();
        _addLog('📄 بيانات المنشور: $postData');
      } else {
        _addLog('❌ فشل في العثور على المنشور الجديد');
      }

      // اختبار Firebase Storage
      _addLog('🗂️ اختبار Firebase Storage...');
      try {
        final storageRef = FirebaseStorage.instance.ref().child('test/test_file.txt');
        final testData = 'Test data from ArzaTalk ${DateTime.now()}';
        await storageRef.putString(testData);
        final downloadUrl = await storageRef.getDownloadURL();
        _addLog('✅ Firebase Storage يعمل: $downloadUrl');
      } catch (storageError) {
        _addLog('❌ خطأ في Firebase Storage: $storageError');
      }

      setState(() {
        _status = '✅ جميع الاختبارات نجحت!';
      });

    } catch (e) {
      _addLog('❌ خطأ في Firebase: $e');
      setState(() {
        _status = '❌ فشل في الاختبار';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار Firebase'),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _status,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سجل الأحداث:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        _logs[index],
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _logs.clear();
                        _status = 'جاري إعادة الاختبار...';
                      });
                      _testFirebase();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFD32F2F),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('إعادة الاختبار'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('العودة'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

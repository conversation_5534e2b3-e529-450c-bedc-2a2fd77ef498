import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/privacy_settings_model.dart';
import '../../services/auth_service.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  late PrivacySettings _privacySettings;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final authService = Provider.of<AuthService>(context, listen: false);
    _privacySettings = authService.currentUser?.privacySettings ?? const PrivacySettings();
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.updatePrivacySettings(_privacySettings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Privacy settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Settings'),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveSettings,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.privacy_tip, color: Colors.blue[700]),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Control Your Privacy',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Choose what information other users can see about you. Your name will always be visible.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Privacy Settings
            const Text(
              'Who can see my information:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFFD32F2F),
              ),
            ),

            const SizedBox(height: 16),

            // Phone Number
            _buildPrivacyTile(
              icon: Icons.phone,
              title: 'Phone Number',
              subtitle: 'Your phone number will be hidden from other users',
              value: _privacySettings.showPhoneNumber,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showPhoneNumber: value);
                });
              },
            ),

            // Age
            _buildPrivacyTile(
              icon: Icons.cake,
              title: 'Age',
              subtitle: 'Your age will be hidden from other users',
              value: _privacySettings.showAge,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showAge: value);
                });
              },
            ),

            // Gender
            _buildPrivacyTile(
              icon: Icons.person_outline,
              title: 'Gender',
              subtitle: 'Your gender will be hidden from other users',
              value: _privacySettings.showGender,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showGender: value);
                });
              },
            ),

            // Location
            _buildPrivacyTile(
              icon: Icons.location_on,
              title: 'Location (Country & City)',
              subtitle: 'Your location will be hidden from other users',
              value: _privacySettings.showLocation,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showLocation: value);
                });
              },
            ),

            // Registration Date
            _buildPrivacyTile(
              icon: Icons.calendar_today,
              title: 'Registration Date',
              subtitle: 'When you joined ArzaTalk will be hidden',
              value: _privacySettings.showRegistrationDate,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showRegistrationDate: value);
                });
              },
            ),

            const SizedBox(height: 24),

            // Online Status Section
            const Text(
              'Online Status:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFFD32F2F),
              ),
            ),

            const SizedBox(height: 16),

            // Last Seen
            _buildPrivacyTile(
              icon: Icons.access_time,
              title: 'Last Seen',
              subtitle: 'When you were last online will be hidden',
              value: _privacySettings.showLastSeen,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showLastSeen: value);
                });
              },
            ),

            // Online Status
            _buildPrivacyTile(
              icon: Icons.circle,
              title: 'Online Status',
              subtitle: 'Your online/offline status will be hidden',
              value: _privacySettings.showOnlineStatus,
              onChanged: (value) {
                setState(() {
                  _privacySettings = _privacySettings.copyWith(showOnlineStatus: value);
                });
              },
            ),

            const SizedBox(height: 30),

            // Save Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFD32F2F),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'Save Privacy Settings',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 16),

            // Note
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange[700], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Note: Your name will always be visible to other users for identification purposes.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: SwitchListTile(
        secondary: Icon(icon, color: const Color(0xFFD32F2F)),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFFD32F2F),
      ),
    );
  }
}

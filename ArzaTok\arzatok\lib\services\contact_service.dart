import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/contact_model.dart';
import 'auth_service.dart';

class ContactService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static final List<ContactModel> _contacts = [];
  static final List<String> _blockedUsers = [];

  List<ContactModel> get contacts => _contacts;
  List<String> get blockedUsers => _blockedUsers;

  // Add contact
  Future<void> addContact(ContactModel contact) async {
    try {
      final existingIndex = _contacts.indexWhere(
        (c) => c.phoneNumber == contact.phoneNumber,
      );

      if (existingIndex != -1) {
        _contacts[existingIndex] = contact;
      } else {
        _contacts.add(contact);
      }

      notifyListeners();
    } catch (e) {
      throw Exception('Failed to add contact: $e');
    }
  }

  // Get all contacts
  List<ContactModel> getAllContacts() {
    return _contacts..sort((a, b) => a.name.compareTo(b.name));
  }

  // Get contacts on ArzaTok
  List<ContactModel> getContactsOnArzaTok() {
    return _contacts.where((contact) => contact.isOnArzaTalk).toList()
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  // Search contacts
  List<ContactModel> searchContacts(String query) {
    if (query.isEmpty) return getAllContacts();

    return _contacts.where((contact) =>
      contact.name.toLowerCase().contains(query.toLowerCase()) ||
      contact.phoneNumber.contains(query)
    ).toList();
  }

  // Block user
  Future<void> blockUser(String phoneNumber) async {
    try {
      if (!_blockedUsers.contains(phoneNumber)) {
        _blockedUsers.add(phoneNumber);

        // Update contact if exists
        final contactIndex = _contacts.indexWhere(
          (c) => c.phoneNumber == phoneNumber,
        );
        if (contactIndex != -1) {
          _contacts[contactIndex] = _contacts[contactIndex].copyWith(
            isBlocked: true,
          );
        }

        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to block user: $e');
    }
  }

  // Unblock user
  Future<void> unblockUser(String phoneNumber) async {
    try {
      _blockedUsers.remove(phoneNumber);

      // Update contact if exists
      final contactIndex = _contacts.indexWhere(
        (c) => c.phoneNumber == phoneNumber,
      );
      if (contactIndex != -1) {
        _contacts[contactIndex] = _contacts[contactIndex].copyWith(
          isBlocked: false,
        );
      }

      notifyListeners();
    } catch (e) {
      throw Exception('Failed to unblock user: $e');
    }
  }

  // Check if user is blocked
  bool isUserBlocked(String phoneNumber) {
    return _blockedUsers.contains(phoneNumber);
  }

  // Mute contact
  Future<void> muteContact(String phoneNumber) async {
    try {
      final contactIndex = _contacts.indexWhere(
        (c) => c.phoneNumber == phoneNumber,
      );
      if (contactIndex != -1) {
        _contacts[contactIndex] = _contacts[contactIndex].copyWith(
          isMuted: true,
        );
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to mute contact: $e');
    }
  }

  // Unmute contact
  Future<void> unmuteContact(String phoneNumber) async {
    try {
      final contactIndex = _contacts.indexWhere(
        (c) => c.phoneNumber == phoneNumber,
      );
      if (contactIndex != -1) {
        _contacts[contactIndex] = _contacts[contactIndex].copyWith(
          isMuted: false,
        );
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to unmute contact: $e');
    }
  }

  // Check if contact is muted
  bool isContactMuted(String phoneNumber) {
    final contact = _contacts.firstWhere(
      (c) => c.phoneNumber == phoneNumber,
      orElse: () => ContactModel(phoneNumber: phoneNumber, name: ''),
    );
    return contact.isMuted;
  }

  // Update contact info
  Future<void> updateContact({
    required String phoneNumber,
    String? name,
    String? profileImageUrl,
    String? status,
  }) async {
    try {
      final contactIndex = _contacts.indexWhere(
        (c) => c.phoneNumber == phoneNumber,
      );

      if (contactIndex != -1) {
        _contacts[contactIndex] = _contacts[contactIndex].copyWith(
          name: name,
          profileImageUrl: profileImageUrl,
          status: status,
        );
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to update contact: $e');
    }
  }

  // Delete contact
  Future<void> deleteContact(String phoneNumber) async {
    try {
      _contacts.removeWhere((c) => c.phoneNumber == phoneNumber);
      notifyListeners();
    } catch (e) {
      throw Exception('Failed to delete contact: $e');
    }
  }

  // Sync with ArzaTalk users from Firestore
  Future<void> syncWithArzaTalkUsers() async {
    try {
      // تحميل المستخدمين من Firestore
      final snapshot = await _firestore.collection('users').get();

      for (final doc in snapshot.docs) {
        final userData = doc.data();
        final phoneNumber = doc.id;

        final existingContactIndex = _contacts.indexWhere(
          (c) => c.phoneNumber == phoneNumber,
        );

        if (existingContactIndex != -1) {
          // Update existing contact
          _contacts[existingContactIndex] = _contacts[existingContactIndex].copyWith(
            name: userData['name'] ?? 'Unknown',
            profileImageUrl: userData['profileImageUrl'],
            isOnArzaTalk: true,
            lastSeen: userData['lastSeen'] != null
                ? DateTime.parse(userData['lastSeen'])
                : DateTime.now(),
            status: userData['status'],
          );
        } else {
          // Add new contact
          _contacts.add(ContactModel(
            phoneNumber: phoneNumber,
            name: userData['name'] ?? 'Unknown',
            profileImageUrl: userData['profileImageUrl'],
            isOnArzaTalk: true,
            lastSeen: userData['lastSeen'] != null
                ? DateTime.parse(userData['lastSeen'])
                : DateTime.now(),
            status: userData['status'],
          ));
        }
      }

      debugPrint('✅ Synced ${snapshot.docs.length} users from Firestore');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error syncing with Firestore users: $e');
      // العودة إلى البيانات المحلية في حالة الخطأ
      final allUsers = await AuthService.getAllUsersStatic();

      for (final user in allUsers) {
        final existingContactIndex = _contacts.indexWhere(
          (c) => c.phoneNumber == user.phoneNumber,
        );

        if (existingContactIndex != -1) {
          _contacts[existingContactIndex] = _contacts[existingContactIndex].copyWith(
            name: user.name,
            profileImageUrl: user.profileImageUrl,
            isOnArzaTalk: true,
            lastSeen: user.lastSeen,
            status: user.status,
          );
        } else {
          _contacts.add(ContactModel(
            phoneNumber: user.phoneNumber,
            name: user.name,
            profileImageUrl: user.profileImageUrl,
            isOnArzaTalk: true,
            lastSeen: user.lastSeen,
            status: user.status,
          ));
        }
      }

      notifyListeners();
    }
  }

  // Demo contacts disabled - using Firebase only
  static void addDemoContacts() {
    // Disabled - using Firebase only
    debugPrint('📱 Demo contacts disabled - using Firebase only');
  }

  // Get contact by phone number
  ContactModel? getContactByPhoneNumber(String phoneNumber) {
    try {
      return _contacts.firstWhere((c) => c.phoneNumber == phoneNumber);
    } catch (e) {
      return null;
    }
  }

  // Report user
  Future<void> reportUser(String phoneNumber, String reason) async {
    try {
      // In a real app, this would send a report to the server
      // For now, we'll just show a success message
      debugPrint('User $phoneNumber reported for: $reason');
    } catch (e) {
      throw Exception('Failed to report user: $e');
    }
  }
}

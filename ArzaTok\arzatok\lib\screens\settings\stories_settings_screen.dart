import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// شاشة إعدادات القصص
class StoriesSettingsScreen extends StatefulWidget {
  const StoriesSettingsScreen({super.key});

  @override
  State<StoriesSettingsScreen> createState() => _StoriesSettingsScreenState();
}

class _StoriesSettingsScreenState extends State<StoriesSettingsScreen> {
  // إعدادات الخصوصية
  String _whoCanSeeStories = 'Everyone';
  String _whoCanReply = 'Everyone';
  bool _allowScreenshots = true;
  bool _showViewers = true;
  
  // إعدادات التحميل
  bool _autoSaveStories = false;
  bool _saveToGallery = true;
  String _storyQuality = 'High';
  
  // إعدادات الإشعارات
  bool _storyNotifications = true;
  bool _viewNotifications = true;
  bool _replyNotifications = true;
  
  // إعدادات التشغيل
  bool _autoPlayStories = true;
  bool _muteByDefault = false;
  int _storyDuration = 24; // hours

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _whoCanSeeStories = prefs.getString('who_can_see_stories') ?? 'Everyone';
      _whoCanReply = prefs.getString('who_can_reply') ?? 'Everyone';
      _allowScreenshots = prefs.getBool('allow_screenshots') ?? true;
      _showViewers = prefs.getBool('show_viewers') ?? true;
      _autoSaveStories = prefs.getBool('auto_save_stories') ?? false;
      _saveToGallery = prefs.getBool('save_stories_to_gallery') ?? true;
      _storyQuality = prefs.getString('story_quality') ?? 'High';
      _storyNotifications = prefs.getBool('story_notifications') ?? true;
      _viewNotifications = prefs.getBool('view_notifications') ?? true;
      _replyNotifications = prefs.getBool('reply_notifications') ?? true;
      _autoPlayStories = prefs.getBool('auto_play_stories') ?? true;
      _muteByDefault = prefs.getBool('mute_by_default') ?? false;
      _storyDuration = prefs.getInt('story_duration') ?? 24;
    });
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('who_can_see_stories', _whoCanSeeStories);
    await prefs.setString('who_can_reply', _whoCanReply);
    await prefs.setBool('allow_screenshots', _allowScreenshots);
    await prefs.setBool('show_viewers', _showViewers);
    await prefs.setBool('auto_save_stories', _autoSaveStories);
    await prefs.setBool('save_stories_to_gallery', _saveToGallery);
    await prefs.setString('story_quality', _storyQuality);
    await prefs.setBool('story_notifications', _storyNotifications);
    await prefs.setBool('view_notifications', _viewNotifications);
    await prefs.setBool('reply_notifications', _replyNotifications);
    await prefs.setBool('auto_play_stories', _autoPlayStories);
    await prefs.setBool('mute_by_default', _muteByDefault);
    await prefs.setInt('story_duration', _storyDuration);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Stories Settings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF9C27B0),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // إعدادات الخصوصية
            _buildSectionCard(
              title: 'Privacy Settings',
              icon: Icons.privacy_tip,
              children: [
                _buildListTile(
                  title: 'Who can see my stories',
                  subtitle: _whoCanSeeStories,
                  onTap: () => _selectWhoCanSeeStories(),
                ),
                _buildListTile(
                  title: 'Who can reply to my stories',
                  subtitle: _whoCanReply,
                  onTap: () => _selectWhoCanReply(),
                ),
                _buildSwitchTile(
                  title: 'Allow Screenshots',
                  subtitle: 'Let others take screenshots of your stories',
                  value: _allowScreenshots,
                  onChanged: (value) {
                    setState(() => _allowScreenshots = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Show Viewers',
                  subtitle: 'Show who viewed your stories',
                  value: _showViewers,
                  onChanged: (value) {
                    setState(() => _showViewers = value);
                    _saveSettings();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات التحميل والحفظ
            _buildSectionCard(
              title: 'Download & Save',
              icon: Icons.download,
              children: [
                _buildSwitchTile(
                  title: 'Auto-save my stories',
                  subtitle: 'Automatically save stories you post',
                  value: _autoSaveStories,
                  onChanged: (value) {
                    setState(() => _autoSaveStories = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Save to Gallery',
                  subtitle: 'Save stories to device gallery',
                  value: _saveToGallery,
                  onChanged: (value) {
                    setState(() => _saveToGallery = value);
                    _saveSettings();
                  },
                ),
                _buildListTile(
                  title: 'Story Quality',
                  subtitle: _storyQuality,
                  onTap: () => _selectStoryQuality(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات الإشعارات
            _buildSectionCard(
              title: 'Notifications',
              icon: Icons.notifications,
              children: [
                _buildSwitchTile(
                  title: 'Story Notifications',
                  subtitle: 'Get notified about new stories',
                  value: _storyNotifications,
                  onChanged: (value) {
                    setState(() => _storyNotifications = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'View Notifications',
                  subtitle: 'Get notified when someone views your story',
                  value: _viewNotifications,
                  onChanged: (value) {
                    setState(() => _viewNotifications = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Reply Notifications',
                  subtitle: 'Get notified about story replies',
                  value: _replyNotifications,
                  onChanged: (value) {
                    setState(() => _replyNotifications = value);
                    _saveSettings();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات التشغيل
            _buildSectionCard(
              title: 'Playback Settings',
              icon: Icons.play_circle,
              children: [
                _buildSwitchTile(
                  title: 'Auto-play Stories',
                  subtitle: 'Automatically play stories in sequence',
                  value: _autoPlayStories,
                  onChanged: (value) {
                    setState(() => _autoPlayStories = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Mute by Default',
                  subtitle: 'Start stories with sound muted',
                  value: _muteByDefault,
                  onChanged: (value) {
                    setState(() => _muteByDefault = value);
                    _saveSettings();
                  },
                ),
                _buildListTile(
                  title: 'Story Duration',
                  subtitle: '$_storyDuration hours',
                  onTap: () => _selectStoryDuration(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF9C27B0)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF9C27B0),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// بناء مفتاح تبديل
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: const Color(0xFF9C27B0),
    );
  }

  /// بناء عنصر قائمة
  Widget _buildListTile({
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ),
      onTap: onTap,
    );
  }

  /// اختيار من يمكنه رؤية القصص
  void _selectWhoCanSeeStories() {
    final options = ['Everyone', 'Friends Only', 'Close Friends', 'Custom'];
    _showSelectionDialog('Who can see your stories', options, _whoCanSeeStories, (value) {
      setState(() => _whoCanSeeStories = value);
      _saveSettings();
    });
  }

  /// اختيار من يمكنه الرد على القصص
  void _selectWhoCanReply() {
    final options = ['Everyone', 'Friends Only', 'Close Friends', 'No One'];
    _showSelectionDialog('Who can reply to your stories', options, _whoCanReply, (value) {
      setState(() => _whoCanReply = value);
      _saveSettings();
    });
  }

  /// اختيار جودة القصص
  void _selectStoryQuality() {
    final options = ['Low', 'Medium', 'High', 'Original'];
    _showSelectionDialog('Select Story Quality', options, _storyQuality, (value) {
      setState(() => _storyQuality = value);
      _saveSettings();
    });
  }

  /// اختيار مدة القصص
  void _selectStoryDuration() {
    final options = ['12 hours', '24 hours', '48 hours', '7 days'];
    final currentOption = '$_storyDuration hours';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Story Duration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) => RadioListTile<String>(
            title: Text(option),
            value: option,
            groupValue: currentOption,
            onChanged: (value) {
              final hours = value!.contains('12') ? 12 : 
                           value.contains('24') ? 24 : 
                           value.contains('48') ? 48 : 168;
              setState(() => _storyDuration = hours);
              _saveSettings();
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF9C27B0),
          )).toList(),
        ),
      ),
    );
  }

  /// عرض حوار الاختيار
  void _showSelectionDialog(String title, List<String> options, String currentValue, Function(String) onSelected) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) => RadioListTile<String>(
            title: Text(option),
            value: option,
            groupValue: currentValue,
            onChanged: (value) {
              onSelected(value!);
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF9C27B0),
          )).toList(),
        ),
      ),
    );
  }
}

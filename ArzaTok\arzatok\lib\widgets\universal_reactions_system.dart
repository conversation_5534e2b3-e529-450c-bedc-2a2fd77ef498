import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'facebook_real_reactions.dart';
import 'facebook_reactions.dart';
import 'dart:math' as math;

/// نظام التفاعلات الموحد للمنشورات والتعليقات والردود
class UniversalReactionsSystem extends StatefulWidget {
  final String? currentReaction;
  final Function(String) onReactionChanged;
  final String contentId; // معرف المحتوى (منشور، تعليق، رد)
  final String contentType; // نوع المحتوى (post, comment, reply)
  final bool isCompact; // للتعليقات والردود
  final Color? customColor;

  const UniversalReactionsSystem({
    super.key,
    this.currentReaction,
    required this.onReactionChanged,
    required this.contentId,
    required this.contentType,
    this.isCompact = false,
    this.customColor,
  });

  @override
  State<UniversalReactionsSystem> createState() => _UniversalReactionsSystemState();
}

class _UniversalReactionsSystemState extends State<UniversalReactionsSystem>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;
  
  bool _showingReactions = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.08,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.25,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    if (widget.currentReaction != null) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(UniversalReactionsSystem oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.currentReaction != oldWidget.currentReaction) {
      if (widget.currentReaction != null) {
        _bounceController.forward().then((_) {
          _bounceController.reverse();
          _pulseController.repeat(reverse: true);
        });
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _bounceAnimation]),
        builder: (context, child) {
          final scale = _bounceAnimation.value * _pulseAnimation.value;
          
          return Transform.scale(
            scale: scale,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: widget.isCompact ? 8 : 12,
                vertical: widget.isCompact ? 4 : 6,
              ),
              decoration: BoxDecoration(
                color: _getButtonColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(widget.isCompact ? 12 : 16),
                border: widget.currentReaction != null
                    ? Border.all(color: _getButtonColor().withOpacity(0.3))
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة التفاعل المتحركة
                  _buildReactionIcon(),
                  
                  if (!widget.isCompact) ...[
                    const SizedBox(width: 6),
                    Text(
                      _getReactionText(),
                      style: TextStyle(
                        color: _getButtonColor(),
                        fontSize: widget.isCompact ? 11 : 13,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildReactionIcon() {
    if (widget.currentReaction == null) {
      return Container(
        padding: EdgeInsets.all(widget.isCompact ? 1 : 2),
        decoration: BoxDecoration(
          color: _getButtonColor().withOpacity(0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.thumb_up_outlined,
          size: widget.isCompact ? 12 : 16,
          color: _getButtonColor(),
        ),
      );
    }

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 2 * math.pi),
      builder: (context, rotation, child) {
        return Transform.rotate(
          angle: rotation * 0.05, // دوران خفيف
          child: Container(
            width: widget.isCompact ? 18 : 22,
            height: widget.isCompact ? 18 : 22,
            decoration: BoxDecoration(
              gradient: _getReactionGradient(),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: _getButtonColor().withOpacity(0.3),
                  blurRadius: widget.isCompact ? 3 : 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // حلقة متحركة
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 800),
                  tween: Tween(begin: 0.0, end: 2 * math.pi),
                  builder: (context, rotation, child) {
                    return Transform.rotate(
                      angle: rotation,
                      child: Container(
                        width: widget.isCompact ? 14 : 18,
                        height: widget.isCompact ? 14 : 18,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withOpacity(0.6),
                            width: 1,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                
                // الأيقونة الرئيسية
                Icon(
                  _getReactionIcon(),
                  color: Colors.white,
                  size: widget.isCompact ? 8 : 10,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getButtonColor() {
    if (widget.customColor != null) return widget.customColor!;
    
    if (widget.currentReaction == null) {
      return const Color(0xFF65676B);
    }

    switch (widget.currentReaction) {
      case 'like':
        return const Color(0xFF1877F2);
      case 'love':
        return const Color(0xFFE91E63);
      case 'haha':
        return const Color(0xFFFFC107);
      case 'wow':
        return const Color(0xFFFF9800);
      case 'sad':
        return const Color(0xFF607D8B);
      case 'angry':
        return const Color(0xFFFF5722);
      default:
        return const Color(0xFF65676B);
    }
  }

  String _getReactionText() {
    if (widget.currentReaction == null) {
      return 'Like';
    }

    switch (widget.currentReaction) {
      case 'like':
        return 'Like';
      case 'love':
        return 'Love';
      case 'haha':
        return 'Haha';
      case 'wow':
        return 'Wow';
      case 'sad':
        return 'Sad';
      case 'angry':
        return 'Angry';
      default:
        return 'Like';
    }
  }

  IconData _getReactionIcon() {
    switch (widget.currentReaction) {
      case 'like':
        return Icons.thumb_up;
      case 'love':
        return Icons.favorite;
      case 'haha':
        return Icons.sentiment_very_satisfied;
      case 'wow':
        return Icons.sentiment_very_satisfied;
      case 'sad':
        return Icons.sentiment_very_dissatisfied;
      case 'angry':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.thumb_up;
    }
  }

  LinearGradient _getReactionGradient() {
    switch (widget.currentReaction) {
      case 'like':
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'love':
        return const LinearGradient(
          colors: [Color(0xFFE91E63), Color(0xFFFF6B9D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'haha':
        return const LinearGradient(
          colors: [Color(0xFFFFC107), Color(0xFFFFD54F)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'wow':
        return const LinearGradient(
          colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'sad':
        return const LinearGradient(
          colors: [Color(0xFF607D8B), Color(0xFF90A4AE)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'angry':
        return const LinearGradient(
          colors: [Color(0xFFFF5722), Color(0xFFFF7043)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  void _handleTap() {
    HapticFeedback.lightImpact();
    
    if (widget.currentReaction == null) {
      // إضافة Like افتراضي
      widget.onReactionChanged('like');
    } else {
      // إزالة التفاعل الحالي
      widget.onReactionChanged('');
    }
  }

  void _handleLongPress() {
    if (_showingReactions) return;
    
    HapticFeedback.mediumImpact();
    _showReactionsOverlay();
  }

  void _showReactionsOverlay() {
    if (_overlayEntry != null) return;

    _showingReactions = true;
    
    // الحصول على موضع الزر الحالي
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final buttonSize = renderBox.size;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => FacebookRealReactions(
        currentReaction: widget.currentReaction,
        buttonPosition: buttonPosition,
        buttonSize: buttonSize,
        onReactionSelected: (reactionId) {
          widget.onReactionChanged(reactionId);
          _removeOverlay();
        },
        onClose: _removeOverlay,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _showingReactions = false;
    }
  }
}

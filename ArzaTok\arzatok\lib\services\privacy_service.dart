import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import '../models/privacy_settings_model.dart';

/// خدمة إدارة إعدادات الخصوصية
class PrivacyService extends ChangeNotifier {
  static final PrivacyService _instance = PrivacyService._internal();
  factory PrivacyService() => _instance;
  PrivacyService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Map<String, PrivacySettings> _userPrivacySettings = {};

  /// الحصول على إعدادات الخصوصية لمستخدم
  Future<PrivacySettings> getUserPrivacySettings(String userId) async {
    // التحقق من الكاش أولاً
    if (_userPrivacySettings.containsKey(userId)) {
      return _userPrivacySettings[userId]!;
    }

    try {
      // محاولة التحميل من Firebase
      final doc = await _firestore
          .collection('privacy_settings')
          .doc(userId)
          .get();

      PrivacySettings settings;
      if (doc.exists) {
        settings = PrivacySettings.fromJson(doc.data()!);
      } else {
        // إعدادات افتراضية
        settings = const PrivacySettings();
        // حفظ الإعدادات الافتراضية
        await _savePrivacySettings(userId, settings);
      }

      _userPrivacySettings[userId] = settings;
      return settings;
    } catch (e) {
      debugPrint('❌ Error loading privacy settings: $e');
      // إرجاع إعدادات افتراضية في حالة الخطأ
      const defaultSettings = PrivacySettings();
      _userPrivacySettings[userId] = defaultSettings;
      return defaultSettings;
    }
  }

  /// حفظ إعدادات الخصوصية
  Future<bool> savePrivacySettings(String userId, PrivacySettings settings) async {
    try {
      await _savePrivacySettings(userId, settings);
      _userPrivacySettings[userId] = settings;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('❌ Error saving privacy settings: $e');
      return false;
    }
  }

  Future<void> _savePrivacySettings(String userId, PrivacySettings settings) async {
    // حفظ في Firebase
    await _firestore
        .collection('privacy_settings')
        .doc(userId)
        .set(settings.toJson());

    // حفظ في التخزين المحلي كنسخة احتياطية
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('privacy_$userId', json.encode(settings.toJson()));
  }

  /// التحقق من إمكانية رؤية محتوى معين
  Future<bool> canViewContent({
    required String ownerId,
    required String viewerId,
    required ContentType contentType,
  }) async {
    // المالك يرى كل شيء
    if (ownerId == viewerId) return true;

    final settings = await getUserPrivacySettings(ownerId);
    return settings.canViewContent(contentType, viewerId, ownerId);
  }

  /// التحقق من إمكانية رؤية معلومات الملف الشخصي
  Future<bool> canViewProfileInfo({
    required String ownerId,
    required String viewerId,
    required String infoType,
  }) async {
    if (ownerId == viewerId) return true;

    final settings = await getUserPrivacySettings(ownerId);
    
    switch (infoType) {
      case 'phoneNumber':
        return settings.showPhoneNumber;
      case 'age':
        return settings.showAge;
      case 'gender':
        return settings.showGender;
      case 'location':
        return settings.showLocation;
      case 'registrationDate':
        return settings.showRegistrationDate;
      case 'lastSeen':
        return settings.showLastSeen;
      case 'onlineStatus':
        return settings.showOnlineStatus;
      default:
        return true;
    }
  }

  /// تحديث إعداد خصوصية واحد
  Future<bool> updatePrivacySetting({
    required String userId,
    required String settingKey,
    required dynamic value,
  }) async {
    try {
      final currentSettings = await getUserPrivacySettings(userId);
      PrivacySettings updatedSettings;

      switch (settingKey) {
        case 'showPhoneNumber':
          updatedSettings = currentSettings.copyWith(showPhoneNumber: value as bool);
          break;
        case 'showAge':
          updatedSettings = currentSettings.copyWith(showAge: value as bool);
          break;
        case 'showGender':
          updatedSettings = currentSettings.copyWith(showGender: value as bool);
          break;
        case 'showLocation':
          updatedSettings = currentSettings.copyWith(showLocation: value as bool);
          break;
        case 'showRegistrationDate':
          updatedSettings = currentSettings.copyWith(showRegistrationDate: value as bool);
          break;
        case 'showLastSeen':
          updatedSettings = currentSettings.copyWith(showLastSeen: value as bool);
          break;
        case 'showOnlineStatus':
          updatedSettings = currentSettings.copyWith(showOnlineStatus: value as bool);
          break;
        case 'photosVisibility':
          updatedSettings = currentSettings.copyWith(photosVisibility: value as ContentVisibility);
          break;
        case 'videosVisibility':
          updatedSettings = currentSettings.copyWith(videosVisibility: value as ContentVisibility);
          break;
        case 'linksVisibility':
          updatedSettings = currentSettings.copyWith(linksVisibility: value as ContentVisibility);
          break;
        case 'textPostsVisibility':
          updatedSettings = currentSettings.copyWith(textPostsVisibility: value as ContentVisibility);
          break;
        case 'sharedPostsVisibility':
          updatedSettings = currentSettings.copyWith(sharedPostsVisibility: value as ContentVisibility);
          break;
        case 'liveStreamsVisibility':
          updatedSettings = currentSettings.copyWith(liveStreamsVisibility: value as ContentVisibility);
          break;
        default:
          return false;
      }

      return await savePrivacySettings(userId, updatedSettings);
    } catch (e) {
      debugPrint('❌ Error updating privacy setting: $e');
      return false;
    }
  }

  /// الحصول على قائمة المحتوى المرئي للمستخدم
  Future<List<ContentType>> getVisibleContentTypes({
    required String ownerId,
    required String viewerId,
  }) async {
    final visibleTypes = <ContentType>[];

    for (final contentType in ContentType.values) {
      final canView = await canViewContent(
        ownerId: ownerId,
        viewerId: viewerId,
        contentType: contentType,
      );
      
      if (canView) {
        visibleTypes.add(contentType);
      }
    }

    return visibleTypes;
  }

  /// تحميل إعدادات متعددة المستخدمين (للتحسين)
  Future<void> preloadPrivacySettings(List<String> userIds) async {
    final futures = userIds
        .where((id) => !_userPrivacySettings.containsKey(id))
        .map((id) => getUserPrivacySettings(id));
    
    await Future.wait(futures);
  }

  /// مسح الكاش
  void clearCache() {
    _userPrivacySettings.clear();
    notifyListeners();
  }

  /// الحصول على إعدادات من الكاش فقط
  PrivacySettings? getCachedPrivacySettings(String userId) {
    return _userPrivacySettings[userId];
  }

  /// التحقق من وجود إعدادات في الكاش
  bool hasPrivacySettingsInCache(String userId) {
    return _userPrivacySettings.containsKey(userId);
  }

  /// إحصائيات الخصوصية
  Map<String, dynamic> getPrivacyStats() {
    return {
      'cachedUsers': _userPrivacySettings.length,
      'totalSettings': _userPrivacySettings.values
          .map((s) => [
                s.showPhoneNumber,
                s.showAge,
                s.showGender,
                s.showLocation,
                s.showRegistrationDate,
                s.showLastSeen,
                s.showOnlineStatus,
              ].where((setting) => setting).length)
          .fold(0, (a, b) => a + b),
    };
  }

  @override
  void dispose() {
    _userPrivacySettings.clear();
    super.dispose();
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vibration/vibration.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/notification_model.dart';

/// خدمة الإشعارات المحسنة للشبكة الاجتماعية
class NotificationsService extends ChangeNotifier {
  final List<NotificationModel> _notifications = [];
  int _unreadCount = 0;

  // إعدادات الإشعارات
  bool _notificationsEnabled = true;
  bool _messageNotifications = true;
  bool _socialNotifications = true;
  bool _storyNotifications = true;
  bool _groupNotifications = true;
  bool _callNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _ledEnabled = true;
  bool _showPreview = true;
  bool _quietHoursEnabled = false;
  String _notificationTone = 'default';
  String _quietHoursStart = '22:00';
  String _quietHoursEnd = '08:00';

  // مشغل الصوت
  final AudioPlayer _audioPlayer = AudioPlayer();

  /// الحصول على جميع الإشعارات
  List<NotificationModel> get notifications => List.unmodifiable(_notifications);

  /// الحصول على عدد الإشعارات غير المقروءة
  int get unreadCount => _unreadCount;

  /// الحصول على الإشعارات غير المقروءة فقط
  List<NotificationModel> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();

  // Getters للإعدادات
  bool get notificationsEnabled => _notificationsEnabled;
  bool get messageNotifications => _messageNotifications;
  bool get socialNotifications => _socialNotifications;
  bool get storyNotifications => _storyNotifications;
  bool get groupNotifications => _groupNotifications;
  bool get callNotifications => _callNotifications;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  bool get ledEnabled => _ledEnabled;
  bool get showPreview => _showPreview;
  bool get quietHoursEnabled => _quietHoursEnabled;
  String get notificationTone => _notificationTone;
  String get quietHoursStart => _quietHoursStart;
  String get quietHoursEnd => _quietHoursEnd;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadSettings();
    debugPrint('🔔 NotificationsService initialized');
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _messageNotifications = prefs.getBool('message_notifications') ?? true;
      _socialNotifications = prefs.getBool('social_notifications') ?? true;
      _storyNotifications = prefs.getBool('story_notifications') ?? true;
      _groupNotifications = prefs.getBool('group_notifications') ?? true;
      _callNotifications = prefs.getBool('call_notifications') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      _ledEnabled = prefs.getBool('led_enabled') ?? true;
      _showPreview = prefs.getBool('show_preview') ?? true;
      _quietHoursEnabled = prefs.getBool('quiet_hours_enabled') ?? false;
      _notificationTone = prefs.getString('notification_tone') ?? 'default';
      _quietHoursStart = prefs.getString('quiet_hours_start') ?? '22:00';
      _quietHoursEnd = prefs.getString('quiet_hours_end') ?? '08:00';
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error loading notification settings: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('message_notifications', _messageNotifications);
      await prefs.setBool('social_notifications', _socialNotifications);
      await prefs.setBool('story_notifications', _storyNotifications);
      await prefs.setBool('group_notifications', _groupNotifications);
      await prefs.setBool('call_notifications', _callNotifications);
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('vibration_enabled', _vibrationEnabled);
      await prefs.setBool('led_enabled', _ledEnabled);
      await prefs.setBool('show_preview', _showPreview);
      await prefs.setBool('quiet_hours_enabled', _quietHoursEnabled);
      await prefs.setString('notification_tone', _notificationTone);
      await prefs.setString('quiet_hours_start', _quietHoursStart);
      await prefs.setString('quiet_hours_end', _quietHoursEnd);
    } catch (e) {
      debugPrint('❌ Error saving notification settings: $e');
    }
  }

  /// تعيين إعدادات الإشعارات
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setMessageNotifications(bool enabled) async {
    _messageNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setSocialNotifications(bool enabled) async {
    _socialNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setStoryNotifications(bool enabled) async {
    _storyNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setGroupNotifications(bool enabled) async {
    _groupNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setCallNotifications(bool enabled) async {
    _callNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setLedEnabled(bool enabled) async {
    _ledEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setShowPreview(bool enabled) async {
    _showPreview = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setQuietHoursEnabled(bool enabled) async {
    _quietHoursEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setNotificationTone(String tone) async {
    _notificationTone = tone;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setQuietHours(String start, String end) async {
    _quietHoursStart = start;
    _quietHoursEnd = end;
    await _saveSettings();
    notifyListeners();
  }

  /// التحقق من وقت الهدوء
  bool _isQuietTime() {
    if (!_quietHoursEnabled) return false;

    final now = TimeOfDay.now();
    final start = _parseTimeOfDay(_quietHoursStart);
    final end = _parseTimeOfDay(_quietHoursEnd);

    if (start.hour < end.hour) {
      return now.hour >= start.hour && now.hour < end.hour;
    } else {
      return now.hour >= start.hour || now.hour < end.hour;
    }
  }

  TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  /// تشغيل الصوت والاهتزاز
  Future<void> _playNotificationEffects() async {
    if (_isQuietTime()) return;

    // تشغيل الصوت
    if (_soundEnabled) {
      try {
        await _audioPlayer.play(AssetSource('sounds/message_default.mp3'));
      } catch (e) {
        debugPrint('❌ Error playing notification sound: $e');
      }
    }

    // تشغيل الاهتزاز
    if (_vibrationEnabled) {
      try {
        final hasVibrator = await Vibration.hasVibrator();
        if (hasVibrator == true) {
          Vibration.vibrate(duration: 200);
        }
      } catch (e) {
        debugPrint('❌ Error vibrating: $e');
      }
    }
  }

  /// إضافة إشعار جديد
  void addNotification(NotificationModel notification) {
    if (!_shouldShowNotification(notification.type)) return;

    _notifications.insert(0, notification); // إضافة في المقدمة
    if (!notification.isRead) {
      _unreadCount++;
    }

    // تشغيل المؤثرات
    _playNotificationEffects();

    notifyListeners();
    debugPrint('🔔 Notification added: ${notification.title}');
  }

  /// التحقق من إمكانية إظهار الإشعار
  bool _shouldShowNotification(String type) {
    if (!_notificationsEnabled) return false;

    switch (type) {
      case 'message':
        return _messageNotifications;
      case NotificationTypes.like:
      case NotificationTypes.love:
      case NotificationTypes.comment:
      case NotificationTypes.reply:
      case NotificationTypes.share:
        return _socialNotifications;
      case NotificationTypes.story:
        return _storyNotifications;
      case 'group':
        return _groupNotifications;
      case 'call':
        return _callNotifications;
      default:
        return true;
    }
  }

  /// إنشاء إشعار تفاعل (إعجاب، حب، إلخ)
  void createReactionNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required String reactionType,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: reactionType,
      title: 'New Reaction',
      message: '$fromUserName reacted to your post',
      postId: postId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار تعليق
  void createCommentNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required String commentId,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: NotificationTypes.comment,
      title: 'New Comment',
      message: '$fromUserName commented on your post',
      postId: postId,
      commentId: commentId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار رد على تعليق
  void createReplyNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required String commentId,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: NotificationTypes.reply,
      title: 'New Reply',
      message: '$fromUserName replied to your comment',
      postId: postId,
      commentId: commentId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار مشاركة
  void createShareNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: NotificationTypes.share,
      title: 'Post Shared',
      message: '$fromUserName shared your post',
      postId: postId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار منشور جديد
  void createNewPostNotification({
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required List<String> followerIds,
  }) {
    for (final followerId in followerIds) {
      // تجنب إرسال إشعار للمستخدم نفسه
      if (followerId == fromUserId) continue;

      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: followerId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        fromUserAvatar: fromUserAvatar,
        type: NotificationTypes.post,
        title: 'New Post',
        message: '$fromUserName shared a new post',
        postId: postId,
        createdAt: DateTime.now(),
      );

      addNotification(notification);
    }
  }

  /// إنشاء إشعار قصة جديدة
  void createNewStoryNotification({
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String storyId,
    required List<String> followerIds,
  }) {
    for (final followerId in followerIds) {
      // تجنب إرسال إشعار للمستخدم نفسه
      if (followerId == fromUserId) continue;

      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: followerId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        fromUserAvatar: fromUserAvatar,
        type: NotificationTypes.story,
        title: 'New Story',
        message: '$fromUserName added a new story',
        data: {'storyId': storyId},
        createdAt: DateTime.now(),
      );

      addNotification(notification);
    }
  }

  /// تحديد إشعار كمقروء
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1 && !_notifications[index].isRead) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _unreadCount--;
      notifyListeners();
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      if (!_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    _unreadCount = 0;
    notifyListeners();
  }

  /// حذف إشعار
  void deleteNotification(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      if (!_notifications[index].isRead) {
        _unreadCount--;
      }
      _notifications.removeAt(index);
      notifyListeners();
    }
  }

  /// مسح جميع الإشعارات
  void clearAllNotifications() {
    _notifications.clear();
    _unreadCount = 0;
    notifyListeners();
  }

  /// الحصول على الإشعارات حسب النوع
  List<NotificationModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  /// إضافة إشعارات تجريبية
  void addDemoNotifications() {
    final demoNotifications = [
      NotificationModel(
        id: '1',
        userId: 'current_user',
        fromUserId: 'user1',
        fromUserName: 'أحمد محمد',
        fromUserAvatar: '',
        type: NotificationTypes.like,
        title: 'New Like',
        message: 'أحمد محمد liked your post',
        postId: 'post1',
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      NotificationModel(
        id: '2',
        userId: 'current_user',
        fromUserId: 'user2',
        fromUserName: 'فاطمة علي',
        fromUserAvatar: '',
        type: NotificationTypes.comment,
        title: 'New Comment',
        message: 'فاطمة علي commented on your post',
        postId: 'post2',
        commentId: 'comment1',
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      NotificationModel(
        id: '3',
        userId: 'current_user',
        fromUserId: 'user3',
        fromUserName: 'محمد حسن',
        fromUserAvatar: '',
        type: NotificationTypes.reply,
        title: 'New Reply',
        message: 'محمد حسن replied to your comment',
        postId: 'post3',
        commentId: 'comment2',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      NotificationModel(
        id: '4',
        userId: 'current_user',
        fromUserId: 'user4',
        fromUserName: 'سارة أحمد',
        fromUserAvatar: '',
        type: NotificationTypes.share,
        title: 'Post Shared',
        message: 'سارة أحمد shared your post',
        postId: 'post4',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      NotificationModel(
        id: '5',
        userId: 'current_user',
        fromUserId: 'user5',
        fromUserName: 'عبدالله خالد',
        fromUserAvatar: '',
        type: NotificationTypes.post,
        title: 'New Post',
        message: 'عبدالله خالد shared a new post',
        postId: 'post5',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    for (final notification in demoNotifications) {
      addNotification(notification);
    }
  }
}

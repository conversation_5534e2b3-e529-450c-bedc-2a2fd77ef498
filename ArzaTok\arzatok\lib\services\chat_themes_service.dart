import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import '../models/reaction_model.dart';

/// خدمة ثيمات الدردشة
class ChatThemesService extends ChangeNotifier {
  static final ChatThemesService _instance = ChatThemesService._internal();
  factory ChatThemesService() => _instance;
  ChatThemesService._internal();

  // قائمة ثيمات الدردشة
  final List<ChatTheme> _chatThemes = [];
  
  // Controllers للبيانات المباشرة
  final StreamController<List<ChatTheme>> _themesController = StreamController.broadcast();

  // الثيمات الافتراضية
  final List<Map<String, dynamic>> _defaultThemes = [
    {
      'name': 'Default Red',
      'backgroundColor': '#FFFFFF',
      'bubbleColor': '#D32F2F',
      'textColor': '#000000',
      'backgroundImage': '',
    },
    {
      'name': 'Ocean Blue',
      'backgroundColor': '#E3F2FD',
      'bubbleColor': '#1976D2',
      'textColor': '#000000',
      'backgroundImage': '',
    },
    {
      'name': 'Forest Green',
      'backgroundColor': '#E8F5E8',
      'bubbleColor': '#388E3C',
      'textColor': '#000000',
      'backgroundImage': '',
    },
    {
      'name': 'Sunset Orange',
      'backgroundColor': '#FFF3E0',
      'bubbleColor': '#F57C00',
      'textColor': '#000000',
      'backgroundImage': '',
    },
    {
      'name': 'Purple Dream',
      'backgroundColor': '#F3E5F5',
      'bubbleColor': '#7B1FA2',
      'textColor': '#000000',
      'backgroundImage': '',
    },
    {
      'name': 'Pink Blossom',
      'backgroundColor': '#FCE4EC',
      'bubbleColor': '#E91E63',
      'textColor': '#000000',
      'backgroundImage': '',
    },
    {
      'name': 'Dark Mode',
      'backgroundColor': '#121212',
      'bubbleColor': '#D32F2F',
      'textColor': '#FFFFFF',
      'backgroundImage': '',
    },
    {
      'name': 'Midnight Blue',
      'backgroundColor': '#0D1421',
      'bubbleColor': '#1976D2',
      'textColor': '#FFFFFF',
      'backgroundImage': '',
    },
    {
      'name': 'Gradient Sunset',
      'backgroundColor': '#FFE0B2',
      'bubbleColor': '#FF5722',
      'textColor': '#000000',
      'backgroundImage': 'gradient_sunset',
    },
    {
      'name': 'Nature',
      'backgroundColor': '#E8F5E8',
      'bubbleColor': '#4CAF50',
      'textColor': '#000000',
      'backgroundImage': 'nature_bg',
    },
  ];

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadChatThemes();
    if (_chatThemes.isEmpty) {
      await _addDefaultThemes();
    }
  }

  /// إضافة الثيمات الافتراضية
  Future<void> _addDefaultThemes() async {
    for (final themeData in _defaultThemes) {
      final theme = ChatTheme(
        id: 'default_${DateTime.now().millisecondsSinceEpoch}_${_chatThemes.length}',
        chatId: '', // ثيم عام
        name: themeData['name'],
        backgroundColor: themeData['backgroundColor'],
        bubbleColor: themeData['bubbleColor'],
        textColor: themeData['textColor'],
        backgroundImage: themeData['backgroundImage'],
        isDefault: true,
        createdAt: DateTime.now(),
      );
      _chatThemes.add(theme);
    }

    await _saveChatThemes();
    _updateStreams();
    debugPrint('🎨 Added ${_defaultThemes.length} default chat themes');
  }

  /// تطبيق ثيم على دردشة
  Future<bool> applyChatTheme({
    required String chatId,
    required String themeId,
  }) async {
    try {
      // البحث عن الثيم
      final theme = _chatThemes.firstWhere(
        (t) => t.id == themeId,
        orElse: () => ChatTheme(
          id: '',
          chatId: '',
          name: '',
          backgroundColor: '',
          bubbleColor: '',
          textColor: '',
          backgroundImage: '',
          createdAt: DateTime.now(),
        ),
      );

      if (theme.id.isEmpty) {
        debugPrint('🎨 Theme not found: $themeId');
        return false;
      }

      // إزالة الثيم السابق للدردشة
      _chatThemes.removeWhere((t) => t.chatId == chatId && !t.isDefault);

      // إنشاء ثيم جديد للدردشة
      final chatTheme = theme.copyWith(
        id: 'chat_${chatId}_${DateTime.now().millisecondsSinceEpoch}',
        chatId: chatId,
        isDefault: false,
        createdAt: DateTime.now(),
      );

      _chatThemes.add(chatTheme);
      await _saveChatThemes();
      _updateStreams();

      debugPrint('🎨 Theme applied to chat: $chatId');
      return true;
    } catch (e) {
      debugPrint('❌ Error applying chat theme: $e');
      return false;
    }
  }

  /// إنشاء ثيم مخصص
  Future<bool> createCustomTheme({
    required String name,
    required String backgroundColor,
    required String bubbleColor,
    required String textColor,
    String backgroundImage = '',
    String chatId = '',
  }) async {
    try {
      final theme = ChatTheme(
        id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
        chatId: chatId,
        name: name,
        backgroundColor: backgroundColor,
        bubbleColor: bubbleColor,
        textColor: textColor,
        backgroundImage: backgroundImage,
        isDefault: false,
        createdAt: DateTime.now(),
      );

      _chatThemes.add(theme);
      await _saveChatThemes();
      _updateStreams();

      debugPrint('🎨 Custom theme created: $name');
      return true;
    } catch (e) {
      debugPrint('❌ Error creating custom theme: $e');
      return false;
    }
  }

  /// تحديث ثيم موجود
  Future<bool> updateTheme({
    required String themeId,
    String? name,
    String? backgroundColor,
    String? bubbleColor,
    String? textColor,
    String? backgroundImage,
  }) async {
    try {
      final index = _chatThemes.indexWhere((t) => t.id == themeId);
      if (index == -1) return false;

      // منع تحديث الثيمات الافتراضية
      if (_chatThemes[index].isDefault) {
        debugPrint('🎨 Cannot update default theme: $themeId');
        return false;
      }

      _chatThemes[index] = _chatThemes[index].copyWith(
        name: name,
        backgroundColor: backgroundColor,
        bubbleColor: bubbleColor,
        textColor: textColor,
        backgroundImage: backgroundImage,
      );

      await _saveChatThemes();
      _updateStreams();

      debugPrint('🎨 Theme updated: $themeId');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating theme: $e');
      return false;
    }
  }

  /// حذف ثيم مخصص
  Future<bool> deleteTheme(String themeId) async {
    try {
      final index = _chatThemes.indexWhere((t) => t.id == themeId);
      if (index == -1) return false;

      // منع حذف الثيمات الافتراضية
      if (_chatThemes[index].isDefault) {
        debugPrint('🎨 Cannot delete default theme: $themeId');
        return false;
      }

      _chatThemes.removeAt(index);
      await _saveChatThemes();
      _updateStreams();

      debugPrint('🎨 Theme deleted: $themeId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting theme: $e');
      return false;
    }
  }

  /// الحصول على ثيم دردشة معينة
  ChatTheme? getChatTheme(String chatId) {
    try {
      return _chatThemes.firstWhere((t) => t.chatId == chatId);
    } catch (e) {
      // إرجاع الثيم الافتراضي
      return getDefaultTheme();
    }
  }

  /// الحصول على الثيم الافتراضي
  ChatTheme? getDefaultTheme() {
    try {
      return _chatThemes.firstWhere((t) => t.isDefault && t.name == 'Default Red');
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع الثيمات الافتراضية
  List<ChatTheme> getDefaultThemes() {
    return _chatThemes.where((t) => t.isDefault).toList();
  }

  /// الحصول على الثيمات المخصصة
  List<ChatTheme> getCustomThemes() {
    return _chatThemes.where((t) => !t.isDefault && t.chatId.isEmpty).toList();
  }

  /// الحصول على جميع الثيمات المتاحة
  List<ChatTheme> getAllAvailableThemes() {
    return _chatThemes.where((t) => t.chatId.isEmpty).toList();
  }

  /// إزالة ثيم من دردشة (العودة للافتراضي)
  Future<bool> removeChatTheme(String chatId) async {
    try {
      _chatThemes.removeWhere((t) => t.chatId == chatId && !t.isDefault);
      await _saveChatThemes();
      _updateStreams();

      debugPrint('🎨 Theme removed from chat: $chatId');
      return true;
    } catch (e) {
      debugPrint('❌ Error removing chat theme: $e');
      return false;
    }
  }

  /// الحصول على ألوان الثيم كـ Color objects
  Map<String, Color> getThemeColors(ChatTheme theme) {
    return {
      'background': _parseColor(theme.backgroundColor),
      'bubble': _parseColor(theme.bubbleColor),
      'text': _parseColor(theme.textColor),
    };
  }

  /// تحويل النص إلى لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return Colors.black;
    } catch (e) {
      return Colors.black;
    }
  }

  /// تحويل اللون إلى نص
  String _colorToString(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  /// الحصول على ثيمات شائعة
  List<ChatTheme> getPopularThemes({int limit = 5}) {
    // يمكن تحسينه لاحقاً بإضافة تتبع الاستخدام
    return getDefaultThemes().take(limit).toList();
  }

  /// البحث في الثيمات
  List<ChatTheme> searchThemes(String query) {
    if (query.trim().isEmpty) return getAllAvailableThemes();

    final lowerQuery = query.toLowerCase();
    return _chatThemes.where((theme) {
      return theme.chatId.isEmpty && // فقط الثيمات المتاحة
             theme.name.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// تحديث البيانات المباشرة
  void _updateStreams() {
    _themesController.add(List.from(_chatThemes));
    notifyListeners();
  }

  /// تحميل ثيمات الدردشة
  Future<void> _loadChatThemes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themesJson = prefs.getString('chat_themes');

      if (themesJson != null) {
        final themesList = json.decode(themesJson) as List;
        _chatThemes.clear();
        for (final themeMap in themesList) {
          _chatThemes.add(ChatTheme.fromMap(themeMap));
        }
      }

      debugPrint('🎨 Loaded ${_chatThemes.length} chat themes');
    } catch (e) {
      debugPrint('❌ Error loading chat themes: $e');
    }
  }

  /// حفظ ثيمات الدردشة
  Future<void> _saveChatThemes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themesList = _chatThemes.map((t) => t.toMap()).toList();
      final themesJson = json.encode(themesList);
      await prefs.setString('chat_themes', themesJson);
    } catch (e) {
      debugPrint('❌ Error saving chat themes: $e');
    }
  }

  /// إحصائيات الثيمات
  Map<String, dynamic> getThemesStats() {
    final stats = <String, dynamic>{};
    
    stats['total'] = _chatThemes.length;
    stats['default'] = _chatThemes.where((t) => t.isDefault).length;
    stats['custom'] = _chatThemes.where((t) => !t.isDefault && t.chatId.isEmpty).length;
    stats['applied'] = _chatThemes.where((t) => t.chatId.isNotEmpty).length;
    
    // الثيمات الأكثر استخداماً (يمكن تحسينه لاحقاً)
    final appliedThemes = _chatThemes.where((t) => t.chatId.isNotEmpty).toList();
    final themeUsage = <String, int>{};
    for (final theme in appliedThemes) {
      themeUsage[theme.name] = (themeUsage[theme.name] ?? 0) + 1;
    }
    stats['usage'] = themeUsage;
    
    return stats;
  }

  // Getters
  Stream<List<ChatTheme>> get themesStream => _themesController.stream;
  List<ChatTheme> get allThemes => List.from(_chatThemes);
  int get totalThemes => _chatThemes.length;
  int get customThemesCount => getCustomThemes().length;

  @override
  void dispose() {
    _themesController.close();
    super.dispose();
  }
}

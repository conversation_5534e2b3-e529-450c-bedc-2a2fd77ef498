import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import '../models/reaction_model.dart';

/// خدمة الملصقات والإيموجي
class StickersService extends ChangeNotifier {
  static final StickersService _instance = StickersService._internal();
  factory StickersService() => _instance;
  StickersService._internal();

  // قائمة الملصقات
  final List<StickerModel> _stickers = [];

  // Controllers للبيانات المباشرة
  final StreamController<List<StickerModel>> _stickersController = StreamController.broadcast();

  // الإيموجي المتاحة مجمعة حسب الفئة
  final Map<String, List<String>> _emojiCategories = {
    'Smileys': [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
      '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
      '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
    ],
    'Emotions': [
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
      '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
      '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
      '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
    ],
    'Gestures': [
      '👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟',
      '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️',
      '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲',
      '🤝', '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿',
    ],
    'People': [
      '👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔',
      '👩', '🧓', '👴', '👵', '🙍', '🙎', '🙅', '🙆',
      '💁', '🙋', '🧏', '🙇', '🤦', '🤷', '👮', '🕵️',
      '💂', '🥷', '👷', '🤴', '👸', '👳', '👲', '🧕',
    ],
    'Animals': [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼',
      '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵',
      '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤',
      '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗',
    ],
    'Food': [
      '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓',
      '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝',
      '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑',
      '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐',
    ],
    'Activities': [
      '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉',
      '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍',
      '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿',
      '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿',
    ],
    'Objects': [
      '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️',
      '🖲️', '🕹️', '🗜️', '💽', '💾', '💿', '📀', '📼',
      '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️',
      '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '🧭',
    ],
    'Symbols': [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍',
      '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖',
      '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️',
      '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈',
      '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐',
      '♑', '♒', '♓', '🆔', '⚡', '🔥', '💯', '✨',
      '🌟', '⭐', '🌠', '💫', '⚠️', '🚫', '✅', '❌',
    ],
    'Arabic': [
      '🕌', '☪️', '🤲', '📿', '🧕', '👳', '🕋', '🌙',
      '⭐', '✨', '🌟', '💫', '🌠', '🎆', '🎇', '✨',
      '🔥', '💎', '👑', '🏆', '🥇', '🏅', '🎖️', '🏵️',
      '🌹', '🌺', '🌸', '🌼', '🌻', '🌷', '💐', '🌿',
    ],
  };

  // الملصقات الافتراضية (إيموجي كبيرة)
  final List<Map<String, dynamic>> _defaultStickers = [
    {
      'name': 'Happy Face',
      'imageUrl': '😀',
      'category': 'Emotions',
      'tags': ['happy', 'smile', 'joy'],
    },
    {
      'name': 'Heart Eyes',
      'imageUrl': '😍',
      'category': 'Emotions',
      'tags': ['love', 'heart', 'eyes'],
    },
    {
      'name': 'Thumbs Up',
      'imageUrl': '👍',
      'category': 'Gestures',
      'tags': ['good', 'ok', 'approve'],
    },
    {
      'name': 'Fire',
      'imageUrl': '🔥',
      'category': 'Symbols',
      'tags': ['hot', 'fire', 'cool'],
    },
    {
      'name': 'Party',
      'imageUrl': '🎉',
      'category': 'Activities',
      'tags': ['party', 'celebration', 'fun'],
    },
    {
      'name': 'Heart',
      'imageUrl': '❤️',
      'category': 'Symbols',
      'tags': ['love', 'heart', 'red'],
    },
    {
      'name': 'Crying Laugh',
      'imageUrl': '😂',
      'category': 'Emotions',
      'tags': ['laugh', 'funny', 'cry'],
    },
    {
      'name': 'Thinking',
      'imageUrl': '🤔',
      'category': 'Emotions',
      'tags': ['think', 'wonder', 'hmm'],
    },
    {
      'name': 'Mosque',
      'imageUrl': '🕌',
      'category': 'Arabic',
      'tags': ['mosque', 'islam', 'prayer'],
    },
    {
      'name': 'Crescent Moon',
      'imageUrl': '🌙',
      'category': 'Arabic',
      'tags': ['moon', 'islam', 'night'],
    },
    {
      'name': 'Star',
      'imageUrl': '⭐',
      'category': 'Arabic',
      'tags': ['star', 'shine', 'bright'],
    },
    {
      'name': 'Rose',
      'imageUrl': '🌹',
      'category': 'Arabic',
      'tags': ['rose', 'flower', 'love'],
    },
    {
      'name': 'Crown',
      'imageUrl': '👑',
      'category': 'Arabic',
      'tags': ['crown', 'king', 'royal'],
    },
    {
      'name': 'Diamond',
      'imageUrl': '💎',
      'category': 'Arabic',
      'tags': ['diamond', 'precious', 'gem'],
    },
    {
      'name': 'Trophy',
      'imageUrl': '🏆',
      'category': 'Arabic',
      'tags': ['trophy', 'winner', 'success'],
    },
    {
      'name': 'Sparkles',
      'imageUrl': '✨',
      'category': 'Arabic',
      'tags': ['sparkles', 'magic', 'shine'],
    },
    {
      'name': 'Clap',
      'imageUrl': '👏',
      'category': 'Gestures',
      'tags': ['clap', 'applause', 'good'],
    },
    {
      'name': 'Peace',
      'imageUrl': '✌️',
      'category': 'Gestures',
      'tags': ['peace', 'victory', 'two'],
    },
    {
      'name': 'OK Hand',
      'imageUrl': '👌',
      'category': 'Gestures',
      'tags': ['ok', 'perfect', 'good'],
    },
    {
      'name': 'Pray',
      'imageUrl': '🤲',
      'category': 'Arabic',
      'tags': ['pray', 'dua', 'hands'],
    },
    {
      'name': 'Wink',
      'imageUrl': '😉',
      'category': 'Emotions',
      'tags': ['wink', 'flirt', 'cute'],
    },
    {
      'name': 'Kiss',
      'imageUrl': '😘',
      'category': 'Emotions',
      'tags': ['kiss', 'love', 'heart'],
    },
    {
      'name': 'Cool',
      'imageUrl': '😎',
      'category': 'Emotions',
      'tags': ['cool', 'sunglasses', 'awesome'],
    },
    {
      'name': 'Shocked',
      'imageUrl': '😱',
      'category': 'Emotions',
      'tags': ['shocked', 'surprised', 'wow'],
    },
    {
      'name': 'Angry',
      'imageUrl': '😡',
      'category': 'Emotions',
      'tags': ['angry', 'mad', 'furious'],
    },
    {
      'name': 'Sad',
      'imageUrl': '😢',
      'category': 'Emotions',
      'tags': ['sad', 'cry', 'tear'],
    },
    {
      'name': 'Sleepy',
      'imageUrl': '😴',
      'category': 'Emotions',
      'tags': ['sleepy', 'tired', 'sleep'],
    },
  ];

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadStickers();
    if (_stickers.isEmpty) {
      await _addDefaultStickers();
    }
  }

  /// إضافة الملصقات الافتراضية
  Future<void> _addDefaultStickers() async {
    for (final stickerData in _defaultStickers) {
      final sticker = StickerModel(
        id: 'default_${DateTime.now().millisecondsSinceEpoch}_${_stickers.length}',
        name: stickerData['name'],
        imageUrl: stickerData['imageUrl'],
        category: stickerData['category'],
        tags: List<String>.from(stickerData['tags']),
        createdAt: DateTime.now(),
      );
      _stickers.add(sticker);
    }

    await _saveStickers();
    _updateStreams();
    debugPrint('🎭 Added ${_defaultStickers.length} default stickers');
  }

  /// الحصول على الإيموجي حسب الفئة
  List<String> getEmojisByCategory(String category) {
    return _emojiCategories[category] ?? [];
  }

  /// الحصول على جميع فئات الإيموجي
  List<String> getEmojiCategories() {
    return _emojiCategories.keys.toList();
  }

  /// الحصول على جميع الإيموجي
  List<String> getAllEmojis() {
    final allEmojis = <String>[];
    for (final emojis in _emojiCategories.values) {
      allEmojis.addAll(emojis);
    }
    return allEmojis;
  }

  /// البحث في الإيموجي
  List<String> searchEmojis(String query) {
    if (query.trim().isEmpty) return getAllEmojis();

    final lowerQuery = query.toLowerCase();
    final results = <String>[];

    // البحث في أسماء الفئات
    for (final entry in _emojiCategories.entries) {
      if (entry.key.toLowerCase().contains(lowerQuery)) {
        results.addAll(entry.value);
      }
    }

    return results.toSet().toList(); // إزالة المكررات
  }

  /// الحصول على الملصقات حسب الفئة
  List<StickerModel> getStickersByCategory(String category) {
    return _stickers.where((s) => s.category == category).toList();
  }

  /// الحصول على جميع الملصقات
  List<StickerModel> getAllStickers() {
    return List.from(_stickers);
  }

  /// البحث في الملصقات
  List<StickerModel> searchStickers(String query) {
    if (query.trim().isEmpty) return getAllStickers();

    final lowerQuery = query.toLowerCase();
    return _stickers.where((sticker) {
      return sticker.name.toLowerCase().contains(lowerQuery) ||
             sticker.category.toLowerCase().contains(lowerQuery) ||
             sticker.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// إضافة ملصق مخصص
  Future<bool> addCustomSticker({
    required String name,
    required String imageUrl,
    required String category,
    required List<String> tags,
    bool isAnimated = false,
  }) async {
    try {
      final sticker = StickerModel(
        id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        imageUrl: imageUrl,
        category: category,
        tags: tags,
        isAnimated: isAnimated,
        createdAt: DateTime.now(),
      );

      _stickers.add(sticker);
      await _saveStickers();
      _updateStreams();

      debugPrint('🎭 Custom sticker added: $name');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding custom sticker: $e');
      return false;
    }
  }

  /// حذف ملصق مخصص
  Future<bool> deleteSticker(String stickerId) async {
    try {
      final index = _stickers.indexWhere((s) => s.id == stickerId);
      if (index == -1) return false;

      // منع حذف الملصقات الافتراضية
      if (_stickers[index].id.startsWith('default_')) {
        debugPrint('🎭 Cannot delete default sticker: $stickerId');
        return false;
      }

      _stickers.removeAt(index);
      await _saveStickers();
      _updateStreams();

      debugPrint('🎭 Sticker deleted: $stickerId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting sticker: $e');
      return false;
    }
  }

  /// الحصول على فئات الملصقات
  List<String> getStickerCategories() {
    final categories = _stickers.map((s) => s.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// الحصول على الملصقات الأكثر استخداماً
  List<StickerModel> getPopularStickers({int limit = 10}) {
    // يمكن تحسينه لاحقاً بإضافة تتبع الاستخدام
    return _stickers.take(limit).toList();
  }

  /// الحصول على الملصقات الحديثة
  List<StickerModel> getRecentStickers({int limit = 10}) {
    final sortedStickers = List<StickerModel>.from(_stickers);
    sortedStickers.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedStickers.take(limit).toList();
  }

  /// إنشاء ملصق من إيموجي
  StickerModel createStickerFromEmoji(String emoji) {
    return StickerModel(
      id: 'emoji_${emoji.hashCode}',
      name: 'Emoji Sticker',
      imageUrl: emoji,
      category: 'Emoji',
      tags: ['emoji'],
      createdAt: DateTime.now(),
    );
  }

  /// التحقق من صحة الإيموجي
  bool isValidEmoji(String text) {
    // فحص بسيط للإيموجي
    final emojiRegex = RegExp(r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]', unicode: true);
    return emojiRegex.hasMatch(text);
  }

  /// الحصول على إيموجي عشوائية
  String getRandomEmoji() {
    final allEmojis = getAllEmojis();
    if (allEmojis.isEmpty) return '😊';

    final random = DateTime.now().millisecondsSinceEpoch % allEmojis.length;
    return allEmojis[random];
  }

  /// تحديث البيانات المباشرة
  void _updateStreams() {
    _stickersController.add(List.from(_stickers));
    notifyListeners();
  }

  /// تحميل الملصقات
  Future<void> _loadStickers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stickersJson = prefs.getString('stickers');

      if (stickersJson != null) {
        final stickersList = json.decode(stickersJson) as List;
        _stickers.clear();
        for (final stickerMap in stickersList) {
          _stickers.add(StickerModel.fromMap(stickerMap));
        }
      }

      debugPrint('🎭 Loaded ${_stickers.length} stickers');
    } catch (e) {
      debugPrint('❌ Error loading stickers: $e');
    }
  }

  /// حفظ الملصقات
  Future<void> _saveStickers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stickersList = _stickers.map((s) => s.toMap()).toList();
      final stickersJson = json.encode(stickersList);
      await prefs.setString('stickers', stickersJson);
    } catch (e) {
      debugPrint('❌ Error saving stickers: $e');
    }
  }

  /// إحصائيات الملصقات
  Map<String, dynamic> getStickersStats() {
    final stats = <String, dynamic>{};

    stats['totalStickers'] = _stickers.length;
    stats['totalEmojis'] = getAllEmojis().length;
    stats['emojiCategories'] = _emojiCategories.length;
    stats['stickerCategories'] = getStickerCategories().length;

    // إحصائيات حسب الفئة
    final categoryStats = <String, int>{};
    for (final sticker in _stickers) {
      categoryStats[sticker.category] = (categoryStats[sticker.category] ?? 0) + 1;
    }
    stats['stickersByCategory'] = categoryStats;

    // إحصائيات الإيموجي حسب الفئة
    final emojiStats = <String, int>{};
    for (final entry in _emojiCategories.entries) {
      emojiStats[entry.key] = entry.value.length;
    }
    stats['emojisByCategory'] = emojiStats;

    return stats;
  }

  // Getters
  Stream<List<StickerModel>> get stickersStream => _stickersController.stream;
  List<StickerModel> get allStickers => List.from(_stickers);
  Map<String, List<String>> get emojiCategories => Map.from(_emojiCategories);
  int get totalStickers => _stickers.length;
  int get totalEmojis => getAllEmojis().length;

  @override
  void dispose() {
    _stickersController.close();
    super.dispose();
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../services/social_feed_service.dart';
import '../widgets/reactions_list_sheet.dart';

/// ويدجت إحصائيات التفاعلات المتقدمة
class ReactionsStatsWidget extends StatefulWidget {
  final PostModel post;

  const ReactionsStatsWidget({
    Key? key,
    required this.post,
  }) : super(key: key);

  @override
  State<ReactionsStatsWidget> createState() => _ReactionsStatsWidgetState();
}

class _ReactionsStatsWidgetState extends State<ReactionsStatsWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // خريطة التفاعلات مع الألوان
  final Map<String, ReactionInfo> _reactionInfo = {
    '👍': ReactionInfo('Like', const Color(0xFF1877F2)),
    '❤️': ReactionInfo('Love', const Color(0xFFE91E63)),
    '😂': ReactionInfo('Haha', const Color(0xFFFFC107)),
    '😮': ReactionInfo('Wow', const Color(0xFFFFC107)),
    '😢': ReactionInfo('Sad', const Color(0xFFFFC107)),
    '😡': ReactionInfo('Angry', const Color(0xFFFF5722)),
    '🙏': ReactionInfo('Support', const Color(0xFF4CAF50)),
    '👏': ReactionInfo('Clap', const Color(0xFF9C27B0)),
  };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // تشغيل النبض المستمر
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SocialFeedService>(
      builder: (context, socialService, child) {
        final reactions = widget.post.reactions;
        final totalReactions = reactions.values.fold(0, (sum, count) => sum + count);

        if (totalReactions == 0) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: () => _showReactionsList(),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // أيقونات التفاعلات المكدسة
                _buildStackedReactionIcons(reactions),

                const SizedBox(width: 8),

                // نص العدد مع تأثير النبض
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: totalReactions > 0 ? _pulseAnimation.value : 1.0,
                      child: Text(
                        _getReactionsText(totalReactions),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  },
                ),

                const Spacer(),

                // إحصائيات إضافية
                _buildAdditionalStats(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء أيقونات التفاعلات المكدسة
  Widget _buildStackedReactionIcons(Map<String, int> reactions) {
    final sortedReactions = reactions.entries
        .where((entry) => entry.value > 0)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (sortedReactions.isEmpty) {
      return const SizedBox.shrink();
    }

    // عرض أكثر 3 تفاعلات شيوعاً
    final topReactions = sortedReactions.take(3).toList();

    return SizedBox(
      width: topReactions.length * 16.0 + 8,
      height: 24,
      child: Stack(
        children: topReactions.asMap().entries.map((entry) {
          final index = entry.key;
          final reaction = entry.value;
          final reactionInfo = _reactionInfo[reaction.key];

          return Positioned(
            left: index * 16.0,
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200 + (index * 100)),
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: reactionInfo?.color.withOpacity(0.1) ?? Colors.grey[200],
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (reactionInfo?.color ?? Colors.grey).withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  reaction.key,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// بناء إحصائيات إضافية
  Widget _buildAdditionalStats() {
    return Row(
      children: [
        // عدد التعليقات
        if (widget.post.commentsCount > 0) ...[
          Text(
            '${widget.post.commentsCount} comments',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 8),
        ],

        // عدد المشاركات
        if (widget.post.repostCount != null && widget.post.repostCount! > 0) ...[
          Text(
            '${widget.post.repostCount} shares',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ],
    );
  }

  /// الحصول على نص التفاعلات
  String _getReactionsText(int totalReactions) {
    if (totalReactions == 1) {
      return '1 reaction';
    } else {
      return '$totalReactions reactions';
    }
  }

  /// عرض قائمة التفاعلات
  void _showReactionsList() {
    final totalReactions = widget.post.reactions.values.fold(0, (sum, count) => sum + count);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ReactionsListSheet(
        postId: widget.post.id,
        totalReactions: totalReactions,
      ),
    );
  }
}

/// معلومات التفاعل
class ReactionInfo {
  final String name;
  final Color color;

  const ReactionInfo(this.name, this.color);
}

/// ويدجت تفاعل مفرد مع تأثيرات متقدمة
class AnimatedReactionIcon extends StatefulWidget {
  final String emoji;
  final Color color;
  final int count;
  final bool isSelected;

  const AnimatedReactionIcon({
    Key? key,
    required this.emoji,
    required this.color,
    required this.count,
    this.isSelected = false,
  }) : super(key: key);

  @override
  State<AnimatedReactionIcon> createState() => _AnimatedReactionIconState();
}

class _AnimatedReactionIconState extends State<AnimatedReactionIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isSelected) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedReactionIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? widget.color.withOpacity(0.1)
                    : Colors.transparent,
                shape: BoxShape.circle,
                border: widget.isSelected
                    ? Border.all(color: widget.color, width: 2)
                    : null,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.emoji,
                    style: const TextStyle(fontSize: 20),
                  ),
                  if (widget.count > 0) ...[
                    const SizedBox(height: 2),
                    Text(
                      widget.count.toString(),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: widget.color,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// ويدجت تأثير الجسيمات للتفاعلات
class ReactionParticlesWidget extends StatefulWidget {
  final String emoji;
  final Color color;

  const ReactionParticlesWidget({
    Key? key,
    required this.emoji,
    required this.color,
  }) : super(key: key);

  @override
  State<ReactionParticlesWidget> createState() => _ReactionParticlesWidgetState();
}

class _ReactionParticlesWidgetState extends State<ReactionParticlesWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _particles = List.generate(10, (index) => Particle());
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(100, 100),
          painter: ParticlesPainter(
            particles: _particles,
            progress: _controller.value,
            emoji: widget.emoji,
            color: widget.color,
          ),
        );
      },
    );
  }
}

/// رسام الجسيمات
class ParticlesPainter extends CustomPainter {
  final List<Particle> particles;
  final double progress;
  final String emoji;
  final Color color;

  ParticlesPainter({
    required this.particles,
    required this.progress,
    required this.emoji,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(1.0 - progress)
      ..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = size.width / 2 + particle.x * progress * 50;
      final y = size.height / 2 + particle.y * progress * 50;

      canvas.drawCircle(
        Offset(x, y),
        particle.size * (1.0 - progress),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// جسيم للتأثيرات
class Particle {
  final double x;
  final double y;
  final double size;

  Particle()
      : x = (2.0 * (0.5 - (DateTime.now().millisecondsSinceEpoch % 1000) / 1000.0)),
        y = (2.0 * (0.5 - (DateTime.now().microsecondsSinceEpoch % 1000) / 1000.0)),
        size = 2.0 + (DateTime.now().millisecondsSinceEpoch % 3);
}

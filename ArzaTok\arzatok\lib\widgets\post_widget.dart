import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../models/post_model.dart';
import '../models/user_model.dart';
import '../services/social_feed_service.dart';
import '../services/auth_service.dart';
import '../services/post_management_service.dart';
import '../services/saved_items_service.dart';
import '../services/users_service.dart';
import '../screens/social/user_profile_screen.dart';
import 'video_player_widget.dart';
import 'audio_player_widget.dart';
import 'reactions_list_sheet.dart';
import 'share_bottom_sheet.dart';
import 'repost_bottom_sheet.dart';
import 'facebook_real_reactions.dart';
import 'reactions_stats_widget.dart';
import 'facebook_reactions.dart';
import 'premium_ui_enhancements.dart';
import 'universal_reactions_system.dart';
import 'link_preview_widget.dart';
import 'facebook_style_video_player.dart';
import 'facebook_style_image_viewer.dart';
import 'post_stats_widget.dart';
import 'universal_profile_navigator.dart';

/// Widget عرض المنشور
class PostWidget extends StatefulWidget {
  final PostModel post;
  final Function(String) onReaction;
  final VoidCallback onComment;
  final VoidCallback onShare;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const PostWidget({
    super.key,
    required this.post,
    required this.onReaction,
    required this.onComment,
    required this.onShare,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<PostWidget> createState() => _PostWidgetState();
}

class _PostWidgetState extends State<PostWidget> {
  final bool _showReactions = false;
  String? _userReactionId;

  @override
  void initState() {
    super.initState();
    _loadUserReaction();
  }

  /// تحميل تفاعل المستخدم
  void _loadUserReaction() async {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      final reaction = await socialService.getUserReaction(
        widget.post.id,
        currentUser.phoneNumber,
      );
      if (mounted) {
        setState(() {
          _userReactionId = _getReactionIdFromEmoji(reaction);
        });
      }
    }
  }

  /// تحديد معرف التفاعل من الإيموجي
  String? _getReactionIdFromEmoji(String? emoji) {
    if (emoji == null) return null;
    switch (emoji) {
      case '👍':
        return 'like';
      case '❤️':
        return 'love';
      case '😂':
        return 'haha';
      case '😮':
        return 'wow';
      case '😢':
        return 'sad';
      case '😡':
        return 'angry';
      case '🙏':
        return 'support';
      case '👏':
        return 'clap';
      case '🐔':
        return 'coward';
      default:
        return 'like';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إذا كان هذا منشور إعادة نشر، أظهر معلومات إعادة النشر
          if (widget.post.isRepost) _buildRepostHeader(),

          // رأس المنشور مع التنقل للملف الشخصي
          _buildClickablePostHeader(),

          // محتوى المنشور (تعليق إعادة النشر إن وجد)
          if (widget.post.isRepost && widget.post.repostComment?.isNotEmpty == true)
            _buildRepostComment(),

          // المنشور الأصلي (إذا كان إعادة نشر)
          if (widget.post.isRepost && widget.post.originalPost != null)
            _buildOriginalPost(),

          // محتوى المنشور العادي
          if (!widget.post.isRepost) _buildPostContent(),

          // الصور (للمنشور العادي فقط)
          if (!widget.post.isRepost && widget.post.images.isNotEmpty) _buildPostImages(),

          // إحصائيات التفاعلات المتقدمة
          ReactionsStatsWidget(post: widget.post),

          // أزرار التفاعل الأصلية
          _buildActionButtons(),


        ],
      ),
    );
  }

  /// بناء رأس المنشور
  Widget _buildPostHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: const Color(0xFFD32F2F),
            child: Text(
              widget.post.authorName.isNotEmpty
                  ? widget.post.authorName[0].toUpperCase()
                  : '?',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () => _openUserProfile(),
                  child: Text(
                    widget.post.authorName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFFD32F2F),
                    ),
                  ),
                ),
                Row(
                  children: [
                    Text(
                      widget.post.timeAgo,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      widget.post.privacyIcon,
                      size: 12,
                      color: Colors.grey[600],
                    ),
                    if (widget.post.isEdited) ...[
                      const SizedBox(width: 4),
                      Text(
                        '• edited',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handlePostMenuAction(value),
            itemBuilder: (context) => _buildPostMenuItems(),
            child: Icon(
              Icons.more_horiz,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس المنشور القابل للنقر
  Widget _buildClickablePostHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ClickableProfileHeader(
        userId: widget.post.authorId,
        userName: widget.post.authorName,
        userAvatar: widget.post.authorAvatar,
        subtitle: '${widget.post.timeAgo}${widget.post.isEdited ? ' • edited' : ''}',
        avatarRadius: 20,
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handlePostMenuAction(value),
          itemBuilder: (context) => _buildPostMenuItems(),
          child: Icon(
            Icons.more_horiz,
            color: Colors.grey[600],
          ),
        ),
      ),
    );
  }

  /// بناء محتوى المنشور
  Widget _buildPostContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // محتوى النص مع خلفية ملونة
        if (widget.post.content.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: widget.post.backgroundColor != null
                ? const EdgeInsets.all(16)
                : EdgeInsets.zero,
            decoration: widget.post.backgroundColor != null
                ? BoxDecoration(
                    gradient: _getBackgroundGradient(widget.post.backgroundColor!),
                    borderRadius: BorderRadius.circular(12),
                  )
                : null,
            child: Text(
              widget.post.content,
              style: TextStyle(
                fontSize: 16,
                height: 1.4,
                color: widget.post.backgroundColor != null
                    ? Colors.white
                    : Colors.black,
                fontWeight: widget.post.backgroundColor != null
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
            ),
          ),

        // معلومات إضافية (الموقع، المشاعر، الموسيقى، الأشخاص المشار إليهم)
        _buildPostExtras(),

        // البث المباشر
        if (widget.post.isLiveStream) _buildLiveStreamIndicator(),

        // الفيديوهات
        if (widget.post.videos.isNotEmpty) _buildVideos(),

        // معاينات الروابط
        if (widget.post.linkPreviews.isNotEmpty) _buildLinkPreviews(),
      ],
    );
  }

  /// الحصول على تدرج الخلفية
  LinearGradient _getBackgroundGradient(String backgroundName) {
    switch (backgroundName) {
      case 'Red Gradient':
        return const LinearGradient(
          colors: [Color(0xFFD32F2F), Color(0xFFB71C1C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Blue Gradient':
        return const LinearGradient(
          colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Green Gradient':
        return const LinearGradient(
          colors: [Color(0xFF388E3C), Color(0xFF2E7D32)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Purple Gradient':
        return const LinearGradient(
          colors: [Color(0xFF7B1FA2), Color(0xFF6A1B9A)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Orange Gradient':
        return const LinearGradient(
          colors: [Color(0xFFFF6F00), Color(0xFFE65100)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Pink Gradient':
        return const LinearGradient(
          colors: [Color(0xFFE91E63), Color(0xFFC2185B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Teal Gradient':
        return const LinearGradient(
          colors: [Color(0xFF00796B), Color(0xFF00695C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Indigo Gradient':
        return const LinearGradient(
          colors: [Color(0xFF303F9F), Color(0xFF283593)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Brown Gradient':
        return const LinearGradient(
          colors: [Color(0xFF5D4037), Color(0xFF4E342E)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'Dark Gradient':
        return const LinearGradient(
          colors: [Color(0xFF424242), Color(0xFF212121)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFFD32F2F), Color(0xFFB71C1C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  /// بناء معلومات إضافية للمنشور
  Widget _buildPostExtras() {
    final hasExtras = widget.post.location != null ||
        widget.post.feeling != null ||
        widget.post.musicTitle != null ||
        widget.post.taggedUsers.isNotEmpty;

    if (!hasExtras) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الموقع
          if (widget.post.location != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: Colors.red),
                  const SizedBox(width: 4),
                  Text(
                    'at ${widget.post.location}',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

          // المشاعر
          if (widget.post.feeling != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  const Icon(Icons.emoji_emotions, size: 16, color: Colors.orange),
                  const SizedBox(width: 4),
                  Text(
                    'feeling ${widget.post.feeling}',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

          // الموسيقى
          if (widget.post.musicTitle != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.music_note, size: 16, color: Colors.purple),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Listening to',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            widget.post.musicTitle!,
                            style: const TextStyle(
                              color: Colors.purple,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // استخدام AudioPlayerWidget للتشغيل الحقيقي
                    if (widget.post.musicUrl != null)
                      SizedBox(
                        width: 60,
                        height: 40,
                        child: AudioPlayerWidget(
                          audioUrl: widget.post.musicUrl!,
                          title: widget.post.musicTitle,
                          width: 60,
                        ),
                      )
                    else
                      IconButton(
                        icon: const Icon(Icons.play_arrow, color: Colors.purple),
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('🎵 Playing ${widget.post.musicTitle}')),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),

          // الأشخاص المشار إليهم
          if (widget.post.taggedUsers.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  const Icon(Icons.person_add, size: 16, color: Colors.teal),
                  const SizedBox(width: 4),
                  Text(
                    'with ${widget.post.taggedUsers.length} people',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء مؤشر البث المباشر
  Widget _buildLiveStreamIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.red, Colors.redAccent],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة البث المباشر مع تأثير الوميض
          Container(
            padding: const EdgeInsets.all(6),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.live_tv,
              color: Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'LIVE',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
                Text(
                  '${widget.post.authorName} is live now',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          // زر المشاهدة
          ElevatedButton(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('🔴 Joining ${widget.post.authorName}\'s live stream...')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text(
              'Watch',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الفيديوهات
  Widget _buildVideos() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: widget.post.videos.map((videoPath) => _buildSingleVideo(videoPath)).toList(),
      ),
    );
  }

  /// بناء فيديو واحد حقيقي مع أدوات Facebook
  Widget _buildSingleVideo(String videoPath) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUserId = authService.currentUser?.phoneNumber;
    final isOwner = currentUserId == widget.post.authorId;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: FacebookStyleVideoPlayer(
        videoUrl: videoPath,
        autoPlay: false,
        showControls: true,
        allowFullscreen: true,
        postId: widget.post.id,
        authorId: widget.post.authorId,
        authorName: widget.post.authorName,
        currentUserId: currentUserId,
        isOwner: isOwner,
        likesCount: widget.post.reactions.values.fold(0, (sum, count) => sum + count),
        commentsCount: 0, // يمكن إضافة عدد التعليقات لاحق<|im_start|>
        isLiked: widget.post.reactions.containsKey(currentUserId),
        onLike: (postId) => _handleVideoLike(postId),
        onComment: (postId) => _handleVideoComment(postId),
        onShare: (postId) => _handleVideoShare(postId),
      ),
    );
  }

  /// التعامل مع إعجاب الفيديو
  void _handleVideoLike(String postId) {
    final socialFeedService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final userId = authService.currentUser?.phoneNumber ?? '';
    socialFeedService.addPostReaction(postId: postId, userId: userId, emoji: '👍');
  }

  /// التعامل مع تعليق الفيديو
  void _handleVideoComment(String postId) {
    // يمكن إضافة منطق فتح شاشة التعليقات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💬 Comments feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// التعامل مع مشاركة الفيديو
  void _handleVideoShare(String postId) {
    // يمكن إضافة منطق المشاركة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔗 Video shared successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// بناء معاينات الروابط
  Widget _buildLinkPreviews() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: widget.post.linkPreviews.map((linkPreview) =>
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: LinkPreviewWidget(
              linkPreview: linkPreview,
              showCloseButton: false,
            ),
          ),
        ).toList(),
      ),
    );
  }

  /// بناء صور المنشور مع أدوات Facebook
  Widget _buildPostImages() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: FacebookStyleImageViewer(
        imageUrls: widget.post.images,
        initialIndex: 0,
        showControls: true,
        allowZoom: true,
        allowSwipe: true,
      ),
    );
  }

  /// بناء صورة واحدة حقيقية
  Widget _buildSingleImage(String imagePath) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: imagePath.startsWith('http')
            ? Image.network(
                imagePath,
                width: double.infinity,
                height: 300,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: 300,
                    color: Colors.grey[200],
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                        color: const Color(0xFFD32F2F),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) => Container(
                  width: double.infinity,
                  height: 300,
                  color: Colors.grey[300],
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, size: 60, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('Failed to load image', style: TextStyle(color: Colors.grey)),
                    ],
                  ),
                ),
              )
            : File(imagePath).existsSync()
                ? Image.file(
                    File(imagePath),
                    width: double.infinity,
                    height: 300,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: double.infinity,
                      height: 300,
                      color: Colors.grey[300],
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.broken_image, size: 60, color: Colors.grey),
                          SizedBox(height: 8),
                          Text('Failed to load local image', style: TextStyle(color: Colors.grey)),
                        ],
                      ),
                    ),
                  )
                : Container(
                    width: double.infinity,
                    height: 300,
                    color: Colors.grey[300],
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.image_not_supported, size: 60, color: Colors.grey),
                        SizedBox(height: 8),
                        Text('Image file not found', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                  ),
      ),
    );
  }

  /// بناء صور متعددة
  Widget _buildMultipleImages() {
    return SizedBox(
      height: 200,
      child: PageView.builder(
        itemCount: widget.post.images.length,
        itemBuilder: (context, index) {
          final imagePath = widget.post.images[index];
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[200],
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: imagePath.startsWith('http')
                      ? Image.network(
                          imagePath,
                          width: double.infinity,
                          height: 200,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            width: double.infinity,
                            height: 200,
                            color: Colors.grey[300],
                            child: const Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.image, size: 40, color: Colors.grey),
                                SizedBox(height: 4),
                                Text('Image', style: TextStyle(color: Colors.grey, fontSize: 12)),
                              ],
                            ),
                          ),
                        )
                      : Container(
                          width: double.infinity,
                          height: 200,
                          color: Colors.grey[300],
                          child: const Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.image, size: 40, color: Colors.grey),
                              SizedBox(height: 4),
                              Text('Local Image', style: TextStyle(color: Colors.grey, fontSize: 12)),
                            ],
                          ),
                        ),
                ),
                // مؤشر الصور المتعددة
                if (widget.post.images.length > 1)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${index + 1}/${widget.post.images.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء إحصائيات التفاعلات
  Widget _buildReactionStats() {
    if (widget.post.totalReactions == 0 &&
        widget.post.commentsCount == 0 &&
        widget.post.sharesCount == 0) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // التفاعلات الاحترافية مثل LinkedIn - قابلة للنقر
          if (widget.post.totalReactions > 0) ...[
            GestureDetector(
              onTap: () => _showReactionsList(),
              child: Row(
                children: [
                  ...widget.post.topReactions.take(3).map((emoji) => Container(
                    margin: const EdgeInsets.only(right: 2),
                    child: _buildProfessionalReactionIcon(emoji),
                  )),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.post.totalReactions}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const Spacer(),

          // التعليقات والمشاركات
          Row(
            children: [
              if (widget.post.commentsCount > 0) ...[
                Text(
                  '${widget.post.commentsCount} comments',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (widget.post.sharesCount > 0) ...[
                  const SizedBox(width: 8),
                  Text(
                    '•',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
              ],
              if (widget.post.sharesCount > 0)
                Text(
                  '${widget.post.sharesCount} shares',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التفاعل (5 أزرار) مع ستايل LinkedIn
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // نظام التفاعلات الموحد المتقدم
          Expanded(
            child: UniversalReactionsSystem(
              currentReaction: _userReactionId,
              contentId: widget.post.id,
              contentType: 'post',
              onReactionChanged: (reactionId) {
                if (reactionId.isEmpty) {
                  // إزالة التفاعل
                  widget.onReaction('');
                } else {
                  // إضافة التفاعل
                  widget.onReaction(FacebookReactions.getReactionEmoji(reactionId));
                }
                _loadUserReaction();
              },
            ),
          ),

          // زر التعليق - رمادي مثل Facebook
          _buildLinkedInActionButton(
            icon: Icons.chat_bubble_outline,
            label: 'Comment',
            onTap: widget.onComment,
          ),

          // زر إعادة النشر - أخضر
          _buildLinkedInActionButton(
            icon: Icons.repeat,
            label: 'Repost',
            onTap: _handleRepost,
          ),

          // زر المشاركة - رمادي (مع الميزة الجديدة)
          _buildLinkedInActionButton(
            icon: Icons.share,
            label: 'Share',
            onTap: _showShareBottomSheet,
          ),
        ],
      ),
    );
  }

  /// بناء زر تفاعل بستايل Facebook الملون
  Widget _buildLinkedInActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isActive = false,
    Color? customColor,
  }) {
    // تحديد الألوان حسب نوع الزر مثل Facebook
    Color buttonColor;
    if (customColor != null) {
      buttonColor = customColor;
    } else {
      switch (label.toLowerCase()) {
        case 'like':
          buttonColor = const Color(0xFF1877F2); // Facebook blue
          break;
        case 'comment':
          buttonColor = const Color(0xFFFF6B35); // Orange للتعليقات
          break;
        case 'repost':
          buttonColor = const Color(0xFF42B883); // Green للإعادة النشر
          break;
        case 'send':
          buttonColor = const Color(0xFF1877F2); // Facebook blue للإرسال
          break;
        case 'share':
          buttonColor = const Color(0xFF9C27B0); // Purple للمشاركة
          break;
        default:
          buttonColor = Colors.grey[700]!;
      }
    }

    return Expanded(
      child: PremiumUIEnhancements.premiumTapEffect(
        onTap: onTap,
        scaleDown: 0.92,
        duration: const Duration(milliseconds: 150),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: isActive ? buttonColor.withOpacity(0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: isActive ? Border.all(
              color: buttonColor.withOpacity(0.3),
              width: 1,
            ) : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: isActive ? buttonColor : buttonColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: isActive ? Colors.white : buttonColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: buttonColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التعامل مع إعادة النشر - النسخة المتقدمة
  void _handleRepost() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => RepostBottomSheet(post: widget.post),
    );
  }

  /// عرض خيارات المشاركة المتقدمة
  void _showShareBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ShareBottomSheet(post: widget.post),
    );
  }

  /// إعادة النشر فور<|im_start|>
  void _repostInstantly() {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      socialService.repostPost(
        originalPost: widget.post,
        reposterName: currentUser.name,
        reposterId: currentUser.phoneNumber,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔄 Post reposted to your feed!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// إعادة النشر مع تعليق
  void _repostWithComment() {
    // فتح شاشة إنشاء منشور مع المنشور الأصلي كمرجع
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔄 Repost with comment feature coming soon!'),
      ),
    );
  }





  /// إرسال رسالة لصاحب المنشور
  void _sendMessage() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please login to send messages')),
      );
      return;
    }

    if (currentUser.phoneNumber == widget.post.authorId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('You cannot message yourself')),
      );
      return;
    }

    // إنشاء UserModel للمؤلف
    final authorUser = UserModel(
      phoneNumber: widget.post.authorId,
      name: widget.post.authorName,
      gender: Gender.male,
      age: 0,
      country: '',
      city: '',
      isOnline: false,
      lastSeen: DateTime.now(),
      registrationDate: DateTime.now(),
    );

    // الانتقال لشاشة الدردشة
    Navigator.of(context).pushNamed(
      '/chat',
      arguments: authorUser,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening chat with ${widget.post.authorName}...')),
    );
  }





  /// عرض قائمة الأشخاص الذين تفاعلوا مع المنشور
  void _showReactionsList() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ReactionsListSheet(
        postId: widget.post.id,
        totalReactions: widget.post.totalReactions,
      ),
    );
  }

  /// بناء محتوى قائمة التفاعلات
  Widget _buildReactionsListContent(ScrollController scrollController) {
    return Consumer<SocialFeedService>(
      builder: (context, socialService, child) {
        // الحصول على تفاصيل التفاعلات من الخدمة
        final reactionDetails = socialService.getPostReactionDetails(widget.post.id);

        if (reactionDetails.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.sentiment_neutral,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No reactions yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: reactionDetails.length,
          itemBuilder: (context, index) {
            final reaction = reactionDetails[index];
            return _buildReactionItem(reaction);
          },
        );
      },
    );
  }

  /// بناء عنصر تفاعل واحد
  Widget _buildReactionItem(Map<String, dynamic> reaction) {
    final userName = reaction['userName'] ?? 'Unknown User';
    final userPhone = reaction['userPhone'] ?? '';
    final emoji = reaction['emoji'] ?? '👍';
    final timestamp = reaction['timestamp'] as DateTime?;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 20,
            backgroundColor: const Color(0xFF1877F2),
            child: Text(
              userName.isNotEmpty ? userName[0].toUpperCase() : '?',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),

          const SizedBox(width: 12),

          // معلومات المستخدم
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1C1E21),
                  ),
                ),
                if (timestamp != null)
                  Text(
                    _formatTimestamp(timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),

          // التفاعل الاحترافي مثل LinkedIn
          _buildProfessionalReactionSticker(emoji),
        ],
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// بناء ملصق تفاعل احترافي مثل LinkedIn
  Widget _buildProfessionalReactionSticker(String emoji) {
    final reactionData = _getReactionData(emoji);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: reactionData['color'].withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: reactionData['color'].withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: reactionData['color'],
              shape: BoxShape.circle,
            ),
            child: Icon(
              reactionData['icon'],
              color: Colors.white,
              size: 14,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            reactionData['name'],
            style: TextStyle(
              color: reactionData['color'],
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على بيانات التفاعل
  Map<String, dynamic> _getReactionData(String emoji) {
    switch (emoji) {
      case '👍':
        return {
          'name': 'Like',
          'color': const Color(0xFF1877F2),
          'icon': Icons.thumb_up,
        };
      case '❤️':
        return {
          'name': 'Love',
          'color': const Color(0xFFE91E63),
          'icon': Icons.favorite,
        };
      case '😂':
        return {
          'name': 'Haha',
          'color': const Color(0xFFFFC107),
          'icon': Icons.sentiment_very_satisfied,
        };
      case '😮':
        return {
          'name': 'Wow',
          'color': const Color(0xFF9C27B0),
          'icon': Icons.sentiment_very_satisfied,
        };
      case '😢':
        return {
          'name': 'Sad',
          'color': const Color(0xFF607D8B),
          'icon': Icons.sentiment_very_dissatisfied,
        };
      case '😡':
        return {
          'name': 'Angry',
          'color': const Color(0xFFFF5722),
          'icon': Icons.sentiment_very_dissatisfied,
        };
      case '🙏':
        return {
          'name': 'Support',
          'color': const Color(0xFF4CAF50),
          'icon': Icons.volunteer_activism,
        };
      case '👏':
        return {
          'name': 'Clap',
          'color': const Color(0xFFFF9800),
          'icon': Icons.celebration,
        };
      case '🐔':
        return {
          'name': 'Coward',
          'color': const Color(0xFF795548),
          'icon': Icons.pets,
        };
      default:
        return {
          'name': 'Like',
          'color': const Color(0xFF1877F2),
          'icon': Icons.thumb_up,
        };
    }
  }



  /// بناء عناصر قائمة المنشور
  List<PopupMenuEntry<String>> _buildPostMenuItems() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;
    final isOwnPost = currentUser?.phoneNumber == widget.post.authorId;

    if (isOwnPost) {
      // قائمة المنشور الخاص بالمستخدم
      return [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 20, color: Color(0xFFD32F2F)),
              SizedBox(width: 8),
              Text('Edit Post'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'copy_link',
          child: Row(
            children: [
              Icon(Icons.link, size: 20),
              SizedBox(width: 8),
              Text('Copy Link'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 20, color: Colors.red),
              SizedBox(width: 8),
              Text('Delete Post', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ];
    } else {
      // قائمة منشور شخص آخر
      return [
        const PopupMenuItem(
          value: 'save_to_favorites',
          child: Row(
            children: [
              Icon(Icons.bookmark, size: 20, color: Color(0xFF1877F2)),
              SizedBox(width: 8),
              Text('Save to Favorites'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'interested',
          child: Row(
            children: [
              Icon(Icons.thumb_up, size: 20, color: Colors.green),
              SizedBox(width: 8),
              Text('Interested'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'not_interested',
          child: Row(
            children: [
              Icon(Icons.thumb_down, size: 20, color: Colors.orange),
              SizedBox(width: 8),
              Text('Not Interested'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'hide_post',
          child: Row(
            children: [
              Icon(Icons.visibility_off, size: 20),
              SizedBox(width: 8),
              Text('Hide Post'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'hide_30_days',
          child: Row(
            children: [
              Icon(Icons.schedule, size: 20),
              SizedBox(width: 8),
              Text('Hide for 30 Days'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'copy_link',
          child: Row(
            children: [
              Icon(Icons.link, size: 20),
              SizedBox(width: 8),
              Text('Copy Link'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'block_user',
          child: Row(
            children: [
              Icon(Icons.block, size: 20, color: Colors.red),
              SizedBox(width: 8),
              Text('Block User', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'report_post',
          child: Row(
            children: [
              Icon(Icons.report, size: 20, color: Colors.red),
              SizedBox(width: 8),
              Text('Report Post', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ];
    }
  }

  /// التعامل مع إجراءات قائمة المنشور
  void _handlePostMenuAction(String action) {
    final postManagement = Provider.of<PostManagementService>(context, listen: false);

    switch (action) {
      case 'edit':
        widget.onEdit?.call();
        break;

      case 'delete':
        widget.onDelete?.call();
        break;

      case 'save_to_favorites':
        _saveToFavorites();
        break;

      case 'copy_link':
        _copyPostLink();
        break;

      case 'interested':
        _markInterested();
        break;

      case 'not_interested':
        _markNotInterested(postManagement);
        break;

      case 'hide_post':
        _hidePost(postManagement);
        break;

      case 'hide_30_days':
        _hidePostTemporarily(postManagement);
        break;

      case 'block_user':
        _blockUser(postManagement);
        break;

      case 'report_post':
        _reportPost(postManagement);
        break;
    }
  }

  /// حفظ المنشور في المفضلة
  void _saveToFavorites() {
    final savedItemsService = Provider.of<SavedItemsService>(context, listen: false);

    if (savedItemsService.isPostSaved(widget.post.id)) {
      // إلغاء الحفظ
      savedItemsService.unsavePost(widget.post.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('📌 Post removed from favorites'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      // حفظ المنشور
      savedItemsService.savePost(widget.post);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('📌 Post saved to favorites'),
          backgroundColor: Color(0xFF1877F2),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// نسخ رابط المنشور
  void _copyPostLink() {
    final postLink = 'https://arzatalk.com/post/${widget.post.id}';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Post link copied: $postLink'),
        action: SnackBarAction(
          label: 'OK',
          onPressed: () {},
        ),
      ),
    );
  }

  /// تسجيل الاهتمام
  void _markInterested() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('✅ Marked as interested! We\'ll show you more posts like this.'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// تسجيل عدم الاهتمام
  void _markNotInterested(PostManagementService postManagement) {
    postManagement.markNotInterested(widget.post.id, widget.post.authorId).then((success) {
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('👎 Marked as not interested. We\'ll show you fewer posts like this.'),
            backgroundColor: Colors.orange,
            action: SnackBarAction(
              label: 'Undo',
              textColor: Colors.white,
              onPressed: () {
                postManagement.undoNotInterested(widget.post.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('🔄 Undone')),
                );
              },
            ),
          ),
        );
      }
    });
  }

  /// إخفاء المنشور
  void _hidePost(PostManagementService postManagement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hide Post'),
        content: const Text('This post will be hidden from your feed. You can unhide it from settings.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              postManagement.hidePost(widget.post.id, 'User choice').then((success) {
                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('🙈 Post hidden'),
                      action: SnackBarAction(
                        label: 'Undo',
                        onPressed: () {
                          postManagement.unhidePost(widget.post.id);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('👁️ Post unhidden')),
                          );
                        },
                      ),
                    ),
                  );
                }
              });
            },
            child: const Text('Hide', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// إخفاء المنشور مؤقتاً
  void _hidePostTemporarily(PostManagementService postManagement) {
    postManagement.hidePostTemporarily(widget.post.id).then((success) {
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('⏰ Post hidden for 30 days'),
            action: SnackBarAction(
              label: 'Undo',
              onPressed: () {
                postManagement.unhidePost(widget.post.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('👁️ Post unhidden')),
                );
              },
            ),
          ),
        );
      }
    });
  }

  /// حظر المستخدم
  void _blockUser(PostManagementService postManagement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Text('Are you sure you want to block ${widget.post.authorName}? You won\'t see their posts anymore.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              postManagement.blockUser(widget.post.authorId).then((success) {
                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('🚫 ${widget.post.authorName} has been blocked'),
                      backgroundColor: Colors.red,
                      action: SnackBarAction(
                        label: 'Undo',
                        textColor: Colors.white,
                        onPressed: () {
                          postManagement.unblockUser(widget.post.authorId);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('✅ ${widget.post.authorName} has been unblocked')),
                          );
                        },
                      ),
                    ),
                  );
                }
              });
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// الإبلاغ عن المنشور
  void _reportPost(PostManagementService postManagement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Post'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Why are you reporting this post?'),
            const SizedBox(height: 16),
            ...['Spam', 'Harassment', 'False Information', 'Inappropriate Content', 'Other'].map(
              (reason) => ListTile(
                title: Text(reason),
                onTap: () {
                  Navigator.of(context).pop();
                  postManagement.reportPost(widget.post.id, widget.post.authorId, reason).then((success) {
                    if (success && mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('🚨 Post reported. Thank you for helping keep our community safe.'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  });
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// بناء أيقونة تفاعل احترافية مثل LinkedIn
  Widget _buildProfessionalReactionIcon(String emoji) {
    // تحويل الإيموجي إلى معرف التفاعل
    String? reactionId = _getReactionIdFromEmoji(emoji);
    reactionId ??= 'like';

    // الحصول على لون التفاعل
    Color reactionColor = FacebookReactions.getReactionColor(reactionId);

    // الحصول على الأيقونة الاحترافية
    IconData iconData = _getReactionIconData(reactionId);

    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: reactionColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: reactionColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Icon(
          iconData,
          color: Colors.white,
          size: 12,
        ),
      ),
    );
  }

  /// الحصول على أيقونة التفاعل
  IconData _getReactionIconData(String reactionId) {
    switch (reactionId) {
      case 'like':
        return Icons.thumb_up;
      case 'love':
        return Icons.favorite;
      case 'haha':
        return Icons.sentiment_very_satisfied;
      case 'wow':
        return Icons.sentiment_very_satisfied_outlined;
      case 'sad':
        return Icons.sentiment_very_dissatisfied;
      case 'angry':
        return Icons.sentiment_very_dissatisfied_outlined;
      case 'support':
        return Icons.volunteer_activism;
      case 'clap':
        return Icons.celebration;
      case 'coward':
        return Icons.warning;
      default:
        return Icons.thumb_up;
    }
  }

  /// فتح الملف الشخصي للمستخدم
  void _openUserProfile() async {
    // الحصول على الخدمات قبل العملية غير المتزامنة
    final usersService = Provider.of<UsersService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    // الحصول على بيانات المستخدم الحقيقية
    final realUser = await usersService.getUserByPhone(widget.post.authorId);

    if (!mounted) return;

    UserModel user;
    if (realUser != null) {
      // استخدام البيانات الحقيقية
      user = realUser;
    } else {
      // إنشاء UserModel مؤقت إذا لم توجد البيانات
      user = UserModel(
        phoneNumber: widget.post.authorId,
        name: widget.post.authorName,
        gender: Gender.male,
        age: 25,
        country: 'Unknown',
        city: 'Unknown',
        isOnline: false,
        lastSeen: DateTime.now(),
        registrationDate: DateTime.now(),
      );
    }

    final isCurrentUser = currentUser?.phoneNumber == widget.post.authorId;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          user: user,
          isCurrentUser: isCurrentUser,
        ),
      ),
    );
  }

  /// بناء رأس إعادة النشر
  Widget _buildRepostHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: const BoxDecoration(
        color: Color(0xFFF0F2F5),
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.repeat,
            color: Colors.grey[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            '${widget.post.authorName} reposted',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Text(
            widget.post.timeAgo,
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تعليق إعادة النشر
  Widget _buildRepostComment() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Text(
        widget.post.repostComment!,
        style: const TextStyle(
          fontSize: 16,
          height: 1.4,
          color: Colors.black,
        ),
      ),
    );
  }

  /// بناء المنشور الأصلي
  Widget _buildOriginalPost() {
    final originalPost = widget.post.originalPost!;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xFFE4E6EA),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المنشور الأصلي
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: const Color(0xFF1877F2),
                  child: Text(
                    originalPost.authorName.isNotEmpty
                        ? originalPost.authorName[0].toUpperCase()
                        : '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        originalPost.authorName,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1C1E21),
                        ),
                      ),
                      Text(
                        originalPost.timeAgo,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // محتوى المنشور الأصلي
          if (originalPost.content.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                originalPost.content,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF1C1E21),
                ),
              ),
            ),

          // صور المنشور الأصلي
          if (originalPost.images.isNotEmpty)
            Container(
              margin: const EdgeInsets.all(12),
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: const Center(
                child: Icon(
                  Icons.image,
                  color: Colors.grey,
                  size: 48,
                ),
              ),
            ),

          // فيديوهات المنشور الأصلي
          if (originalPost.videos.isNotEmpty)
            Container(
              margin: const EdgeInsets.all(12),
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: const Center(
                child: Icon(
                  Icons.play_circle_outline,
                  color: Colors.grey,
                  size: 48,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

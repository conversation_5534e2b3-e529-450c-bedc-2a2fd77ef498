import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/reaction_model.dart';
import '../services/reactions_service.dart';

/// Widget لعرض ردود الفعل على الرسائل
class MessageReactionsWidget extends StatelessWidget {
  final String messageId;
  final String currentUserId;
  final String currentUserName;

  const MessageReactionsWidget({
    super.key,
    required this.messageId,
    required this.currentUserId,
    required this.currentUserName,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ReactionsService>(
      builder: (context, reactionsService, child) {
        final reactionStats = reactionsService.getReactionStatsForMessage(messageId, currentUserId);
        
        if (reactionStats.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.only(top: 4),
          child: Wrap(
            spacing: 4,
            runSpacing: 4,
            children: reactionStats.map((stat) => _buildReactionChip(
              context,
              stat,
              reactionsService,
            )).toList(),
          ),
        );
      },
    );
  }

  /// بناء رقاقة رد الفعل
  Widget _buildReactionChip(
    BuildContext context,
    ReactionStats stat,
    ReactionsService reactionsService,
  ) {
    return GestureDetector(
      onTap: () => _handleReactionTap(context, stat.emoji, reactionsService),
      onLongPress: () => _showReactionDetails(context, stat),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: stat.currentUserReacted 
              ? const Color(0xFFD32F2F).withOpacity(0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: stat.currentUserReacted 
                ? const Color(0xFFD32F2F)
                : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              stat.emoji,
              style: const TextStyle(fontSize: 16),
            ),
            if (stat.count > 1) ...[
              const SizedBox(width: 4),
              Text(
                '${stat.count}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: stat.currentUserReacted 
                      ? const Color(0xFFD32F2F)
                      : Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// التعامل مع النقر على رد الفعل
  void _handleReactionTap(
    BuildContext context,
    String emoji,
    ReactionsService reactionsService,
  ) {
    reactionsService.addReaction(
      messageId: messageId,
      emoji: emoji,
      userId: currentUserId,
      userName: currentUserName,
    );
  }

  /// عرض تفاصيل ردود الفعل
  void _showReactionDetails(BuildContext context, ReactionStats stat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Text(
              stat.emoji,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(width: 8),
            Text(
              '${stat.count} ${stat.count == 1 ? 'person' : 'people'}',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...stat.userNames.map((name) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Text(
                name,
                style: const TextStyle(fontSize: 14),
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Widget لإضافة ردود الفعل السريعة
class QuickReactionsWidget extends StatelessWidget {
  final String messageId;
  final String currentUserId;
  final String currentUserName;
  final VoidCallback? onReactionAdded;

  const QuickReactionsWidget({
    super.key,
    required this.messageId,
    required this.currentUserId,
    required this.currentUserName,
    this.onReactionAdded,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ReactionsService>(
      builder: (context, reactionsService, child) {
        final quickEmojis = reactionsService.getQuickEmojis();
        
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: quickEmojis.take(8).map((emoji) => _buildQuickReaction(
              context,
              emoji,
              reactionsService,
            )).toList(),
          ),
        );
      },
    );
  }

  /// بناء رد فعل سريع
  Widget _buildQuickReaction(
    BuildContext context,
    String emoji,
    ReactionsService reactionsService,
  ) {
    return GestureDetector(
      onTap: () => _addReaction(context, emoji, reactionsService),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 20),
          ),
        ),
      ),
    );
  }

  /// إضافة رد فعل
  void _addReaction(
    BuildContext context,
    String emoji,
    ReactionsService reactionsService,
  ) {
    reactionsService.addReaction(
      messageId: messageId,
      emoji: emoji,
      userId: currentUserId,
      userName: currentUserName,
    );
    
    onReactionAdded?.call();
  }
}

/// Widget لعرض جميع الإيموجي
class EmojiPickerWidget extends StatefulWidget {
  final Function(String) onEmojiSelected;

  const EmojiPickerWidget({
    super.key,
    required this.onEmojiSelected,
  });

  @override
  State<EmojiPickerWidget> createState() => _EmojiPickerWidgetState();
}

class _EmojiPickerWidgetState extends State<EmojiPickerWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _categories = [
    'Smileys',
    'Emotions',
    'Gestures',
    'People',
    'Animals',
    'Food',
    'Activities',
    'Objects',
    'Symbols',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReactionsService>(
      builder: (context, reactionsService, child) {
        return Container(
          height: 300,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // تبويبات الفئات
              TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: const Color(0xFFD32F2F),
                unselectedLabelColor: Colors.grey,
                indicatorColor: const Color(0xFFD32F2F),
                tabs: _categories.map((category) => Tab(
                  text: category,
                )).toList(),
              ),
              
              // محتوى الإيموجي
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: _categories.map((category) => _buildEmojiGrid(
                    context,
                    category,
                    reactionsService,
                  )).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء شبكة الإيموجي
  Widget _buildEmojiGrid(
    BuildContext context,
    String category,
    ReactionsService reactionsService,
  ) {
    // استخدام StickersService للحصول على الإيموجي
    return Consumer<ReactionsService>(
      builder: (context, service, child) {
        // هنا يمكن استخدام StickersService للحصول على الإيموجي
        // لكن للبساطة سنستخدم قائمة ثابتة
        final emojis = _getEmojisForCategory(category);
        
        return GridView.builder(
          padding: const EdgeInsets.all(8),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
            childAspectRatio: 1,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: emojis.length,
          itemBuilder: (context, index) {
            final emoji = emojis[index];
            return GestureDetector(
              onTap: () => widget.onEmojiSelected(emoji),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[100],
                ),
                child: Center(
                  child: Text(
                    emoji,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// الحصول على إيموجي الفئة
  List<String> _getEmojisForCategory(String category) {
    // قائمة مبسطة للإيموجي
    switch (category) {
      case 'Smileys':
        return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰'];
      case 'Emotions':
        return ['😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠'];
      case 'Gestures':
        return ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️'];
      default:
        return ['😊', '👍', '❤️', '😂', '😮', '😢', '😡', '👏'];
    }
  }
}

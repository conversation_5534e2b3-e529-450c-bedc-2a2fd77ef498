import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// شاشة إعدادات اللغة والمنطقة
class LanguageRegionSettingsScreen extends StatefulWidget {
  const LanguageRegionSettingsScreen({super.key});

  @override
  State<LanguageRegionSettingsScreen> createState() => _LanguageRegionSettingsScreenState();
}

class _LanguageRegionSettingsScreenState extends State<LanguageRegionSettingsScreen> {
  String _selectedLanguage = 'English';
  String _selectedRegion = 'United States';
  String _dateFormat = 'MM/DD/YYYY';
  String _timeFormat = '12 Hour';
  String _numberFormat = '1,234.56';

  // قائمة اللغات المدعومة
  final List<Map<String, String>> _languages = [
    {'code': 'en', 'name': 'English', 'nativeName': 'English'},
    {'code': 'ar', 'name': 'Arabic', 'nativeName': 'العربية'},
    {'code': 'fr', 'name': 'French', 'nativeName': 'Français'},
    {'code': 'es', 'name': 'Spanish', 'nativeName': 'Español'},
    {'code': 'de', 'name': 'German', 'nativeName': 'Deutsch'},
    {'code': 'it', 'name': 'Italian', 'nativeName': 'Italiano'},
    {'code': 'pt', 'name': 'Portuguese', 'nativeName': 'Português'},
    {'code': 'ru', 'name': 'Russian', 'nativeName': 'Русский'},
    {'code': 'zh', 'name': 'Chinese', 'nativeName': '中文'},
    {'code': 'ja', 'name': 'Japanese', 'nativeName': '日本語'},
    {'code': 'ko', 'name': 'Korean', 'nativeName': '한국어'},
    {'code': 'hi', 'name': 'Hindi', 'nativeName': 'हिन्दी'},
    {'code': 'tr', 'name': 'Turkish', 'nativeName': 'Türkçe'},
    {'code': 'nl', 'name': 'Dutch', 'nativeName': 'Nederlands'},
    {'code': 'sv', 'name': 'Swedish', 'nativeName': 'Svenska'},
  ];

  // قائمة المناطق
  final List<Map<String, String>> _regions = [
    {'code': 'US', 'name': 'United States'},
    {'code': 'GB', 'name': 'United Kingdom'},
    {'code': 'CA', 'name': 'Canada'},
    {'code': 'AU', 'name': 'Australia'},
    {'code': 'DE', 'name': 'Germany'},
    {'code': 'FR', 'name': 'France'},
    {'code': 'ES', 'name': 'Spain'},
    {'code': 'IT', 'name': 'Italy'},
    {'code': 'JP', 'name': 'Japan'},
    {'code': 'KR', 'name': 'South Korea'},
    {'code': 'CN', 'name': 'China'},
    {'code': 'IN', 'name': 'India'},
    {'code': 'BR', 'name': 'Brazil'},
    {'code': 'MX', 'name': 'Mexico'},
    {'code': 'RU', 'name': 'Russia'},
    {'code': 'SA', 'name': 'Saudi Arabia'},
    {'code': 'AE', 'name': 'United Arab Emirates'},
    {'code': 'EG', 'name': 'Egypt'},
    {'code': 'MA', 'name': 'Morocco'},
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedLanguage = prefs.getString('selected_language') ?? 'English';
      _selectedRegion = prefs.getString('selected_region') ?? 'United States';
      _dateFormat = prefs.getString('date_format') ?? 'MM/DD/YYYY';
      _timeFormat = prefs.getString('time_format') ?? '12 Hour';
      _numberFormat = prefs.getString('number_format') ?? '1,234.56';
    });
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_language', _selectedLanguage);
    await prefs.setString('selected_region', _selectedRegion);
    await prefs.setString('date_format', _dateFormat);
    await prefs.setString('time_format', _timeFormat);
    await prefs.setString('number_format', _numberFormat);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Language & Region',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1877F2),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // قسم اللغة
            _buildSectionCard(
              title: 'Language',
              icon: Icons.language,
              children: [
                _buildListTile(
                  title: 'App Language',
                  subtitle: _selectedLanguage,
                  onTap: _selectLanguage,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // قسم المنطقة
            _buildSectionCard(
              title: 'Region',
              icon: Icons.public,
              children: [
                _buildListTile(
                  title: 'Country/Region',
                  subtitle: _selectedRegion,
                  onTap: _selectRegion,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // قسم التنسيق
            _buildSectionCard(
              title: 'Format Settings',
              icon: Icons.format_list_numbered,
              children: [
                _buildListTile(
                  title: 'Date Format',
                  subtitle: _dateFormat,
                  onTap: _selectDateFormat,
                ),
                _buildListTile(
                  title: 'Time Format',
                  subtitle: _timeFormat,
                  onTap: _selectTimeFormat,
                ),
                _buildListTile(
                  title: 'Number Format',
                  subtitle: _numberFormat,
                  onTap: _selectNumberFormat,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // معلومات إضافية
            _buildInfoCard(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1877F2).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF1877F2)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1877F2),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// بناء عنصر قائمة
  Widget _buildListTile({
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ),
      onTap: onTap,
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue[700]),
              const SizedBox(width: 8),
              Text(
                'Information',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Language and region settings affect how dates, times, numbers, and text are displayed throughout the app. Some changes may require restarting the app.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue[600],
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار اللغة
  void _selectLanguage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _languages.length,
            itemBuilder: (context, index) {
              final language = _languages[index];
              return RadioListTile<String>(
                title: Text(language['name']!),
                subtitle: Text(language['nativeName']!),
                value: language['name']!,
                groupValue: _selectedLanguage,
                onChanged: (value) {
                  setState(() => _selectedLanguage = value!);
                  _saveSettings();
                  Navigator.of(context).pop();
                  _showRestartDialog();
                },
                activeColor: const Color(0xFF1877F2),
              );
            },
          ),
        ),
      ),
    );
  }

  /// اختيار المنطقة
  void _selectRegion() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Region'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _regions.length,
            itemBuilder: (context, index) {
              final region = _regions[index];
              return RadioListTile<String>(
                title: Text(region['name']!),
                value: region['name']!,
                groupValue: _selectedRegion,
                onChanged: (value) {
                  setState(() => _selectedRegion = value!);
                  _saveSettings();
                  Navigator.of(context).pop();
                },
                activeColor: const Color(0xFF1877F2),
              );
            },
          ),
        ),
      ),
    );
  }

  /// اختيار تنسيق التاريخ
  void _selectDateFormat() {
    final formats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'DD-MM-YYYY'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Date Format'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats.map((format) => RadioListTile<String>(
            title: Text(format),
            value: format,
            groupValue: _dateFormat,
            onChanged: (value) {
              setState(() => _dateFormat = value!);
              _saveSettings();
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF1877F2),
          )).toList(),
        ),
      ),
    );
  }

  /// اختيار تنسيق الوقت
  void _selectTimeFormat() {
    final formats = ['12 Hour', '24 Hour'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Time Format'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats.map((format) => RadioListTile<String>(
            title: Text(format),
            subtitle: Text(format == '12 Hour' ? '1:30 PM' : '13:30'),
            value: format,
            groupValue: _timeFormat,
            onChanged: (value) {
              setState(() => _timeFormat = value!);
              _saveSettings();
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF1877F2),
          )).toList(),
        ),
      ),
    );
  }

  /// اختيار تنسيق الأرقام
  void _selectNumberFormat() {
    final formats = ['1,234.56', '1.234,56', '1 234,56', '1234.56'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Number Format'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats.map((format) => RadioListTile<String>(
            title: Text(format),
            value: format,
            groupValue: _numberFormat,
            onChanged: (value) {
              setState(() => _numberFormat = value!);
              _saveSettings();
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF1877F2),
          )).toList(),
        ),
      ),
    );
  }

  /// عرض حوار إعادة التشغيل
  void _showRestartDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Restart Required'),
        content: const Text('Please restart the app to apply the new language settings.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../services/sound_service.dart';

class VoiceRecorder extends StatefulWidget {
  final Function(String audioPath, int duration) onVoiceRecorded;
  final bool isRecording;
  final Function(bool) onRecordingStateChanged;

  const VoiceRecorder({
    super.key,
    required this.onVoiceRecorded,
    required this.isRecording,
    required this.onRecordingStateChanged,
  });

  @override
  State<VoiceRecorder> createState() => _VoiceRecorderState();
}

class _VoiceRecorderState extends State<VoiceRecorder>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final SoundService _soundService = SoundService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _startRecording() async {
    try {
      // محاكاة بدء التسجيل
      widget.onRecordingStateChanged(true);
      _animationController.repeat(reverse: true);

      // تشغيل صوت بدء التسجيل
      _soundService.playRecordingSound(isStart: true);

      // عرض رسالة للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('بدء التسجيل... (محاكاة)'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في بدء التسجيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      _animationController.stop();
      _animationController.reset();

      widget.onRecordingStateChanged(false);

      // محاكاة إنهاء التسجيل مع مدة عشوائية واقعية
      final random = DateTime.now().millisecondsSinceEpoch % 10 + 3; // 3-12 ثانية
      final dummyPath = 'voice_${DateTime.now().millisecondsSinceEpoch}.m4a';

      widget.onVoiceRecorded(dummyPath, random);

      // تشغيل صوت إرسال الرسالة الصوتية
      _soundService.playVoiceMessageSentSound();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🎤 Voice message recorded (${random}s)'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Recording error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cancelRecording() async {
    try {
      _animationController.stop();
      _animationController.reset();

      widget.onRecordingStateChanged(false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء التسجيل'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إلغاء التسجيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isRecording) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر الإلغاء
          IconButton(
            icon: const Icon(Icons.close, color: Colors.red),
            onPressed: _cancelRecording,
          ),

          // مؤشر التسجيل
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.mic, color: Colors.white, size: 16),
                      SizedBox(width: 4),
                      Text(
                        'Recording...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // زر الإرسال
          IconButton(
            icon: const Icon(Icons.send, color: Color(0xFFD32F2F)),
            onPressed: _stopRecording,
          ),
        ],
      );
    }

    return GestureDetector(
      onLongPress: _startRecording,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: const Color(0xFFD32F2F),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFD32F2F).withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Icon(
          Icons.mic,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'link_preview_model.dart';

/// نموذج المنشور
class PostModel {
  final String id;
  final String authorId;
  final String authorName;
  final String authorAvatar;
  final String content;
  final List<String> images;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final PostType type;
  final PostPrivacy privacy;
  final bool isEdited;
  final bool isDeleted;
  final Map<String, int> reactions;
  final int commentsCount;
  final int sharesCount; // عدد المشاركات الخارجية
  final int repostsCount; // عدد إعادات النشر
  final String? location;
  final List<String> tags;
  final List<String> videos;
  final String? musicTitle;
  final String? musicUrl;
  final String? feeling;
  final String? backgroundColor;
  final List<String> taggedUsers;
  final bool isLiveStream;
  final String? originalPostId; // معرف المنشور الأصلي في حالة إعادة النشر
  final bool isRepost; // هل هذا المنشور إعادة نشر
  final PostModel? originalPost; // المنشور الأصلي الكامل
  final String? repostComment; // تعليق إعادة النشر
  final int? repostCount; // عدد إعادات النشر
  final List<LinkPreviewModel> linkPreviews; // معاينات الروابط

  PostModel({
    required this.id,
    required this.authorId,
    required this.authorName,
    required this.authorAvatar,
    required this.content,
    this.images = const [],
    required this.createdAt,
    this.updatedAt,
    this.type = PostType.text,
    this.privacy = PostPrivacy.public,
    this.isEdited = false,
    this.isDeleted = false,
    this.reactions = const {},
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.repostsCount = 0,
    this.location,
    this.tags = const [],
    this.videos = const [],
    this.musicTitle,
    this.musicUrl,
    this.feeling,
    this.backgroundColor,
    this.taggedUsers = const [],
    this.isLiveStream = false,
    this.originalPostId,
    this.isRepost = false,
    this.originalPost,
    this.repostComment,
    this.repostCount,
    this.linkPreviews = const [],
  });

  factory PostModel.fromMap(Map<String, dynamic> map) {
    return PostModel(
      id: map['id'] ?? '',
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? '',
      authorAvatar: map['authorAvatar'] ?? '',
      content: map['content'] ?? '',
      images: List<String>.from(map['images'] ?? []),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
      type: PostType.values.firstWhere(
        (e) => e.toString() == 'PostType.${map['type']}',
        orElse: () => PostType.text,
      ),
      privacy: PostPrivacy.values.firstWhere(
        (e) => e.toString() == 'PostPrivacy.${map['privacy']}',
        orElse: () => PostPrivacy.public,
      ),
      isEdited: map['isEdited'] ?? false,
      isDeleted: map['isDeleted'] ?? false,
      reactions: Map<String, int>.from(map['reactions'] ?? {}),
      commentsCount: map['commentsCount'] ?? 0,
      sharesCount: map['sharesCount'] ?? 0,
      repostsCount: map['repostsCount'] ?? 0,
      location: map['location'],
      tags: List<String>.from(map['tags'] ?? []),
      videos: List<String>.from(map['videos'] ?? []),
      musicTitle: map['musicTitle'],
      musicUrl: map['musicUrl'],
      feeling: map['feeling'],
      backgroundColor: map['backgroundColor'],
      taggedUsers: List<String>.from(map['taggedUsers'] ?? []),
      isLiveStream: map['isLiveStream'] ?? false,
      originalPostId: map['originalPostId'],
      isRepost: map['isRepost'] ?? false,
      originalPost: map['originalPost'] != null
          ? PostModel.fromMap(map['originalPost'])
          : null,
      repostComment: map['repostComment'],
      repostCount: map['repostCount'],
      linkPreviews: (map['linkPreviews'] as List<dynamic>?)
          ?.map((preview) => LinkPreviewModel.fromMap(preview))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'authorId': authorId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'content': content,
      'images': images,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'type': type.toString().split('.').last,
      'privacy': privacy.toString().split('.').last,
      'isEdited': isEdited,
      'isDeleted': isDeleted,
      'reactions': reactions,
      'commentsCount': commentsCount,
      'sharesCount': sharesCount,
      'repostsCount': repostsCount,
      'location': location,
      'tags': tags,
      'videos': videos,
      'musicTitle': musicTitle,
      'musicUrl': musicUrl,
      'feeling': feeling,
      'backgroundColor': backgroundColor,
      'taggedUsers': taggedUsers,
      'isLiveStream': isLiveStream,
      'originalPostId': originalPostId,
      'isRepost': isRepost,
      'originalPost': originalPost?.toMap(),
      'repostComment': repostComment,
      'repostCount': repostCount,
      'linkPreviews': linkPreviews.map((preview) => preview.toMap()).toList(),
    };
  }

  PostModel copyWith({
    String? id,
    String? authorId,
    String? authorName,
    String? authorAvatar,
    String? content,
    List<String>? images,
    DateTime? createdAt,
    DateTime? updatedAt,
    PostType? type,
    PostPrivacy? privacy,
    bool? isEdited,
    bool? isDeleted,
    Map<String, int>? reactions,
    int? commentsCount,
    int? sharesCount,
    int? repostsCount,
    String? location,
    List<String>? tags,
    List<String>? videos,
    String? musicUrl,
    String? musicTitle,
    String? feeling,
    String? backgroundColor,
    List<String>? taggedUsers,
    bool? isLiveStream,
    String? originalPostId,
    bool? isRepost,
    PostModel? originalPost,
    String? repostComment,
    int? repostCount,
    List<LinkPreviewModel>? linkPreviews,
  }) {
    return PostModel(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      content: content ?? this.content,
      images: images ?? this.images,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      privacy: privacy ?? this.privacy,
      isEdited: isEdited ?? this.isEdited,
      isDeleted: isDeleted ?? this.isDeleted,
      reactions: reactions ?? this.reactions,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      repostsCount: repostsCount ?? this.repostsCount,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      videos: videos ?? this.videos,
      musicUrl: musicUrl ?? this.musicUrl,
      musicTitle: musicTitle ?? this.musicTitle,
      feeling: feeling ?? this.feeling,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      taggedUsers: taggedUsers ?? this.taggedUsers,
      isLiveStream: isLiveStream ?? this.isLiveStream,
      originalPostId: originalPostId ?? this.originalPostId,
      isRepost: isRepost ?? this.isRepost,
      originalPost: originalPost ?? this.originalPost,
      repostComment: repostComment ?? this.repostComment,
      repostCount: repostCount ?? this.repostCount,
      linkPreviews: linkPreviews ?? this.linkPreviews,
    );
  }

  /// الحصول على إجمالي التفاعلات
  int get totalReactions {
    return reactions.values.fold(0, (sum, count) => sum + count);
  }

  /// التحقق من وجود تفاعل معين
  bool hasReaction(String emoji) {
    return reactions.containsKey(emoji) && reactions[emoji]! > 0;
  }

  /// الحصول على أكثر التفاعلات
  List<String> get topReactions {
    final sortedReactions = reactions.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return sortedReactions.take(3).map((e) => e.key).toList();
  }

  /// التحقق من إمكانية التعديل
  bool canEdit(String currentUserId) {
    return authorId == currentUserId && !isDeleted;
  }

  /// التحقق من إمكانية الحذف
  bool canDelete(String currentUserId) {
    return authorId == currentUserId && !isDeleted;
  }

  /// الحصول على نص الوقت المنسق
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  /// الحصول على نص الخصوصية
  String get privacyText {
    switch (privacy) {
      case PostPrivacy.public:
        return 'Public';
      case PostPrivacy.friends:
        return 'Friends';
      case PostPrivacy.private:
        return 'Only me';
    }
  }

  /// أيقونة الخصوصية
  IconData get privacyIcon {
    switch (privacy) {
      case PostPrivacy.public:
        return Icons.public;
      case PostPrivacy.friends:
        return Icons.people;
      case PostPrivacy.private:
        return Icons.lock;
    }
  }
}

/// نوع المنشور
enum PostType {
  text,
  image,
  video,
  status,
  liveStream,
  music,
  location,
  feeling,
}

/// خصوصية المنشور
enum PostPrivacy {
  public,
  friends,
  private,
}

/// نموذج التعليق
class CommentModel {
  final String id;
  final String postId;
  final String authorId;
  final String authorName;
  final String authorAvatar;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isEdited;
  final bool isDeleted;
  final Map<String, String> reactions; // userId -> emoji
  final List<CommentModel> replies;
  final int likesCount;
  final int reactionsCount;
  final String? parentCommentId; // للردود على التعليقات
  final List<String> images; // URLs للصور
  final List<String> videos; // URLs للفيديوهات

  CommentModel({
    required this.id,
    required this.postId,
    required this.authorId,
    required this.authorName,
    required this.authorAvatar,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.isEdited = false,
    this.isDeleted = false,
    this.reactions = const {},
    this.replies = const [],
    this.likesCount = 0,
    this.reactionsCount = 0,
    this.parentCommentId,
    this.images = const [],
    this.videos = const [],
  });

  factory CommentModel.fromMap(Map<String, dynamic> map) {
    return CommentModel(
      id: map['id'] ?? '',
      postId: map['postId'] ?? '',
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? '',
      authorAvatar: map['authorAvatar'] ?? '',
      content: map['content'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
      isEdited: map['isEdited'] ?? false,
      isDeleted: map['isDeleted'] ?? false,
      reactions: Map<String, String>.from(map['reactions'] ?? {}),
      replies: (map['replies'] as List<dynamic>?)
          ?.map((reply) => CommentModel.fromMap(reply))
          .toList() ?? [],
      likesCount: map['likesCount'] ?? 0,
      reactionsCount: map['reactionsCount'] ?? 0,
      parentCommentId: map['parentCommentId'],
      images: List<String>.from(map['images'] ?? []),
      videos: List<String>.from(map['videos'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'authorId': authorId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'content': content,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'isEdited': isEdited,
      'isDeleted': isDeleted,
      'reactions': reactions,
      'replies': replies.map((reply) => reply.toMap()).toList(),
      'likesCount': likesCount,
      'reactionsCount': reactionsCount,
      'parentCommentId': parentCommentId,
      'images': images,
      'videos': videos,
    };
  }

  /// الحصول على نص الوقت المنسق
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  /// الحصول على إجمالي التفاعلات
  int get totalReactions {
    return reactions.length;
  }

  /// التحقق من وجود وسائط
  bool get hasMedia => images.isNotEmpty || videos.isNotEmpty;

  /// التحقق من وجود صور
  bool get hasImages => images.isNotEmpty;

  /// التحقق من وجود فيديوهات
  bool get hasVideos => videos.isNotEmpty;

  /// الحصول على عدد الوسائط الإجمالي
  int get mediaCount => images.length + videos.length;
}

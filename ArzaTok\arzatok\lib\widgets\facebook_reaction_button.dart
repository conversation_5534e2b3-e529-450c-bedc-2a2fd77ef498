import 'package:flutter/material.dart';
import 'facebook_reactions_system.dart';
import 'dart:math' as math;

/// زر التفاعل الذكي مثل Facebook
class FacebookReactionButton extends StatefulWidget {
  final String? currentReaction;
  final Function(String) onReactionChanged;
  final VoidCallback? onShowReactions;

  const FacebookReactionButton({
    Key? key,
    this.currentReaction,
    required this.onReactionChanged,
    this.onShowReactions,
  }) : super(key: key);

  @override
  State<FacebookReactionButton> createState() => _FacebookReactionButtonState();
}

class _FacebookReactionButtonState extends State<FacebookReactionButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;
  
  bool _showingReactions = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    // تشغيل النبض إذا كان هناك تفاعل
    if (widget.currentReaction != null) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(FacebookReactionButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.currentReaction != oldWidget.currentReaction) {
      if (widget.currentReaction != null) {
        _bounceController.forward().then((_) {
          _bounceController.reverse();
          _pulseController.repeat(reverse: true);
        });
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _bounceAnimation]),
        builder: (context, child) {
          final scale = _bounceAnimation.value * _pulseAnimation.value;
          
          return Transform.scale(
            scale: scale,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: _getButtonColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: widget.currentReaction != null
                    ? Border.all(color: _getButtonColor().withOpacity(0.3))
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة التفاعل المتحركة
                  _buildReactionIcon(),
                  
                  const SizedBox(width: 6),
                  
                  // نص التفاعل
                  Text(
                    _getReactionText(),
                    style: TextStyle(
                      color: _getButtonColor(),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildReactionIcon() {
    if (widget.currentReaction == null) {
      return Icon(
        Icons.thumb_up_outlined,
        size: 18,
        color: _getButtonColor(),
      );
    }

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 500),
      tween: Tween(begin: 0.0, end: 2 * math.pi),
      builder: (context, rotation, child) {
        return Transform.rotate(
          angle: rotation * 0.1, // دوران خفيف
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              gradient: _getReactionGradient(),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: _getButtonColor().withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              _getReactionIcon(),
              color: Colors.white,
              size: 14,
            ),
          ),
        );
      },
    );
  }

  Color _getButtonColor() {
    if (widget.currentReaction == null) {
      return const Color(0xFF65676B);
    }

    switch (widget.currentReaction) {
      case 'like':
        return const Color(0xFF1877F2);
      case 'love':
        return const Color(0xFFE91E63);
      case 'haha':
        return const Color(0xFFFFC107);
      case 'wow':
        return const Color(0xFFFF9800);
      case 'sad':
        return const Color(0xFF607D8B);
      case 'angry':
        return const Color(0xFFFF5722);
      default:
        return const Color(0xFF65676B);
    }
  }

  String _getReactionText() {
    if (widget.currentReaction == null) {
      return 'Like';
    }

    switch (widget.currentReaction) {
      case 'like':
        return 'Like';
      case 'love':
        return 'Love';
      case 'haha':
        return 'Haha';
      case 'wow':
        return 'Wow';
      case 'sad':
        return 'Sad';
      case 'angry':
        return 'Angry';
      default:
        return 'Like';
    }
  }

  IconData _getReactionIcon() {
    switch (widget.currentReaction) {
      case 'like':
        return Icons.thumb_up;
      case 'love':
        return Icons.favorite;
      case 'haha':
        return Icons.sentiment_very_satisfied;
      case 'wow':
        return Icons.sentiment_very_satisfied;
      case 'sad':
        return Icons.sentiment_very_dissatisfied;
      case 'angry':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.thumb_up;
    }
  }

  LinearGradient _getReactionGradient() {
    switch (widget.currentReaction) {
      case 'like':
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'love':
        return const LinearGradient(
          colors: [Color(0xFFE91E63), Color(0xFFFF6B9D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'haha':
        return const LinearGradient(
          colors: [Color(0xFFFFC107), Color(0xFFFFD54F)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'wow':
        return const LinearGradient(
          colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'sad':
        return const LinearGradient(
          colors: [Color(0xFF607D8B), Color(0xFF90A4AE)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'angry':
        return const LinearGradient(
          colors: [Color(0xFFFF5722), Color(0xFFFF7043)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF1877F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  void _handleTap() {
    if (widget.currentReaction == null) {
      // إضافة Like افتراضي
      widget.onReactionChanged('like');
    } else {
      // إزالة التفاعل الحالي
      widget.onReactionChanged('');
    }
  }

  void _handleLongPress() {
    if (_showingReactions) return;
    
    _showReactionsOverlay();
    widget.onShowReactions?.call();
  }

  void _showReactionsOverlay() {
    if (_overlayEntry != null) return;

    _showingReactions = true;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => FacebookReactionsSystem(
        currentReaction: widget.currentReaction,
        onReactionSelected: (reactionId) {
          widget.onReactionChanged(reactionId);
          _removeOverlay();
        },
        onClose: _removeOverlay,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _showingReactions = false;
    }
  }
}

/// ويدجت تأثير الجسيمات عند التفاعل
class ReactionParticleEffect extends StatefulWidget {
  final String reactionId;
  final Color color;

  const ReactionParticleEffect({
    Key? key,
    required this.reactionId,
    required this.color,
  }) : super(key: key);

  @override
  State<ReactionParticleEffect> createState() => _ReactionParticleEffectState();
}

class _ReactionParticleEffectState extends State<ReactionParticleEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _generateParticles();
    _controller.forward();
  }

  void _generateParticles() {
    final random = math.Random();
    _particles = List.generate(12, (index) {
      final angle = (index / 12) * 2 * math.pi;
      return Particle(
        angle: angle,
        speed: random.nextDouble() * 2 + 1,
        size: random.nextDouble() * 4 + 2,
        color: widget.color,
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(100, 100),
          painter: ParticleEffectPainter(
            particles: _particles,
            progress: _controller.value,
          ),
        );
      },
    );
  }
}

class Particle {
  final double angle;
  final double speed;
  final double size;
  final Color color;

  Particle({
    required this.angle,
    required this.speed,
    required this.size,
    required this.color,
  });
}

class ParticleEffectPainter extends CustomPainter {
  final List<Particle> particles;
  final double progress;

  ParticleEffectPainter({
    required this.particles,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    for (final particle in particles) {
      final distance = particle.speed * progress * 30;
      final x = center.dx + math.cos(particle.angle) * distance;
      final y = center.dy + math.sin(particle.angle) * distance;

      final paint = Paint()
        ..color = particle.color.withOpacity(1.0 - progress)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(x, y),
        particle.size * (1.0 - progress * 0.5),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../services/social_feed_service.dart';
import '../services/auth_service.dart';
import '../screens/social/create_repost_screen.dart';

/// قائمة خيارات إعادة النشر مثل Facebook
class RepostBottomSheet extends StatelessWidget {
  final PostModel post;

  const RepostBottomSheet({
    Key? key,
    required this.post,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  'Repost',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ],
            ),
          ),

          // فاصل
          Divider(color: Colors.grey[200], height: 1),

          // خيارات إعادة النشر
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // إعادة نشر عادية
                _buildRepostOption(
                  context: context,
                  icon: Icons.repeat,
                  title: 'Repost',
                  subtitle: 'Share this post to your timeline',
                  color: const Color(0xFF42B883),
                  onTap: () => _repostNormal(context),
                ),

                const SizedBox(height: 16),

                // إعادة نشر مع تعليق
                _buildRepostOption(
                  context: context,
                  icon: Icons.edit,
                  title: 'Repost with your thoughts',
                  subtitle: 'Add your own comment to this post',
                  color: const Color(0xFF1877F2),
                  onTap: () => _repostWithThoughts(context),
                ),
              ],
            ),
          ),

          // مساحة أسفل
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// بناء خيار إعادة النشر
  Widget _buildRepostOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // أيقونة
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // النص
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1C1E21),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // سهم
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// إعادة نشر عادية
  void _repostNormal(BuildContext context) {
    Navigator.of(context).pop();
    
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      // إنشاء منشور إعادة نشر
      socialService.createRepost(
        originalPost: post,
        reposterUserId: currentUser.phoneNumber,
        reposterName: currentUser.name,
        comment: null, // بدون تعليق
      ).then((success) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('✅ Post reposted successfully!'),
              backgroundColor: const Color(0xFF42B883),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ Failed to repost'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    }
  }

  /// إعادة نشر مع تعليق
  void _repostWithThoughts(BuildContext context) {
    Navigator.of(context).pop();
    
    // فتح شاشة إنشاء إعادة نشر مع تعليق
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateRepostScreen(
          originalPost: post,
        ),
      ),
    );
  }
}

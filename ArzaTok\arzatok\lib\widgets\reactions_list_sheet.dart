import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/social_feed_service.dart';
import '../screens/social/user_profile_screen.dart';
import '../models/user_model.dart';

/// قائمة التفاعلات المحسنة مثل Facebook
class ReactionsListSheet extends StatefulWidget {
  final String postId;
  final int totalReactions;

  const ReactionsListSheet({
    super.key,
    required this.postId,
    required this.totalReactions,
  });

  @override
  State<ReactionsListSheet> createState() => _ReactionsListSheetState();
}

class _ReactionsListSheetState extends State<ReactionsListSheet>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 10, vsync: this); // All + 9 reactions
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.4,
      maxChildSize: 0.95,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  const Text(
                    'Reactions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1C1E21),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${widget.totalReactions}',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // تبويبات التفاعلات مثل Facebook
            _buildReactionTabs(),

            // فاصل
            Divider(color: Colors.grey[200], height: 1),

            // قائمة التفاعلات
            Expanded(
              child: _buildReactionsListContent(scrollController),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويبات التفاعلات
  Widget _buildReactionTabs() {
    return Consumer<SocialFeedService>(
      builder: (context, socialService, child) {
        final reactionDetails = socialService.getPostReactionDetails(widget.postId);
        final reactionCounts = _getReactionCounts(reactionDetails);

        return Container(
          height: 50,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: const Color(0xFF1877F2),
            labelColor: const Color(0xFF1877F2),
            unselectedLabelColor: Colors.grey[600],
            onTap: (index) {
              setState(() {
                _selectedFilter = _getFilterByIndex(index);
              });
            },
            tabs: [
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text('All'),
                    const SizedBox(width: 4),
                    Text('${widget.totalReactions}'),
                  ],
                ),
              ),
              ...reactionCounts.entries.map((entry) => Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildReactionIcon(entry.key),
                    const SizedBox(width: 4),
                    Text('${entry.value}'),
                  ],
                ),
              )),
            ],
          ),
        );
      },
    );
  }

  /// بناء أيقونة التفاعل
  Widget _buildReactionIcon(String emoji) {
    final reactionData = _getReactionData(emoji);
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: reactionData['color'],
        shape: BoxShape.circle,
      ),
      child: Icon(
        reactionData['icon'],
        color: Colors.white,
        size: 12,
      ),
    );
  }

  /// الحصول على عدد كل تفاعل
  Map<String, int> _getReactionCounts(List<Map<String, dynamic>> reactions) {
    final counts = <String, int>{};
    for (final reaction in reactions) {
      final emoji = reaction['emoji'] as String;
      counts[emoji] = (counts[emoji] ?? 0) + 1;
    }
    return counts;
  }

  /// الحصول على الفلتر حسب الفهرس
  String _getFilterByIndex(int index) {
    if (index == 0) return 'all';
    final emojis = ['👍', '❤️', '😂', '😮', '😢', '😡', '🙏', '👏', '🐔'];
    return index <= emojis.length ? emojis[index - 1] : 'all';
  }

  /// بناء محتوى قائمة التفاعلات
  Widget _buildReactionsListContent(ScrollController scrollController) {
    return Consumer<SocialFeedService>(
      builder: (context, socialService, child) {
        final reactionDetails = socialService.getPostReactionDetails(widget.postId);

        // فلترة التفاعلات حسب التبويب المختار
        final filteredReactions = _selectedFilter == 'all'
            ? reactionDetails
            : reactionDetails.where((r) => r['emoji'] == _selectedFilter).toList();

        if (filteredReactions.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.sentiment_neutral,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No reactions yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: filteredReactions.length,
          itemBuilder: (context, index) {
            final reaction = filteredReactions[index];
            return _buildReactionItem(reaction);
          },
        );
      },
    );
  }

  /// بناء عنصر تفاعل واحد
  Widget _buildReactionItem(Map<String, dynamic> reaction) {
    final userName = reaction['userName'] ?? 'Unknown User';
    final userId = reaction['userId'] ?? '';
    final emoji = reaction['emoji'] ?? '👍';
    final timestamp = reaction['timestamp'] as DateTime?;

    return GestureDetector(
      onTap: () => _navigateToUserProfile(userId, userName),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            // صورة المستخدم - قابلة للنقر
            GestureDetector(
              onTap: () => _navigateToUserProfile(userId, userName),
              child: CircleAvatar(
                radius: 20,
                backgroundColor: const Color(0xFF1877F2),
                child: Text(
                  userName.isNotEmpty ? userName[0].toUpperCase() : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // معلومات المستخدم - قابلة للنقر
            Expanded(
              child: GestureDetector(
                onTap: () => _navigateToUserProfile(userId, userName),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1C1E21),
                      ),
                    ),
                    if (timestamp != null)
                      Text(
                        _formatTimestamp(timestamp),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // التفاعل الاحترافي
            _buildProfessionalReactionSticker(emoji),
          ],
        ),
      ),
    );
  }

  /// الانتقال إلى ملف المستخدم الشخصي
  void _navigateToUserProfile(String userId, String userName) {
    Navigator.of(context).pop(); // إغلاق قائمة التفاعلات

    // إنشاء مستخدم تجريبي للعرض
    final demoUser = UserModel(
      phoneNumber: userId,
      name: userName,
      gender: Gender.male,
      age: 25,
      country: 'Morocco',
      city: 'Casablanca',
      registrationDate: DateTime.now().subtract(const Duration(days: 30)),
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(hours: 1)),
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          user: demoUser,
          isCurrentUser: false,
        ),
      ),
    );
  }

  /// بناء ملصق تفاعل احترافي مثل LinkedIn
  Widget _buildProfessionalReactionSticker(String emoji) {
    final reactionData = _getReactionData(emoji);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: reactionData['color'].withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: reactionData['color'].withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: reactionData['color'],
              shape: BoxShape.circle,
            ),
            child: Icon(
              reactionData['icon'],
              color: Colors.white,
              size: 12,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            reactionData['name'],
            style: TextStyle(
              color: reactionData['color'],
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على بيانات التفاعل
  Map<String, dynamic> _getReactionData(String emoji) {
    switch (emoji) {
      case '👍':
        return {
          'name': 'Like',
          'color': const Color(0xFF1877F2),
          'icon': Icons.thumb_up,
        };
      case '❤️':
        return {
          'name': 'Love',
          'color': const Color(0xFFE91E63),
          'icon': Icons.favorite,
        };
      case '😂':
        return {
          'name': 'Haha',
          'color': const Color(0xFFFFC107),
          'icon': Icons.sentiment_very_satisfied,
        };
      case '😮':
        return {
          'name': 'Wow',
          'color': const Color(0xFF9C27B0),
          'icon': Icons.sentiment_very_satisfied,
        };
      case '😢':
        return {
          'name': 'Sad',
          'color': const Color(0xFF607D8B),
          'icon': Icons.sentiment_very_dissatisfied,
        };
      case '😡':
        return {
          'name': 'Angry',
          'color': const Color(0xFFFF5722),
          'icon': Icons.sentiment_very_dissatisfied,
        };
      case '🙏':
        return {
          'name': 'Support',
          'color': const Color(0xFF4CAF50),
          'icon': Icons.volunteer_activism,
        };
      case '👏':
        return {
          'name': 'Clap',
          'color': const Color(0xFFFF9800),
          'icon': Icons.celebration,
        };
      case '🐔':
        return {
          'name': 'Coward',
          'color': const Color(0xFF795548),
          'icon': Icons.pets,
        };
      default:
        return {
          'name': 'Like',
          'color': const Color(0xFF1877F2),
          'icon': Icons.thumb_up,
        };
    }
  }

  /// تنسيق الوقت
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

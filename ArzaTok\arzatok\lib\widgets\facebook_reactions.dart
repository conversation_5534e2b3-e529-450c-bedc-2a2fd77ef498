import 'package:flutter/material.dart';

/// تفاعلات Facebook الحقيقية
class FacebookReactions {
  static const List<Map<String, dynamic>> reactions = [
    {
      'id': 'like',
      'name': 'Like',
      'emoji': '👍',
      'color': 0xFF1877F2,
      'animation': 'bounce',
    },
    {
      'id': 'love',
      'name': 'Love',
      'emoji': '❤️',
      'color': 0xFFE91E63,
      'animation': 'heartbeat',
    },
    {
      'id': 'haha',
      'name': 'Haha',
      'emoji': '😂',
      'color': 0xFFFFC107,
      'animation': 'shake',
    },
    {
      'id': 'wow',
      'name': 'Wow',
      'emoji': '😮',
      'color': 0xFF9C27B0, // بنفسجي للدهشة
      'animation': 'scale',
    },
    {
      'id': 'sad',
      'name': 'Sad',
      'emoji': '😢',
      'color': 0xFF607D8B, // رمادي مزرق للحزن
      'animation': 'drop',
    },
    {
      'id': 'angry',
      'name': 'Angry',
      'emoji': '😡',
      'color': 0xFFFF5722,
      'animation': 'vibrate',
    },
    // التفاعلات الثلاثة الجديدة الاحترافية
    {
      'id': 'support',
      'name': 'Support',
      'emoji': '🙏',
      'color': 0xFF4CAF50, // أخضر للدعم
      'animation': 'pulse',
    },
    {
      'id': 'clap',
      'name': 'Clap',
      'emoji': '👏',
      'color': 0xFFFF9800, // برتقالي للتصفيق
      'animation': 'clap',
    },
    {
      'id': 'coward',
      'name': 'Coward',
      'emoji': '🐔',
      'color': 0xFF795548, // بني للجبان
      'animation': 'shake',
    },
  ];

  static Map<String, dynamic>? getReaction(String id) {
    try {
      return reactions.firstWhere((r) => r['id'] == id);
    } catch (e) {
      return null;
    }
  }

  static Color getReactionColor(String id) {
    final reaction = getReaction(id);
    return Color(reaction?['color'] ?? 0xFF1877F2);
  }

  static String getReactionName(String id) {
    final reaction = getReaction(id);
    return reaction?['name'] ?? 'Like';
  }

  static String getReactionEmoji(String id) {
    final reaction = getReaction(id);
    return reaction?['emoji'] ?? '👍';
  }
}

/// ويدجت تفاعلات Facebook
class FacebookReactionBar extends StatefulWidget {
  final Function(String) onReactionSelected;
  final VoidCallback onClose;

  const FacebookReactionBar({
    super.key,
    required this.onReactionSelected,
    required this.onClose,
  });

  @override
  State<FacebookReactionBar> createState() => _FacebookReactionBarState();
}

class _FacebookReactionBarState extends State<FacebookReactionBar>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      FacebookReactions.reactions.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 200 + (index * 50)),
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isMediumScreen = screenWidth < 400;

    // تقسيم التفاعلات إلى صفوف للشاشات الصغيرة
    final reactions = FacebookReactions.reactions;
    final shouldUseMultipleRows = reactions.length > 6;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 6 : 12,
        vertical: shouldUseMultipleRows ? 8 : 12,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(shouldUseMultipleRows ? 20 : 50),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 4,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 40,
            offset: const Offset(0, 16),
            spreadRadius: 8,
          ),
        ],
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: shouldUseMultipleRows
          ? _buildMultiRowLayout(reactions, isSmallScreen, isMediumScreen)
          : _buildSingleRowLayout(reactions, isSmallScreen),
    );
  }

  /// بناء تخطيط صف واحد للتفاعلات القليلة
  Widget _buildSingleRowLayout(List<Map<String, dynamic>> reactions, bool isSmallScreen) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      mainAxisSize: MainAxisSize.min,
      children: reactions.asMap().entries.map((entry) {
        final index = entry.key;
        final reaction = entry.value;

        return AnimatedBuilder(
          animation: _controllers[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[index].value,
              child: SlideTransition(
                position: _slideAnimations[index],
                child: _buildLinkedInStyleReaction(reaction, index, isSmallScreen),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  /// بناء تخطيط متعدد الصفوف للتفاعلات الكثيرة
  Widget _buildMultiRowLayout(List<Map<String, dynamic>> reactions, bool isSmallScreen, bool isMediumScreen) {
    final itemsPerRow = isSmallScreen ? 3 : (isMediumScreen ? 4 : 5);
    final rows = <Widget>[];

    for (int i = 0; i < reactions.length; i += itemsPerRow) {
      final rowReactions = reactions.sublist(
        i,
        (i + itemsPerRow > reactions.length) ? reactions.length : i + itemsPerRow
      );

      rows.add(
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: rowReactions.asMap().entries.map((entry) {
            final globalIndex = i + entry.key;
            final reaction = entry.value;

            return AnimatedBuilder(
              animation: _controllers[globalIndex],
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimations[globalIndex].value,
                  child: SlideTransition(
                    position: _slideAnimations[globalIndex],
                    child: _buildLinkedInStyleReaction(reaction, globalIndex, isSmallScreen),
                  ),
                );
              },
            );
          }).toList(),
        ),
      );

      if (i + itemsPerRow < reactions.length) {
        rows.add(const SizedBox(height: 8));
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: rows,
    );
  }

  /// بناء تفاعل بستايل LinkedIn احترافي
  Widget _buildLinkedInStyleReaction(Map<String, dynamic> reaction, int index, bool isSmallScreen) {
    final reactionColor = Color(reaction['color']);
    final size = isSmallScreen ? 45.0 : 55.0;
    final iconSize = isSmallScreen ? 24.0 : 28.0;
    final fontSize = isSmallScreen ? 9.0 : 10.0;

    return GestureDetector(
      onTap: () {
        _animateSelection(index);
        widget.onReactionSelected(reaction['id']);
        widget.onClose();
      },
      child: Container(
        width: size,
        height: size + 12,
        margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 2 : 4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة احترافية بدل<|im_start|> من الإيموجي
            Container(
              width: iconSize + 8,
              height: iconSize + 8,
              decoration: BoxDecoration(
                color: reactionColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: reactionColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    reactionColor,
                    reactionColor.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Center(
                child: _getLinkedInStyleIcon(reaction['id'], iconSize),
              ),
            ),

            const SizedBox(height: 4),

            // اسم التفاعل بستايل LinkedIn
            Text(
              reaction['name'],
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.w600,
                color: reactionColor,
                letterSpacing: 0.2,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على أيقونة احترافية بدل<|im_start|> من الإيموجي
  Widget _getLinkedInStyleIcon(String reactionId, double size) {
    IconData iconData;

    switch (reactionId) {
      case 'like':
        iconData = Icons.thumb_up;
        break;
      case 'love':
        iconData = Icons.favorite;
        break;
      case 'haha':
        iconData = Icons.sentiment_very_satisfied;
        break;
      case 'wow':
        iconData = Icons.sentiment_very_satisfied_outlined;
        break;
      case 'sad':
        iconData = Icons.sentiment_very_dissatisfied;
        break;
      case 'angry':
        iconData = Icons.sentiment_very_dissatisfied_outlined;
        break;
      case 'support':
        iconData = Icons.volunteer_activism;
        break;
      case 'clap':
        iconData = Icons.celebration;
        break;
      case 'coward':
        iconData = Icons.warning;
        break;
      default:
        iconData = Icons.thumb_up;
    }

    return Icon(
      iconData,
      color: Colors.white,
      size: size * 0.7,
    );
  }

  void _animateSelection(int index) {
    _controllers[index].reverse().then((_) {
      _controllers[index].forward();
    });
  }
}

/// زر التفاعل مع ستايل Facebook
class FacebookReactionButton extends StatefulWidget {
  final String? currentReaction;
  final Function(String) onReactionChanged;
  final VoidCallback onShowReactions;

  const FacebookReactionButton({
    super.key,
    this.currentReaction,
    required this.onReactionChanged,
    required this.onShowReactions,
  });

  @override
  State<FacebookReactionButton> createState() => _FacebookReactionButtonState();
}

class _FacebookReactionButtonState extends State<FacebookReactionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isReacted = widget.currentReaction != null && widget.currentReaction!.isNotEmpty;
    final reactionColor = isReacted
        ? FacebookReactions.getReactionColor(widget.currentReaction!)
        : Colors.grey[700]!;
    final reactionName = isReacted
        ? FacebookReactions.getReactionName(widget.currentReaction!)
        : 'Like';


    return GestureDetector(
      onTap: () {
        _animatePress();
        if (isReacted) {
          widget.onReactionChanged('');
        } else {
          widget.onReactionChanged('like');
        }
      },
      onLongPress: () {
        _animatePress();
        widget.onShowReactions();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة احترافية بدل<|im_start|> من الإيموجي
                  isReacted
                      ? Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: reactionColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: reactionColor.withValues(alpha: 0.3),
                                blurRadius: 4,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Center(
                            child: _getReactionIcon(widget.currentReaction!, 12),
                          ),
                        )
                      : Icon(
                          Icons.thumb_up_outlined,
                          size: 18,
                          color: reactionColor,
                        ),

                  const SizedBox(width: 6),

                  // النص
                  Text(
                    reactionName,
                    style: TextStyle(
                      color: reactionColor,
                      fontWeight: isReacted ? FontWeight.bold : FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _animatePress() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
  }

  /// الحصول على أيقونة التفاعل
  Widget _getReactionIcon(String reactionId, double size) {
    IconData iconData;

    switch (reactionId) {
      case 'like':
        iconData = Icons.thumb_up;
        break;
      case 'love':
        iconData = Icons.favorite;
        break;
      case 'haha':
        iconData = Icons.sentiment_very_satisfied;
        break;
      case 'wow':
        iconData = Icons.sentiment_very_satisfied_outlined;
        break;
      case 'sad':
        iconData = Icons.sentiment_very_dissatisfied;
        break;
      case 'angry':
        iconData = Icons.sentiment_very_dissatisfied_outlined;
        break;
      case 'support':
        iconData = Icons.volunteer_activism;
        break;
      case 'clap':
        iconData = Icons.celebration;
        break;
      case 'coward':
        iconData = Icons.warning;
        break;
      default:
        iconData = Icons.thumb_up;
    }

    return Icon(
      iconData,
      color: Colors.white,
      size: size,
    );
  }
}

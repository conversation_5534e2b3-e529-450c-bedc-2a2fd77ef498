import 'package:flutter/material.dart';
import '../../services/firebase_test_service.dart';

/// شاشة اختبار Firebase
class FirebaseTestScreen extends StatefulWidget {
  const FirebaseTestScreen({super.key});

  @override
  State<FirebaseTestScreen> createState() => _FirebaseTestScreenState();
}

class _FirebaseTestScreenState extends State<FirebaseTestScreen> {
  bool _isRunningTest = false;
  Map<String, dynamic>? _testResults;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Firebase Test',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFD32F2F),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الاختبار
            _buildTestInfo(),
            
            const SizedBox(height: 24),
            
            // أزرار الاختبار
            _buildTestButtons(),
            
            const SizedBox(height: 24),
            
            // نتائج الاختبار
            if (_testResults != null) _buildTestResults(),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الاختبار
  Widget _buildTestInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[700]),
              const SizedBox(width: 8),
              Text(
                'Firebase Connection Test',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'This test will check:\n'
            '• Firestore connection\n'
            '• Firestore read/write operations\n'
            '• Firebase Storage upload\n'
            '• Real post and reaction saving\n'
            '• Link preview functionality',
            style: TextStyle(fontSize: 14, height: 1.5),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الاختبار
  Widget _buildTestButtons() {
    return Column(
      children: [
        // اختبار شامل
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isRunningTest ? null : _runCompleteTest,
            icon: _isRunningTest 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(_isRunningTest ? 'Running Test...' : 'Run Complete Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD32F2F),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // اختبار سريع
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _isRunningTest ? null : _runQuickTest,
            icon: const Icon(Icons.flash_on),
            label: const Text('Quick Connection Test'),
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFFD32F2F),
              side: const BorderSide(color: Color(0xFFD32F2F)),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // اختبار المنشورات
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _isRunningTest ? null : _testPostSaving,
            icon: const Icon(Icons.post_add),
            label: const Text('Test Post Saving'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.green[700],
              side: BorderSide(color: Colors.green[700]!),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // اختبار التفاعلات
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _isRunningTest ? null : _testReactionSaving,
            icon: const Icon(Icons.thumb_up),
            label: const Text('Test Reaction Saving'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.orange[700],
              side: BorderSide(color: Colors.orange[700]!),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء نتائج الاختبار
  Widget _buildTestResults() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _testResults!['overall_success'] == true 
                      ? Icons.check_circle 
                      : Icons.error,
                  color: _testResults!['overall_success'] == true 
                      ? Colors.green 
                      : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Test Results',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _testResults!['overall_success'] == true 
                        ? Colors.green[700] 
                        : Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView(
                children: _testResults!.entries.map((entry) {
                  if (entry.key == 'overall_success') return const SizedBox.shrink();
                  
                  final result = entry.value as Map<String, dynamic>;
                  final isSuccess = result['success'] == true;
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSuccess 
                          ? Colors.green.withValues(alpha: 0.1) 
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSuccess 
                            ? Colors.green.withValues(alpha: 0.3) 
                            : Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              isSuccess ? Icons.check : Icons.close,
                              color: isSuccess ? Colors.green[700] : Colors.red[700],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                entry.key.replaceAll('_', ' ').toUpperCase(),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: isSuccess ? Colors.green[700] : Colors.red[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          result['message'] ?? 'No message',
                          style: const TextStyle(fontSize: 14),
                        ),
                        if (result['error'] != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            'Error: ${result['error']}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.red[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تشغيل الاختبار الشامل
  Future<void> _runCompleteTest() async {
    setState(() {
      _isRunningTest = true;
      _testResults = null;
    });

    try {
      final results = await FirebaseTestService.runCompleteTest();
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'error': {
            'success': false,
            'message': 'Test failed: $e',
          },
          'overall_success': false,
        };
      });
    } finally {
      setState(() {
        _isRunningTest = false;
      });
    }
  }

  /// تشغيل الاختبار السريع
  Future<void> _runQuickTest() async {
    setState(() {
      _isRunningTest = true;
      _testResults = null;
    });

    try {
      final success = await FirebaseTestService.quickConnectionTest();
      setState(() {
        _testResults = {
          'quick_test': {
            'success': success,
            'message': success 
                ? 'Firebase connection is working!' 
                : 'Firebase connection failed!',
          },
          'overall_success': success,
        };
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'quick_test': {
            'success': false,
            'message': 'Quick test failed: $e',
          },
          'overall_success': false,
        };
      });
    } finally {
      setState(() {
        _isRunningTest = false;
      });
    }
  }

  /// اختبار حفظ المنشورات
  Future<void> _testPostSaving() async {
    setState(() {
      _isRunningTest = true;
      _testResults = null;
    });

    try {
      final result = await FirebaseTestService.testRealPostSave();
      setState(() {
        _testResults = {
          'post_saving': result,
          'overall_success': result['success'],
        };
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'post_saving': {
            'success': false,
            'message': 'Post saving test failed: $e',
          },
          'overall_success': false,
        };
      });
    } finally {
      setState(() {
        _isRunningTest = false;
      });
    }
  }

  /// اختبار حفظ التفاعلات
  Future<void> _testReactionSaving() async {
    setState(() {
      _isRunningTest = true;
      _testResults = null;
    });

    try {
      final result = await FirebaseTestService.testRealReactionSave();
      setState(() {
        _testResults = {
          'reaction_saving': result,
          'overall_success': result['success'],
        };
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'reaction_saving': {
            'success': false,
            'message': 'Reaction saving test failed: $e',
          },
          'overall_success': false,
        };
      });
    } finally {
      setState(() {
        _isRunningTest = false;
      });
    }
  }
}

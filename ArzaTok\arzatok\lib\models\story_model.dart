import 'package:flutter/material.dart';

/// نوع محتوى القصة
enum StoryType {
  text,
  image,
  video,
}

/// نموذج القصة
class StoryModel {
  final String id;
  final String userId;
  final String userName;
  final String? userProfileImage;
  final StoryType type;
  final String? content; // للنص
  final String? mediaUrl; // للصور والفيديو
  final String? thumbnailUrl; // للفيديو
  final Color? backgroundColor; // لون خلفية النص
  final Color? textColor; // لون النص
  final String? fontFamily; // خط النص
  final DateTime createdAt;
  final DateTime expiresAt;
  final List<String> viewedBy; // قائمة المشاهدين
  final bool isViewed; // هل تم مشاهدتها من قبل المستخدم الحالي

  const StoryModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.userProfileImage,
    required this.type,
    this.content,
    this.mediaUrl,
    this.thumbnailUrl,
    this.backgroundColor,
    this.textColor,
    this.fontFamily,
    required this.createdAt,
    required this.expiresAt,
    this.viewedBy = const [],
    this.isViewed = false,
  });

  /// إنشاء قصة نصية
  factory StoryModel.text({
    required String id,
    required String userId,
    required String userName,
    String? userProfileImage,
    required String content,
    Color? backgroundColor,
    Color? textColor,
    String? fontFamily,
  }) {
    final now = DateTime.now();
    return StoryModel(
      id: id,
      userId: userId,
      userName: userName,
      userProfileImage: userProfileImage,
      type: StoryType.text,
      content: content,
      backgroundColor: backgroundColor ?? const Color(0xFFD32F2F),
      textColor: textColor ?? Colors.white,
      fontFamily: fontFamily ?? 'Roboto',
      createdAt: now,
      expiresAt: now.add(const Duration(hours: 24)),
    );
  }

  /// إنشاء قصة صورة
  factory StoryModel.image({
    required String id,
    required String userId,
    required String userName,
    String? userProfileImage,
    required String imageUrl,
  }) {
    final now = DateTime.now();
    return StoryModel(
      id: id,
      userId: userId,
      userName: userName,
      userProfileImage: userProfileImage,
      type: StoryType.image,
      mediaUrl: imageUrl,
      createdAt: now,
      expiresAt: now.add(const Duration(hours: 24)),
    );
  }

  /// إنشاء قصة فيديو
  factory StoryModel.video({
    required String id,
    required String userId,
    required String userName,
    String? userProfileImage,
    required String videoUrl,
    String? thumbnailUrl,
  }) {
    final now = DateTime.now();
    return StoryModel(
      id: id,
      userId: userId,
      userName: userName,
      userProfileImage: userProfileImage,
      type: StoryType.video,
      mediaUrl: videoUrl,
      thumbnailUrl: thumbnailUrl,
      createdAt: now,
      expiresAt: now.add(const Duration(hours: 24)),
    );
  }

  /// نسخ القصة مع تعديلات
  StoryModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userProfileImage,
    StoryType? type,
    String? content,
    String? mediaUrl,
    String? thumbnailUrl,
    Color? backgroundColor,
    Color? textColor,
    String? fontFamily,
    DateTime? createdAt,
    DateTime? expiresAt,
    List<String>? viewedBy,
    bool? isViewed,
  }) {
    return StoryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      type: type ?? this.type,
      content: content ?? this.content,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      fontFamily: fontFamily ?? this.fontFamily,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      viewedBy: viewedBy ?? this.viewedBy,
      isViewed: isViewed ?? this.isViewed,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userProfileImage': userProfileImage,
      'type': type.name,
      'content': content,
      'mediaUrl': mediaUrl,
      'thumbnailUrl': thumbnailUrl,
      'backgroundColor': backgroundColor?.value,
      'textColor': textColor?.value,
      'fontFamily': fontFamily,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'viewedBy': viewedBy,
      'isViewed': isViewed,
    };
  }

  /// إنشاء من Map
  factory StoryModel.fromMap(Map<String, dynamic> map) {
    return StoryModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userProfileImage: map['userProfileImage'],
      type: StoryType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => StoryType.text,
      ),
      content: map['content'],
      mediaUrl: map['mediaUrl'],
      thumbnailUrl: map['thumbnailUrl'],
      backgroundColor: map['backgroundColor'] != null 
          ? Color(map['backgroundColor']) 
          : null,
      textColor: map['textColor'] != null 
          ? Color(map['textColor']) 
          : null,
      fontFamily: map['fontFamily'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      expiresAt: DateTime.fromMillisecondsSinceEpoch(map['expiresAt'] ?? 0),
      viewedBy: List<String>.from(map['viewedBy'] ?? []),
      isViewed: map['isViewed'] ?? false,
    );
  }

  /// هل انتهت صلاحية القصة؟
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// الوقت المتبقي للانتهاء
  Duration get timeRemaining {
    final now = DateTime.now();
    if (now.isAfter(expiresAt)) {
      return Duration.zero;
    }
    return expiresAt.difference(now);
  }

  /// نص الوقت المتبقي
  String get timeRemainingText {
    final remaining = timeRemaining;
    if (remaining.inHours > 0) {
      return '${remaining.inHours}h';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes}m';
    } else {
      return '${remaining.inSeconds}s';
    }
  }

  /// عدد المشاهدين
  int get viewCount => viewedBy.length;

  /// هل القصة للمستخدم الحالي؟
  bool isOwnStory(String currentUserId) => userId == currentUserId;

  @override
  String toString() {
    return 'StoryModel(id: $id, userId: $userId, type: $type, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// مجموعة قصص المستخدم
class UserStoriesModel {
  final String userId;
  final String userName;
  final String? userProfileImage;
  final List<StoryModel> stories;
  final bool hasUnviewedStories;

  const UserStoriesModel({
    required this.userId,
    required this.userName,
    this.userProfileImage,
    required this.stories,
    this.hasUnviewedStories = false,
  });

  /// آخر قصة
  StoryModel? get latestStory {
    if (stories.isEmpty) return null;
    return stories.reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);
  }

  /// عدد القصص غير المشاهدة
  int get unviewedCount {
    return stories.where((story) => !story.isViewed).length;
  }

  /// هل توجد قصص صالحة؟
  bool get hasValidStories {
    return stories.any((story) => !story.isExpired);
  }

  /// نسخ مع تعديلات
  UserStoriesModel copyWith({
    String? userId,
    String? userName,
    String? userProfileImage,
    List<StoryModel>? stories,
    bool? hasUnviewedStories,
  }) {
    return UserStoriesModel(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      stories: stories ?? this.stories,
      hasUnviewedStories: hasUnviewedStories ?? this.hasUnviewedStories,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/group_model.dart';
import '../../models/contact_model.dart';
import '../../services/auth_service.dart';
import '../../services/group_service.dart';
import '../../services/contact_service.dart';
import '../../services/storage_service.dart';
import '../contacts/contact_profile_screen.dart';
import 'edit_group_screen.dart';
import 'dart:io';

class GroupInfoScreen extends StatefulWidget {
  final GroupModel group;

  const GroupInfoScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupInfoScreen> createState() => _GroupInfoScreenState();
}

class _GroupInfoScreenState extends State<GroupInfoScreen> {
  final StorageService _storageService = StorageService();
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Consumer3<AuthService, GroupService, ContactService>(
      builder: (context, authService, groupService, contactService, child) {
        final currentUser = authService.currentUser;
        if (currentUser == null) return const Scaffold();

        final group = groupService.getGroupById(widget.group.id) ?? widget.group;
        final isAdmin = group.isAdmin(currentUser.phoneNumber);
        final isCreator = group.createdBy == currentUser.phoneNumber;

        return Scaffold(
          appBar: AppBar(
            title: const Text('Group Info'),
            actions: [
              if (isAdmin)
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => EditGroupScreen(group: group),
                      ),
                    );
                  },
                ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'mute':
                      _toggleMute(groupService);
                      break;
                    case 'clear':
                      _showClearMessagesDialog(groupService);
                      break;
                    case 'leave':
                      _showLeaveGroupDialog(groupService, currentUser.phoneNumber);
                      break;
                    case 'delete':
                      if (isCreator) {
                        _showDeleteGroupDialog(groupService);
                      }
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'mute',
                    child: Row(
                      children: [
                        Icon(group.isMuted ? Icons.volume_up : Icons.volume_off),
                        const SizedBox(width: 8),
                        Text(group.isMuted ? 'Unmute' : 'Mute'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear',
                    child: Row(
                      children: [
                        Icon(Icons.clear),
                        SizedBox(width: 8),
                        Text('Clear Messages'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'leave',
                    child: Row(
                      children: [
                        Icon(Icons.exit_to_app, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Leave Group', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                  if (isCreator)
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete Group', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                // Group header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: isAdmin ? () => _changeGroupImage(groupService) : null,
                        child: Stack(
                          children: [
                            CircleAvatar(
                              radius: 60,
                              backgroundColor: const Color(0xFFD32F2F),
                              backgroundImage: group.groupImageUrl != null
                                  ? NetworkImage(group.groupImageUrl!)
                                  : null,
                              child: group.groupImageUrl == null
                                  ? Text(
                                      group.name.isNotEmpty
                                          ? group.name[0].toUpperCase()
                                          : 'G',
                                      style: const TextStyle(
                                        fontSize: 40,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )
                                  : null,
                            ),
                            if (isAdmin)
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: const BoxDecoration(
                                    color: Color(0xFFD32F2F),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        group.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (group.description != null) ...[
                        Text(
                          group.description!,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                      ],
                      Text(
                        'Created ${_formatDate(group.createdAt)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),

                const Divider(),



                const Divider(),

                // Members section
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.people, color: Color(0xFFD32F2F)),
                      const SizedBox(width: 8),
                      Text(
                        '${group.members.length} Members',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (isAdmin)
                        IconButton(
                          icon: const Icon(Icons.person_add),
                          onPressed: () => _showAddMemberDialog(groupService, contactService),
                        ),
                    ],
                  ),
                ),

                // Members list
                ...group.members.map((memberPhone) {
                  final contact = contactService.getContactByPhoneNumber(memberPhone);
                  final isCurrentUser = memberPhone == currentUser.phoneNumber;
                  final isMemberAdmin = group.isAdmin(memberPhone);
                  final isMemberCreator = group.createdBy == memberPhone;

                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: const Color(0xFFD32F2F),
                      backgroundImage: contact?.profileImageUrl != null
                          ? NetworkImage(contact!.profileImageUrl!)
                          : null,
                      child: contact?.profileImageUrl == null
                          ? Text(
                              contact?.name.isNotEmpty == true
                                  ? contact!.name[0].toUpperCase()
                                  : memberPhone[0].toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    title: Row(
                      children: [
                        Expanded(
                          child: Text(
                            isCurrentUser
                                ? 'You'
                                : contact?.name ?? memberPhone,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        if (isMemberCreator)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'Creator',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                        else if (isMemberAdmin)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: const Color(0xFFD32F2F),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'Admin',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    subtitle: Text(memberPhone),
                    trailing: isAdmin && !isCurrentUser
                        ? PopupMenuButton<String>(
                            onSelected: (value) {
                              switch (value) {
                                case 'make_admin':
                                  groupService.makeAdmin(group.id, memberPhone);
                                  break;
                                case 'remove_admin':
                                  groupService.removeAdmin(group.id, memberPhone);
                                  break;
                                case 'remove_member':
                                  _showRemoveMemberDialog(groupService, memberPhone, contact?.name ?? memberPhone);
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              if (!isMemberAdmin && !isMemberCreator)
                                const PopupMenuItem(
                                  value: 'make_admin',
                                  child: Text('Make Admin'),
                                ),
                              if (isMemberAdmin && !isMemberCreator)
                                const PopupMenuItem(
                                  value: 'remove_admin',
                                  child: Text('Remove Admin'),
                                ),
                              if (!isMemberCreator)
                                const PopupMenuItem(
                                  value: 'remove_member',
                                  child: Text('Remove from Group', style: TextStyle(color: Colors.red)),
                                ),
                            ],
                          )
                        : null,
                    onTap: !isCurrentUser && contact != null
                        ? () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => ContactProfileScreen(contact: contact),
                              ),
                            );
                          }
                        : null,
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? const Color(0xFFD32F2F),
      ),
      title: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  Future<void> _changeGroupImage(GroupService groupService) async {
    final imageFile = await _storageService.showImagePickerOptions(context);
    if (imageFile != null) {
      setState(() {
        _isUpdating = true;
      });

      try {
        final imageUrl = await _storageService.uploadImage(imageFile, 'group_images');
        if (imageUrl != null) {
          await groupService.updateGroupInfo(
            groupId: widget.group.id,
            groupImageUrl: imageUrl,
          );
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Group image updated')),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating image: $e')),
        );
      } finally {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }



  void _toggleMute(GroupService groupService) {
    groupService.toggleGroupMute(widget.group.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.group.isMuted ? 'Group unmuted' : 'Group muted'),
      ),
    );
  }

  void _showClearMessagesDialog(GroupService groupService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Messages'),
        content: const Text('Are you sure you want to clear all messages in this group?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              groupService.clearGroupMessages(widget.group.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Messages cleared')),
              );
            },
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showLeaveGroupDialog(GroupService groupService, String userPhone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Group'),
        content: Text('Are you sure you want to leave "${widget.group.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to groups list
              groupService.leaveGroup(widget.group.id, userPhone);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Left group')),
              );
            },
            child: const Text('Leave', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showDeleteGroupDialog(GroupService groupService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Group'),
        content: Text('Are you sure you want to delete "${widget.group.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to groups list
              groupService.deleteGroup(widget.group.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Group deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showAddMemberDialog(GroupService groupService, ContactService contactService) {
    final availableContacts = contactService.getContactsOnArzaTok()
        .where((contact) => !widget.group.members.contains(contact.phoneNumber))
        .toList();

    if (availableContacts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No available contacts to add')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Members'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: availableContacts.length,
            itemBuilder: (context, index) {
              final contact = availableContacts[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: const Color(0xFFD32F2F),
                  child: Text(
                    contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(contact.name),
                subtitle: Text(contact.phoneNumber),
                onTap: () {
                  Navigator.pop(context);
                  groupService.addMemberToGroup(widget.group.id, contact.phoneNumber);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('${contact.name} added to group')),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showRemoveMemberDialog(GroupService groupService, String memberPhone, String memberName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Member'),
        content: Text('Are you sure you want to remove $memberName from the group?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              groupService.removeMemberFromGroup(widget.group.id, memberPhone);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('$memberName removed from group')),
              );
            },
            child: const Text('Remove', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

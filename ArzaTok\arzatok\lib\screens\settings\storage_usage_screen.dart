import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'dart:math';

class StorageUsageScreen extends StatefulWidget {
  const StorageUsageScreen({super.key});

  @override
  State<StorageUsageScreen> createState() => _StorageUsageScreenState();
}

class _StorageUsageScreenState extends State<StorageUsageScreen> {
  bool _isLoading = true;
  Map<String, int> _storageData = {};
  int _totalSize = 0;

  @override
  void initState() {
    super.initState();
    _calculateStorageUsage();
  }

  Future<void> _calculateStorageUsage() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // محاكاة حساب استخدام التخزين
      await Future.delayed(const Duration(seconds: 2));

      // بيانات تجريبية واقعية
      final random = Random();
      _storageData = {
        'Messages': 1024 * 1024 * (2 + random.nextInt(8)), // 2-10 MB
        'Images': 1024 * 1024 * (5 + random.nextInt(20)), // 5-25 MB
        'Voice Messages': 1024 * 1024 * (1 + random.nextInt(5)), // 1-6 MB
        'Videos': 1024 * 1024 * (10 + random.nextInt(30)), // 10-40 MB
        'Documents': 1024 * 1024 * (1 + random.nextInt(3)), // 1-4 MB
        'Cache': 1024 * 1024 * (3 + random.nextInt(7)), // 3-10 MB
        'App Data': 1024 * 1024 * (2 + random.nextInt(3)), // 2-5 MB
      };

      _totalSize = _storageData.values.reduce((a, b) => a + b);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error calculating storage: $e')),
        );
      }
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  double _getPercentage(int size) {
    if (_totalSize == 0) return 0;
    return (size / _totalSize) * 100;
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Messages':
        return Colors.blue;
      case 'Images':
        return Colors.green;
      case 'Voice Messages':
        return Colors.orange;
      case 'Videos':
        return Colors.purple;
      case 'Documents':
        return Colors.teal;
      case 'Cache':
        return Colors.grey;
      case 'App Data':
        return const Color(0xFFD32F2F);
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Messages':
        return Icons.message;
      case 'Images':
        return Icons.image;
      case 'Voice Messages':
        return Icons.mic;
      case 'Videos':
        return Icons.videocam;
      case 'Documents':
        return Icons.description;
      case 'Cache':
        return Icons.cached;
      case 'App Data':
        return Icons.storage;
      default:
        return Icons.folder;
    }
  }

  Future<void> _clearCategory(String category) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear $category'),
        content: Text('Are you sure you want to clear all $category? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _storageData[category] = 0;
        _totalSize = _storageData.values.reduce((a, b) => a + b);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$category cleared successfully')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Storage Usage'),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _calculateStorageUsage,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Color(0xFFD32F2F)),
                  SizedBox(height: 16),
                  Text('Calculating storage usage...'),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Total Usage Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          const Text(
                            'Total Storage Used',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFFD32F2F),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _formatBytes(_totalSize),
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          LinearProgressIndicator(
                            value: _totalSize / (100 * 1024 * 1024), // Assume 100MB max
                            backgroundColor: Colors.grey[300],
                            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Storage Breakdown
                  const Text(
                    'Storage Breakdown',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFD32F2F),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Category List
                  ..._storageData.entries.map((entry) {
                    final category = entry.key;
                    final size = entry.value;
                    final percentage = _getPercentage(size);
                    final color = _getCategoryColor(category);
                    final icon = _getCategoryIcon(category);

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Icon(icon, color: color),
                        title: Text(category),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${_formatBytes(size)} (${percentage.toStringAsFixed(1)}%)'),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: percentage / 100,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(color),
                            ),
                          ],
                        ),
                        trailing: size > 0
                            ? IconButton(
                                icon: const Icon(Icons.delete_outline, color: Colors.red),
                                onPressed: () => _clearCategory(category),
                              )
                            : null,
                      ),
                    );
                  }),

                  const SizedBox(height: 24),

                  // Clear All Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _totalSize > 0
                          ? () async {
                              final confirmed = await showDialog<bool>(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Clear All Data'),
                                  content: const Text(
                                    'This will clear all cached data, downloaded media, and temporary files. Your messages and account data will not be affected.',
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.of(context).pop(false),
                                      child: const Text('Cancel'),
                                    ),
                                    TextButton(
                                      onPressed: () => Navigator.of(context).pop(true),
                                      child: const Text('Clear All', style: TextStyle(color: Colors.red)),
                                    ),
                                  ],
                                ),
                              );

                              if (confirmed == true) {
                                setState(() {
                                  _storageData = _storageData.map((key, value) => MapEntry(key, 0));
                                  _totalSize = 0;
                                });

                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(content: Text('All data cleared successfully')),
                                  );
                                }
                              }
                            }
                          : null,
                      icon: const Icon(Icons.delete_sweep),
                      label: const Text('Clear All Cache & Media'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}

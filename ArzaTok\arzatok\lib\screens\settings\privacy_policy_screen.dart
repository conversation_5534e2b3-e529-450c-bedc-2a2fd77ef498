import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

/// شاشة الخصوصية والشروط
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Privacy & Terms',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF607D8B),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // معلومات التطبيق
            _buildAppInfoCard(),

            const SizedBox(height: 16),

            // قسم الخصوصية
            _buildSectionCard(
              title: 'Privacy & Security',
              icon: Icons.privacy_tip,
              children: [
                _buildListTile(
                  title: 'Privacy Policy',
                  subtitle: 'How we handle your data',
                  icon: Icons.policy,
                  onTap: () => _openPrivacyPolicy(),
                ),
                _buildListTile(
                  title: 'Data Usage',
                  subtitle: 'What data we collect and why',
                  icon: Icons.data_usage,
                  onTap: () => _showDataUsage(context),
                ),
                _buildListTile(
                  title: 'Security Settings',
                  subtitle: 'Manage your account security',
                  icon: Icons.security,
                  onTap: () => _showSecuritySettings(context),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // قسم الشروط والأحكام
            _buildSectionCard(
              title: 'Terms & Conditions',
              icon: Icons.description,
              children: [
                _buildListTile(
                  title: 'Terms of Service',
                  subtitle: 'Rules and guidelines for using ArzaTok',
                  icon: Icons.gavel,
                  onTap: () => _openTermsOfService(),
                ),
                _buildListTile(
                  title: 'Community Guidelines',
                  subtitle: 'How to behave in our community',
                  icon: Icons.people,
                  onTap: () => _showCommunityGuidelines(context),
                ),
                _buildListTile(
                  title: 'Acceptable Use Policy',
                  subtitle: 'What you can and cannot do',
                  icon: Icons.rule,
                  onTap: () => _showAcceptableUse(context),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // قسم الدعم والتواصل
            _buildSectionCard(
              title: 'Support & Contact',
              icon: Icons.support_agent,
              children: [
                _buildListTile(
                  title: 'Contact Us',
                  subtitle: '<EMAIL>',
                  icon: Icons.email,
                  onTap: () => _contactSupport('<EMAIL>'),
                ),
                _buildListTile(
                  title: 'Business Inquiries',
                  subtitle: '<EMAIL>',
                  icon: Icons.business,
                  onTap: () => _contactSupport('<EMAIL>'),
                ),
                _buildListTile(
                  title: 'Phone Support',
                  subtitle: '+212638813823',
                  icon: Icons.phone,
                  onTap: () => _callSupport('+212638813823'),
                ),
                _buildListTile(
                  title: 'WhatsApp Support',
                  subtitle: '+212771878802',
                  icon: Icons.chat,
                  onTap: () => _whatsappSupport('+212771878802'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // قسم حول التطبيق
            _buildSectionCard(
              title: 'About ArzaTok',
              icon: Icons.info,
              children: [
                _buildListTile(
                  title: 'About Us',
                  subtitle: 'Learn more about ArzaTok',
                  icon: Icons.info_outline,
                  onTap: () => _showAboutUs(context),
                ),
                _buildListTile(
                  title: 'Version',
                  subtitle: '1.0.0 (Build 1)',
                  icon: Icons.system_update,
                  onTap: () => _checkForUpdates(context),
                ),
                _buildListTile(
                  title: 'Licenses',
                  subtitle: 'Open source licenses',
                  icon: Icons.copyright,
                  onTap: () => _showLicenses(context),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات التطبيق
  Widget _buildAppInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF607D8B),
            const Color(0xFF607D8B).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.chat,
              size: 40,
              color: Color(0xFF607D8B),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'ArzaTok',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'تطبيق دردشة وشبكة اجتماعية متقدم\nمع التركيز على المحتوى العربي',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Powered by ArzaPress',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF607D8B).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF607D8B)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF607D8B),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// بناء عنصر قائمة
  Widget _buildListTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF607D8B).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: const Color(0xFF607D8B),
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ),
      onTap: onTap,
    );
  }

  /// فتح سياسة الخصوصية
  void _openPrivacyPolicy() async {
    const url = 'https://arzapress.com/privacy-policy';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  /// فتح شروط الخدمة
  void _openTermsOfService() async {
    const url = 'https://arzapress.com/terms-of-service';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  /// التواصل مع الدعم عبر البريد الإلكتروني
  void _contactSupport(String email) async {
    final url = 'mailto:$email?subject=ArzaTalk Support';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  /// الاتصال بالدعم
  void _callSupport(String phone) async {
    final url = 'tel:$phone';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  /// التواصل عبر WhatsApp
  void _whatsappSupport(String phone) async {
    final url = 'https://wa.me/$phone';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  /// عرض معلومات استخدام البيانات
  void _showDataUsage(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data Usage'),
        content: const SingleChildScrollView(
          child: Text(
            'ArzaTalk collects and uses the following data:\n\n'
            '• Profile information (name, phone number)\n'
            '• Messages and media you send\n'
            '• Usage analytics to improve the app\n'
            '• Device information for security\n\n'
            'We never sell your personal data to third parties.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات الأمان
  void _showSecuritySettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Settings'),
        content: const Text(
          'Your account is protected with:\n\n'
          '• End-to-end encryption for messages\n'
          '• Secure authentication\n'
          '• Regular security updates\n'
          '• Privacy controls\n\n'
          'For additional security options, visit your account settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// عرض إرشادات المجتمع
  void _showCommunityGuidelines(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Community Guidelines'),
        content: const SingleChildScrollView(
          child: Text(
            'To maintain a safe and respectful community:\n\n'
            '• Be respectful to all users\n'
            '• No harassment or bullying\n'
            '• No spam or inappropriate content\n'
            '• Respect privacy and consent\n'
            '• Report violations when you see them\n\n'
            'Violations may result in account suspension.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// عرض سياسة الاستخدام المقبول
  void _showAcceptableUse(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Acceptable Use Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'You may use ArzaTok for:\n\n'
            '• Personal communication\n'
            '• Sharing appropriate content\n'
            '• Connecting with friends and family\n\n'
            'You may NOT use ArzaTok for:\n\n'
            '• Illegal activities\n'
            '• Spreading misinformation\n'
            '• Commercial spam\n'
            '• Impersonating others\n'
            '• Violating others\' rights',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// عرض معلومات حول التطبيق
  void _showAboutUs(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About ArzaTalk'),
        content: const SingleChildScrollView(
          child: Text(
            'ArzaTalk هو تطبيق دردشة وشبكة اجتماعية متقدم يركز على المحتوى العربي.\n\n'
            'تم تطوير التطبيق بواسطة ArzaPress لتوفير منصة آمنة وسهلة الاستخدام للتواصل والمشاركة.\n\n'
            'الميزات الرئيسية:\n'
            '• دردشة آمنة ومشفرة\n'
            '• شبكة اجتماعية متكاملة\n'
            '• قصص تفاعلية\n'
            '• مجموعات وقنوات\n'
            '• دعم كامل للغة العربية\n\n'
            'نحن ملتزمون بتوفير أفضل تجربة تواصل لمستخدمينا.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// التحقق من التحديثات
  void _checkForUpdates(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Check for Updates'),
        content: const Text('You are using the latest version of ArzaTalk.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// عرض التراخيص
  void _showLicenses(BuildContext context) {
    showLicensePage(
      context: context,
      applicationName: 'ArzaTalk',
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 ArzaPress. All rights reserved.',
    );
  }
}

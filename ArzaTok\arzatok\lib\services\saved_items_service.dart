import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/post_model.dart';

/// خدمة العناصر المحفوظة
class SavedItemsService extends ChangeNotifier {
  final List<PostModel> _savedPosts = [];
  static const String _savedPostsKey = 'saved_posts';

  /// الحصول على جميع المنشورات المحفوظة
  List<PostModel> get savedPosts => List.unmodifiable(_savedPosts);

  /// التحقق من كون المنشور محفوظ
  bool isPostSaved(String postId) {
    return _savedPosts.any((post) => post.id == postId);
  }

  /// حفظ منشور
  Future<void> savePost(PostModel post) async {
    if (!isPostSaved(post.id)) {
      _savedPosts.insert(0, post); // إضافة في المقدمة
      await _saveToPersistence();
      notifyListeners();
    }
  }

  /// إلغاء حفظ منشور
  Future<void> unsavePost(String postId) async {
    _savedPosts.removeWhere((post) => post.id == postId);
    await _saveToPersistence();
    notifyListeners();
  }

  /// تبديل حالة الحفظ
  Future<void> toggleSavePost(PostModel post) async {
    if (isPostSaved(post.id)) {
      await unsavePost(post.id);
    } else {
      await savePost(post);
    }
  }

  /// مسح جميع المنشورات المحفوظة
  Future<void> clearAllSavedPosts() async {
    _savedPosts.clear();
    await _saveToPersistence();
    notifyListeners();
  }

  /// الحصول على عدد المنشورات المحفوظة
  int get savedPostsCount => _savedPosts.length;

  /// الحصول على المنشورات المحفوظة حسب النوع
  List<PostModel> getSavedPostsByType(String type) {
    return _savedPosts.where((post) {
      switch (type) {
        case 'text':
          return post.content.isNotEmpty &&
                 post.images.isEmpty &&
                 post.videos.isEmpty;
        case 'image':
          return post.images.isNotEmpty;
        case 'video':
          return post.videos.isNotEmpty;
        case 'link':
          return post.linkPreviews.isNotEmpty;
        default:
          return true;
      }
    }).toList();
  }

  /// حفظ البيانات في التخزين المحلي
  Future<void> _saveToPersistence() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsJson = _savedPosts.map((post) => post.toMap()).toList();
      await prefs.setString(_savedPostsKey, jsonEncode(postsJson));
    } catch (e) {
      debugPrint('Error saving posts to persistence: $e');
    }
  }

  /// تحميل البيانات من التخزين المحلي
  Future<void> loadSavedPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsJsonString = prefs.getString(_savedPostsKey);

      if (postsJsonString != null) {
        final List<dynamic> postsJson = jsonDecode(postsJsonString);
        _savedPosts.clear();
        _savedPosts.addAll(
          postsJson.map((json) => PostModel.fromMap(json)).toList(),
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading saved posts: $e');
    }
  }

  /// إضافة منشورات تجريبية محفوظة
  void addDemoSavedPosts() {
    final demoPosts = [
      PostModel(
        id: 'saved_demo_1',
        authorId: 'demo_user_1',
        authorName: 'أحمد محمد',
        authorAvatar: '',
        content: 'منشور تجريبي محفوظ - نصائح مفيدة للبرمجة',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        reactions: {'👍': 15},
        commentsCount: 5,
        sharesCount: 3,
        images: [],
      ),
      PostModel(
        id: 'saved_demo_2',
        authorId: 'demo_user_2',
        authorName: 'فاطمة علي',
        authorAvatar: '',
        content: 'فيديو تعليمي رائع عن Flutter',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        reactions: {'👍': 28, '❤️': 5},
        commentsCount: 12,
        sharesCount: 7,
        images: [],
        videos: ['demo_video_url'],
      ),
      PostModel(
        id: 'saved_demo_3',
        authorId: 'demo_user_3',
        authorName: 'محمد حسن',
        authorAvatar: '',
        content: 'صور جميلة من الطبيعة',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        reactions: {'👍': 42, '❤️': 8},
        commentsCount: 18,
        sharesCount: 12,
        images: ['demo_image_1.jpg', 'demo_image_2.jpg'],
      ),
    ];

    for (final post in demoPosts) {
      if (!isPostSaved(post.id)) {
        _savedPosts.add(post);
      }
    }
    notifyListeners();
  }

  /// إحصائيات العناصر المحفوظة
  Map<String, int> get savedItemsStats {
    return {
      'total': _savedPosts.length,
      'text': getSavedPostsByType('text').length,
      'image': getSavedPostsByType('image').length,
      'video': getSavedPostsByType('video').length,
      'link': getSavedPostsByType('link').length,
    };
  }

  /// البحث في المنشورات المحفوظة
  List<PostModel> searchSavedPosts(String query) {
    if (query.isEmpty) return _savedPosts;

    final lowerQuery = query.toLowerCase();
    return _savedPosts.where((post) {
      return post.content.toLowerCase().contains(lowerQuery) ||
             post.authorName.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// ترتيب المنشورات المحفوظة
  void sortSavedPosts(String sortBy) {
    switch (sortBy) {
      case 'newest':
        _savedPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'oldest':
        _savedPosts.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'most_liked':
        _savedPosts.sort((a, b) => b.totalReactions.compareTo(a.totalReactions));
        break;
      case 'author':
        _savedPosts.sort((a, b) => a.authorName.compareTo(b.authorName));
        break;
    }
    notifyListeners();
  }
}

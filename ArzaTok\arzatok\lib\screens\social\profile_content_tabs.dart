import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/post_model.dart';
import '../../models/privacy_settings_model.dart';
import '../../models/user_model.dart';
import '../../services/content_categorization_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/post_widget.dart';

/// شاشة تصنيفات المحتوى في الملف الشخصي
class ProfileContentTabs extends StatefulWidget {
  final UserModel user;
  final bool isCurrentUser;

  const ProfileContentTabs({
    Key? key,
    required this.user,
    required this.isCurrentUser,
  }) : super(key: key);

  @override
  State<ProfileContentTabs> createState() => _ProfileContentTabsState();
}

class _ProfileContentTabsState extends State<ProfileContentTabs>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadContent();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل المحتوى
  Future<void> _loadContent() async {
    final contentService = Provider.of<ContentCategorizationService>(context, listen: false);
    await contentService.loadContentFromAllSources(widget.user.phoneNumber);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF1C1E21)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.isCurrentUser ? 'My Content' : '${widget.user.name}\'s Content',
          style: const TextStyle(
            color: Color(0xFF1C1E21),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Column(
            children: [
              // شريط البحث
              Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF0F2F5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search content...',
                    prefixIcon: Icon(Icons.search, color: Color(0xFF65676B)),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),

              // تبويبات التصنيف
              _buildContentTabs(),
            ],
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildPhotosTab(),
                _buildVideosTab(),
                _buildLinksTab(),
                _buildTextPostsTab(),
                _buildSharedPostsTab(),
                _buildLiveStreamsTab(),
              ],
            ),
    );
  }

  /// بناء تبويبات التصنيف
  Widget _buildContentTabs() {
    return Consumer2<ContentCategorizationService, AuthService>(
      builder: (context, contentService, authService, child) {
        final currentUserId = authService.currentUser?.phoneNumber ?? '';
        final privacySettings = widget.user.privacySettings;
        final counts = contentService.getContentCounts(
          currentUserId,
          widget.user.phoneNumber,
          privacySettings,
        );

        return TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: const Color(0xFFD32F2F),
          labelColor: const Color(0xFFD32F2F),
          unselectedLabelColor: const Color(0xFF65676B),
          tabs: [
            _buildTab(Icons.photo, 'Photos', counts[ContentType.photos] ?? 0),
            _buildTab(Icons.videocam, 'Videos', counts[ContentType.videos] ?? 0),
            _buildTab(Icons.link, 'Links', counts[ContentType.links] ?? 0),
            _buildTab(Icons.text_fields, 'Text', counts[ContentType.textPosts] ?? 0),
            _buildTab(Icons.share, 'Shared', counts[ContentType.sharedPosts] ?? 0),
            _buildTab(Icons.live_tv, 'Live', counts[ContentType.liveStreams] ?? 0),
          ],
        );
      },
    );
  }

  /// بناء تبويب واحد
  Widget _buildTab(IconData icon, String label, int count) {
    return Tab(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          if (count > 0)
            Container(
              margin: const EdgeInsets.only(top: 2),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
              decoration: BoxDecoration(
                color: const Color(0xFFD32F2F),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء تبويب الصور
  Widget _buildPhotosTab() {
    return _buildContentGrid(ContentType.photos);
  }

  /// بناء تبويب الفيديوهات
  Widget _buildVideosTab() {
    return _buildContentGrid(ContentType.videos);
  }

  /// بناء تبويب الروابط
  Widget _buildLinksTab() {
    return _buildContentList(ContentType.links);
  }

  /// بناء تبويب المنشورات النصية
  Widget _buildTextPostsTab() {
    return _buildContentList(ContentType.textPosts);
  }

  /// بناء تبويب المنشورات المشاركة
  Widget _buildSharedPostsTab() {
    return _buildContentList(ContentType.sharedPosts);
  }

  /// بناء تبويب البث المباشر
  Widget _buildLiveStreamsTab() {
    return _buildContentList(ContentType.liveStreams);
  }

  /// بناء شبكة المحتوى (للصور والفيديوهات)
  Widget _buildContentGrid(ContentType contentType) {
    return Consumer2<ContentCategorizationService, AuthService>(
      builder: (context, contentService, authService, child) {
        final currentUserId = authService.currentUser?.phoneNumber ?? '';
        final content = contentService.searchInContent(
          contentType,
          _searchQuery,
          currentUserId,
          widget.user.phoneNumber,
          widget.user.privacySettings,
        );

        if (content.isEmpty) {
          return _buildEmptyState(contentType);
        }

        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: content.length,
          itemBuilder: (context, index) {
            final post = content[index];
            return _buildGridItem(post, contentType);
          },
        );
      },
    );
  }

  /// بناء قائمة المحتوى (للروابط والنصوص والمشاركات)
  Widget _buildContentList(ContentType contentType) {
    return Consumer2<ContentCategorizationService, AuthService>(
      builder: (context, contentService, authService, child) {
        final currentUserId = authService.currentUser?.phoneNumber ?? '';
        final content = contentService.searchInContent(
          contentType,
          _searchQuery,
          currentUserId,
          widget.user.phoneNumber,
          widget.user.privacySettings,
        );

        if (content.isEmpty) {
          return _buildEmptyState(contentType);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: content.length,
          itemBuilder: (context, index) {
            final post = content[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: PostWidget(
                post: post,
                onReaction: (emoji) {},
                onComment: () {},
                onShare: () {},
              ),
            );
          },
        );
      },
    );
  }

  /// بناء عنصر الشبكة
  Widget _buildGridItem(PostModel post, ContentType contentType) {
    return GestureDetector(
      onTap: () => _showPostDetails(post),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[200],
        ),
        child: Stack(
          children: [
            // محتوى العنصر
            Center(
              child: Icon(
                contentType == ContentType.photos ? Icons.image : Icons.play_circle_outline,
                color: Colors.grey[600],
                size: 32,
              ),
            ),

            // عدد التفاعلات
            if (post.reactions.isNotEmpty)
              Positioned(
                bottom: 4,
                right: 4,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    post.reactions.values.fold(0, (sum, count) => sum + count).toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(ContentType contentType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getContentTypeIcon(contentType),
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No ${contentType.displayName.toLowerCase()} yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            contentType.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نوع المحتوى
  IconData _getContentTypeIcon(ContentType contentType) {
    switch (contentType) {
      case ContentType.photos:
        return Icons.photo_library;
      case ContentType.videos:
        return Icons.video_library;
      case ContentType.links:
        return Icons.link;
      case ContentType.textPosts:
        return Icons.text_fields;
      case ContentType.sharedPosts:
        return Icons.share;
      case ContentType.liveStreams:
        return Icons.live_tv;
    }
  }

  /// عرض تفاصيل المنشور
  void _showPostDetails(PostModel post) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxHeight: 600),
          child: PostWidget(
            post: post,
            onReaction: (emoji) {},
            onComment: () {},
            onShare: () {},
          ),
        ),
      ),
    );
  }
}

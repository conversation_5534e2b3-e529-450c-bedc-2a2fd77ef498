import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

class StorageService {
  final ImagePicker _imagePicker = ImagePicker();

  // محاكاة رفع صورة (بدون Firebase)
  Future<String?> uploadImage(File imageFile, String folder) async {
    try {
      // محاكاة رفع الصورة - إرجاع مسار محلي
      await Future.delayed(const Duration(seconds: 1)); // محاكاة وقت الرفع
      return 'local://images/${DateTime.now().millisecondsSinceEpoch}.jpg';
    } catch (e) {
      debugPrint('خطأ في رفع الصورة: $e');
      return null;
    }
  }

  // محاكاة رفع ملف صوتي (بدون Firebase)
  Future<String?> uploadAudio(File audioFile, String folder) async {
    try {
      // محاكاة رفع الملف الصوتي - إرجاع مسار محلي
      await Future.delayed(const Duration(seconds: 1)); // محاكاة وقت الرفع
      return 'local://audio/${DateTime.now().millisecondsSinceEpoch}.m4a';
    } catch (e) {
      debugPrint('خطأ في رفع الملف الصوتي: $e');
      return null;
    }
  }

  // اختيار صورة من المعرض
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في اختيار الصورة من المعرض: $e');
      return null;
    }
  }

  // التقاط صورة بالكاميرا
  Future<File?> takePhoto() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في التقاط الصورة: $e');
      return null;
    }
  }

  // اختيار ملف
  Future<File?> pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في اختيار الملف: $e');
      return null;
    }
  }

  // محاكاة حذف ملف
  Future<bool> deleteFile(String fileUrl) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف الملف: $e');
      return false;
    }
  }

  // محاكاة رفع صورة الملف الشخصي
  Future<String?> uploadProfileImage(File imageFile, String userId) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return 'local://profiles/$userId.jpg';
    } catch (e) {
      debugPrint('خطأ في رفع صورة الملف الشخصي: $e');
      return null;
    }
  }

  // Show image picker options
  Future<File?> showImagePickerOptions(BuildContext context) async {
    return await showModalBottomSheet<File?>(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final image = await pickImageFromGallery();
                  Navigator.of(context).pop(image);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Take Photo'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final image = await takePhoto();
                  Navigator.of(context).pop(image);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('Cancel'),
                onTap: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // الحصول على حجم الملف بصيغة قابلة للقراءة
  String getFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }



  // Upload any file
  Future<String?> uploadFile(File file, String folder, String fileName) async {
    try {
      // For now, simulate upload and return a dummy URL
      await Future.delayed(const Duration(seconds: 2));

      // In a real app, you would upload to Firebase Storage or another service
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final dummyUrl = 'https://example.com/files/$folder/${timestamp}_$fileName';

      debugPrint('File uploaded: $dummyUrl');
      return dummyUrl;
    } catch (e) {
      debugPrint('Error uploading file: $e');
      return null;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/social_feed_service.dart';
import '../../services/auth_service.dart';
import '../../services/users_service.dart';
import '../../services/profile_customization_service.dart';
import 'dart:io';
import '../../models/post_model.dart';
import '../../models/user_model.dart';
import '../../widgets/post_widget.dart';
import 'profile_content_tabs.dart';
import '../settings/content_privacy_settings_screen.dart';


/// شاشة الملف الشخصي للمستخدم
class UserProfileScreen extends StatefulWidget {
  final UserModel user;
  final bool isCurrentUser;

  const UserProfileScreen({
    super.key,
    required this.user,
    this.isCurrentUser = false,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: const Color(0xFFD32F2F),
              flexibleSpace: FlexibleSpaceBar(
                background: _buildProfileHeader(),
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
              actions: [
                if (widget.isCurrentUser)
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.white),
                    onPressed: _editProfile,
                  )
                else
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    onSelected: (value) {
                      switch (value) {
                        case 'message':
                          _sendMessage();
                          break;
                        case 'block':
                          _blockUser();
                          break;
                        case 'report':
                          _reportUser();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'message',
                        child: Row(
                          children: [
                            Icon(Icons.message, size: 20),
                            SizedBox(width: 8),
                            Text('Send Message'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'block',
                        child: Row(
                          children: [
                            Icon(Icons.block, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Block User', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'report',
                        child: Row(
                          children: [
                            Icon(Icons.report, size: 20, color: Colors.orange),
                            SizedBox(width: 8),
                            Text('Report User', style: TextStyle(color: Colors.orange)),
                          ],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ];
        },
        body: Column(
          children: [
            // تبويبات الملف الشخصي
            Container(
              color: Colors.white,
              child: TabBar(
                controller: _tabController,
                labelColor: const Color(0xFFD32F2F),
                unselectedLabelColor: Colors.grey,
                indicatorColor: const Color(0xFFD32F2F),
                tabs: const [
                  Tab(text: 'Posts', icon: Icon(Icons.grid_on)),
                  Tab(text: 'About', icon: Icon(Icons.info)),
                  Tab(text: 'Photos', icon: Icon(Icons.photo)),
                ],
              ),
            ),

            // محتوى التبويبات
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPostsTab(),
                  _buildAboutTab(),
                  _buildPhotosTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الملف الشخصي
  Widget _buildProfileHeader() {
    return Consumer<ProfileCustomizationService>(
      builder: (context, customizationService, child) {
        final theme = customizationService.getUserTheme(widget.user.phoneNumber);
        final profileImage = customizationService.getProfileImage(widget.user.phoneNumber);
        final coverImage = customizationService.getCoverImage(widget.user.phoneNumber);

        return Container(
          decoration: BoxDecoration(
            gradient: theme.backgroundGradient ?? LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [theme.primaryColor, theme.secondaryColor],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 40), // مساحة للـ AppBar



                  // صورة الغلاف (اختيارية)
                  if (coverImage != null) ...[
                    Container(
                      height: 120,
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        image: DecorationImage(
                          image: FileImage(File(coverImage)),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],

                  // صورة المستخدم
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.white,
                        child: profileImage != null
                            ? CircleAvatar(
                                radius: 47,
                                backgroundImage: FileImage(File(profileImage)),
                              )
                            : CircleAvatar(
                                radius: 47,
                                backgroundColor: theme.primaryColor,
                                child: Text(
                                  widget.user.name.isNotEmpty
                                      ? widget.user.name[0].toUpperCase()
                                      : '?',
                                  style: TextStyle(
                                    color: theme.textColor == Colors.black ? Colors.white : theme.textColor,
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ),

                      // زر تعديل الصورة (للمستخدم الحالي فقط)
                      if (widget.isCurrentUser)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => _editProfileImage(customizationService),
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black26,
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.camera_alt,
                                size: 16,
                                color: theme.primaryColor,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // اسم المستخدم
                  Text(
                    widget.user.name,
                    style: TextStyle(
                      color: theme.textColor == Colors.black ? Colors.white : theme.textColor,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

              // معلومات إضافية
              Consumer<SocialFeedService>(
                builder: (context, socialService, child) {
                  final userPosts = socialService.getUserPosts(widget.user.phoneNumber);
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatItem('Posts', userPosts.length.toString()),
                      _buildStatItem('Joined', _getJoinDate()),
                    ],
                  );
                },
              ),

              const SizedBox(height: 16),

              // أزرار التفاعل
              if (!widget.isCurrentUser) _buildActionButtons(),

              // أزرار المحتوى والإعدادات (للمستخدم الحالي)
              if (widget.isCurrentUser) _buildContentButtons(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// بناء أزرار التفاعل
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: _sendMessage,
          icon: const Icon(Icons.message, size: 16),
          label: const Text('Message'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: const Color(0xFFD32F2F),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
        ),
        ElevatedButton.icon(
          onPressed: _callUser,
          icon: const Icon(Icons.call, size: 16),
          label: const Text('Call'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: const Color(0xFFD32F2F),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء أزرار المحتوى والإعدادات (للمستخدم الحالي)
  Widget _buildContentButtons() {
    return Column(
      children: [
        // زر تصنيف المحتوى
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 32),
          child: ElevatedButton.icon(
            onPressed: _openContentTabs,
            icon: const Icon(Icons.category, size: 18),
            label: const Text('View My Content'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFFD32F2F),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),

        const SizedBox(height: 8),

        // زر إعدادات الخصوصية
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 32),
          child: ElevatedButton.icon(
            onPressed: _openPrivacySettings,
            icon: const Icon(Icons.security, size: 18),
            label: const Text('Content Privacy'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFFD32F2F),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// تعديل صورة الملف الشخصي
  void _editProfileImage(ProfileCustomizationService customizationService) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Profile Picture',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.photo_camera, color: Color(0xFFD32F2F)),
              title: const Text('Upload New Photo'),
              onTap: () {
                Navigator.of(context).pop();
                _uploadProfileImage(customizationService);
              },
            ),
            ListTile(
              leading: const Icon(Icons.palette, color: Color(0xFFD32F2F)),
              title: const Text('Change Theme'),
              onTap: () {
                Navigator.of(context).pop();
                _showThemeSelector(customizationService);
              },
            ),
            if (customizationService.getProfileImage(widget.user.phoneNumber) != null)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Remove Photo'),
                onTap: () {
                  Navigator.of(context).pop();
                  _removeProfileImage(customizationService);
                },
              ),
          ],
        ),
      ),
    );
  }

  /// رفع صورة الملف الشخصي
  void _uploadProfileImage(ProfileCustomizationService customizationService) async {
    final success = await customizationService.uploadProfileImage(widget.user.phoneNumber);
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('✅ Profile picture updated!')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Failed to update profile picture'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إزالة صورة الملف الشخصي
  void _removeProfileImage(ProfileCustomizationService customizationService) async {
    final success = await customizationService.removeProfileImage(widget.user.phoneNumber);
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('✅ Profile picture removed!')),
        );
      }
    }
  }

  /// عرض محدد الثيم
  void _showThemeSelector(ProfileCustomizationService customizationService) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Choose Theme',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: customizationService.getAvailableThemes().length,
                itemBuilder: (context, index) {
                  final theme = customizationService.getAvailableThemes()[index];
                  return GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      _applyTheme(customizationService, theme);
                    },
                    child: Container(
                      width: 80,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        gradient: theme.backgroundGradient,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 2,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.palette,
                            color: theme.textColor == Colors.black ? Colors.white : theme.textColor,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            theme.name,
                            style: TextStyle(
                              color: theme.textColor == Colors.black ? Colors.white : theme.textColor,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تطبيق الثيم
  void _applyTheme(ProfileCustomizationService customizationService, ProfileTheme theme) async {
    final success = await customizationService.updateProfileTheme(widget.user.phoneNumber, theme);
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('✅ Theme changed to ${theme.name}!')),
        );
      }
    }
  }

  /// فتح شاشة تصنيف المحتوى
  void _openContentTabs() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProfileContentTabs(
          user: widget.user,
          isCurrentUser: widget.isCurrentUser,
        ),
      ),
    );
  }

  /// فتح إعدادات خصوصية المحتوى
  void _openPrivacySettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ContentPrivacySettingsScreen(),
      ),
    );
  }

  /// الحصول على تاريخ الانضمام
  String _getJoinDate() {
    // استخدام تاريخ التسجيل الحقيقي
    final registrationDate = widget.user.registrationDate;
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[registrationDate.month - 1]} ${registrationDate.year}';
  }

  /// بناء تبويب المنشورات
  Widget _buildPostsTab() {
    return Consumer<SocialFeedService>(
      builder: (context, socialService, child) {
        final userPosts = socialService.getUserPosts(widget.user.phoneNumber);

        if (userPosts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.post_add,
                  size: 80,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  widget.isCurrentUser ? 'No posts yet' : '${widget.user.name} hasn\'t posted anything yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                if (widget.isCurrentUser) ...[
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Create Your First Post'),
                  ),
                ],
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: userPosts.length,
          itemBuilder: (context, index) {
            final post = userPosts[index];
            return PostWidget(
              post: post,
              onReaction: (emoji) => _handleReaction(post.id, emoji),
              onComment: () => _showCommentsSheet(post),
              onShare: () => _handleShare(post.id),
              onEdit: widget.isCurrentUser ? () => _editPost(post) : null,
              onDelete: widget.isCurrentUser ? () => _deletePost(post.id) : null,
            );
          },
        );
      },
    );
  }

  /// بناء تبويب معلومات المستخدم
  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFacebookStyleInfoSection('Contact Information', [
            _buildFacebookStyleInfoItem(Icons.phone, 'Phone', widget.user.phoneNumber, const Color(0xFF42B883)),
          ]),

          const SizedBox(height: 24),

          _buildFacebookStyleInfoSection('Personal Information', [
            _buildFacebookStyleInfoItem(Icons.person, 'Name', widget.user.name, const Color(0xFF1877F2)),
            _buildFacebookStyleInfoItem(
              widget.user.gender == Gender.male ? Icons.male : Icons.female,
              'Gender',
              widget.user.gender.toString().split('.').last.toUpperCase(),
              widget.user.gender == Gender.male ? const Color(0xFF4267B2) : const Color(0xFFE91E63)
            ),
            _buildFacebookStyleInfoItem(Icons.cake, 'Age', '${widget.user.age} years old', const Color(0xFFFF9800)),
            _buildFacebookStyleInfoItem(Icons.public, 'Country', widget.user.country, const Color(0xFF4CAF50)),
            _buildFacebookStyleInfoItem(Icons.location_city, 'City', widget.user.city, const Color(0xFF9C27B0)),
          ]),

          const SizedBox(height: 24),

          _buildFacebookStyleInfoSection('Additional Information', [
            _buildFacebookStyleInfoItem(Icons.access_time, 'Joined', 'Member since 2024', const Color(0xFF607D8B)),
            _buildFacebookStyleInfoItem(Icons.verified, 'Status', 'Active User', const Color(0xFF4CAF50)),
            _buildFacebookStyleInfoItem(Icons.language, 'Language', 'Arabic, English', const Color(0xFF795548)),
          ]),

          const SizedBox(height: 24),

          _buildInfoSection('Account', [
            _buildInfoItem('Joined', _getJoinDate()),
            _buildInfoItem('Status', widget.user.isOnline ? 'Online' : 'Offline'),
          ]),
        ],
      ),
    );
  }

  /// بناء قسم معلومات
  Widget _buildInfoSection(String title, List<Widget> items) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFFD32F2F),
            ),
          ),
          const SizedBox(height: 12),
          ...items,
        ],
      ),
    );
  }

  /// بناء قسم معلومات بتصميم Facebook
  Widget _buildFacebookStyleInfoSection(String title, List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1C1E21),
              ),
            ),
          ),
          const Divider(height: 1, color: Color(0xFFE4E6EA)),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(children: items),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومة بتصميم Facebook مع أيقونة
  Widget _buildFacebookStyleInfoItem(IconData icon, String label, String value, Color iconColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // أيقونة ملونة مع خلفية دائرية
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),

          // المعلومات
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1C1E21),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF65676B),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومة (احتياطي)
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب الصور
  Widget _buildPhotosTab() {
    return Consumer<SocialFeedService>(
      builder: (context, socialService, child) {
        final userPosts = socialService.getUserPosts(widget.user.phoneNumber);
        final postsWithImages = userPosts.where((post) => post.images.isNotEmpty).toList();

        if (postsWithImages.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.photo_library,
                  size: 80,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No photos yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return GridView.builder(
          padding: const EdgeInsets.all(8),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: postsWithImages.fold<int>(0, (sum, post) => sum + post.images.length),
          itemBuilder: (context, index) {
            // حساب الصورة المطلوبة
            int currentIndex = 0;
            for (final post in postsWithImages) {
              if (index < currentIndex + post.images.length) {
                final imageIndex = index - currentIndex;
                return GestureDetector(
                  onTap: () => _viewImage(post.images[imageIndex]),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[300],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image,
                          size: 40,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                );
              }
              currentIndex += post.images.length;
            }
            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  /// تعديل الملف الشخصي
  void _editProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit profile feature coming soon!')),
    );
  }

  /// إرسال رسالة
  void _sendMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening chat with ${widget.user.name}...')),
    );
  }

  /// اتصال بالمستخدم
  void _callUser() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Calling ${widget.user.name}...')),
    );
  }



  /// حظر المستخدم
  void _blockUser() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Text('Are you sure you want to block ${widget.user.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${widget.user.name} has been blocked')),
              );
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// الإبلاغ عن المستخدم
  void _reportUser() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${widget.user.name} has been reported')),
    );
  }

  /// التعامل مع التفاعل
  void _handleReaction(String postId, String emoji) {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      socialService.addPostReaction(
        postId: postId,
        emoji: emoji,
        userId: currentUser.phoneNumber,
      );
    }
  }

  /// عرض ورقة التعليقات
  void _showCommentsSheet(PostModel post) {
    // نفس الكود من social_feed_screen.dart
  }

  /// التعامل مع المشاركة
  void _handleShare(String postId) {
    // نفس الكود من social_feed_screen.dart
  }

  /// تعديل منشور
  void _editPost(PostModel post) {
    // نفس الكود من social_feed_screen.dart
  }

  /// حذف منشور
  void _deletePost(String postId) {
    // نفس الكود من social_feed_screen.dart
  }

  /// عرض صورة
  void _viewImage(String imagePath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: Container(
                color: Colors.grey[300],
                child: const Icon(
                  Icons.image,
                  size: 100,
                  color: Colors.grey,
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

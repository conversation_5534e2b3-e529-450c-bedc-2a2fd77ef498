import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../services/social_feed_service.dart';
import '../services/auth_service.dart';

/// ويدجت التفاعلات المتقدمة مثل Facebook
class AdvancedReactionsWidget extends StatefulWidget {
  final PostModel post;
  final Function(String) onReaction;

  const AdvancedReactionsWidget({
    Key? key,
    required this.post,
    required this.onReaction,
  }) : super(key: key);

  @override
  State<AdvancedReactionsWidget> createState() => _AdvancedReactionsWidgetState();
}

class _AdvancedReactionsWidgetState extends State<AdvancedReactionsWidget>
    with TickerProviderStateMixin {
  bool _showReactions = false;
  String? _currentUserReaction;
  late AnimationController _scaleController;
  late AnimationController _slideController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  // تعريف التفاعلات المتقدمة
  final List<ReactionData> _reactions = [
    ReactionData(
      emoji: '👍',
      name: 'Like',
      color: const Color(0xFF1877F2),
      id: 'like',
    ),
    ReactionData(
      emoji: '❤️',
      name: 'Love',
      color: const Color(0xFFE91E63),
      id: 'love',
    ),
    ReactionData(
      emoji: '😂',
      name: 'Haha',
      color: const Color(0xFFFFC107),
      id: 'haha',
    ),
    ReactionData(
      emoji: '😮',
      name: 'Wow',
      color: const Color(0xFFFFC107),
      id: 'wow',
    ),
    ReactionData(
      emoji: '😢',
      name: 'Sad',
      color: const Color(0xFFFFC107),
      id: 'sad',
    ),
    ReactionData(
      emoji: '😡',
      name: 'Angry',
      color: const Color(0xFFFF5722),
      id: 'angry',
    ),
    ReactionData(
      emoji: '🙏',
      name: 'Support',
      color: const Color(0xFF4CAF50),
      id: 'support',
    ),
    ReactionData(
      emoji: '👏',
      name: 'Clap',
      color: const Color(0xFF9C27B0),
      id: 'clap',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCurrentUserReaction();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
  }

  void _loadCurrentUserReaction() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      final reaction = await socialService.getUserReaction(
        widget.post.id,
        currentUser.phoneNumber,
      );
      if (mounted) {
        setState(() {
          _currentUserReaction = reaction;
        });
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: _showReactionsPanel,
      onTap: _handleQuickReaction,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // زر التفاعل الرئيسي
          _buildMainReactionButton(),
          
          // لوحة التفاعلات المتقدمة
          if (_showReactions) _buildReactionsPanel(),
        ],
      ),
    );
  }

  /// بناء زر التفاعل الرئيسي
  Widget _buildMainReactionButton() {
    final currentReaction = _getCurrentReactionData();
    final hasReacted = _currentUserReaction != null;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: hasReacted ? currentReaction.color.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        border: hasReacted 
            ? Border.all(color: currentReaction.color.withOpacity(0.3))
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة التفاعل مع تأثير النبض
          AnimatedScale(
            scale: hasReacted ? 1.2 : 1.0,
            duration: const Duration(milliseconds: 200),
            child: Text(
              hasReacted ? currentReaction.emoji : '👍',
              style: const TextStyle(fontSize: 18),
            ),
          ),
          
          const SizedBox(width: 6),
          
          // نص التفاعل
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 200),
            style: TextStyle(
              color: hasReacted ? currentReaction.color : const Color(0xFF65676B),
              fontSize: 14,
              fontWeight: hasReacted ? FontWeight.w600 : FontWeight.w500,
            ),
            child: Text(hasReacted ? currentReaction.name : 'Like'),
          ),
        ],
      ),
    );
  }

  /// بناء لوحة التفاعلات المتقدمة
  Widget _buildReactionsPanel() {
    return Positioned(
      bottom: 50,
      left: -20,
      child: SlideTransition(
        position: _slideAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: _reactions.map((reaction) => _buildReactionItem(reaction)).toList(),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء عنصر تفاعل واحد
  Widget _buildReactionItem(ReactionData reaction) {
    final isSelected = _currentUserReaction == reaction.emoji;
    
    return GestureDetector(
      onTap: () => _selectReaction(reaction),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? reaction.color.withOpacity(0.1) : Colors.transparent,
          shape: BoxShape.circle,
          border: isSelected 
              ? Border.all(color: reaction.color, width: 2)
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الإيموجي مع تأثير الحركة
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 300),
              tween: Tween(begin: 1.0, end: isSelected ? 1.3 : 1.0),
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Text(
                    reaction.emoji,
                    style: const TextStyle(fontSize: 24),
                  ),
                );
              },
            ),
            
            // اسم التفاعل
            if (isSelected) ...[
              const SizedBox(height: 4),
              Text(
                reaction.name,
                style: TextStyle(
                  color: reaction.color,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// الحصول على بيانات التفاعل الحالي
  ReactionData _getCurrentReactionData() {
    if (_currentUserReaction == null) {
      return _reactions.first; // Like افتراضي
    }
    
    return _reactions.firstWhere(
      (reaction) => reaction.emoji == _currentUserReaction,
      orElse: () => _reactions.first,
    );
  }

  /// عرض لوحة التفاعلات
  void _showReactionsPanel() {
    setState(() {
      _showReactions = true;
    });
    
    _slideController.forward();
    _scaleController.forward();
    
    // إخفاء اللوحة بعد 3 ثوان
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showReactions) {
        _hideReactionsPanel();
      }
    });
  }

  /// إخفاء لوحة التفاعلات
  void _hideReactionsPanel() {
    _slideController.reverse();
    _scaleController.reverse();
    
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _showReactions = false;
        });
      }
    });
  }

  /// التفاعل السريع (نقرة واحدة)
  void _handleQuickReaction() {
    if (_currentUserReaction == null) {
      // إضافة Like افتراضي
      _selectReaction(_reactions.first);
    } else {
      // إزالة التفاعل الحالي
      _removeReaction();
    }
  }

  /// اختيار تفاعل
  void _selectReaction(ReactionData reaction) {
    setState(() {
      _currentUserReaction = reaction.emoji;
    });
    
    widget.onReaction(reaction.emoji);
    _hideReactionsPanel();
    
    // تأثير بصري للتأكيد
    _showReactionConfirmation(reaction);
  }

  /// إزالة التفاعل
  void _removeReaction() {
    setState(() {
      _currentUserReaction = null;
    });
    
    widget.onReaction(''); // إرسال تفاعل فارغ للإزالة
  }

  /// عرض تأكيد التفاعل
  void _showReactionConfirmation(ReactionData reaction) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.4,
        left: MediaQuery.of(context).size.width * 0.4,
        child: Material(
          color: Colors.transparent,
          child: TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 1.0 + (value * 0.5),
                child: Opacity(
                  opacity: 1.0 - value,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: reaction.color.withOpacity(0.9),
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      reaction.emoji,
                      style: const TextStyle(fontSize: 32),
                    ),
                  ),
                ),
              );
            },
            onEnd: () => overlayEntry.remove(),
          ),
        ),
      ),
    );
    
    overlay.insert(overlayEntry);
  }
}

/// بيانات التفاعل
class ReactionData {
  final String emoji;
  final String name;
  final Color color;
  final String id;

  const ReactionData({
    required this.emoji,
    required this.name,
    required this.color,
    required this.id,
  });
}

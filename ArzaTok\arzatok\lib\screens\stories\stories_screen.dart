import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/story_model.dart';
import '../../services/stories_service.dart';
import '../../services/auth_service.dart';
import '../../services/story_service.dart';
import '../../services/media_service.dart';
import 'create_story_screen.dart';
import 'story_viewer_screen.dart';

/// شاشة القصص الرئيسية
class StoriesScreen extends StatefulWidget {
  const StoriesScreen({super.key});

  @override
  State<StoriesScreen> createState() => _StoriesScreenState();
}

class _StoriesScreenState extends State<StoriesScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<StoriesService>(context, listen: false).loadStories();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Stories',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFFD32F2F),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Consumer<StoriesService>(
        builder: (context, storiesService, child) {
          if (storiesService.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final userStories = storiesService.userStories;

              return CustomScrollView(
                slivers: [
                  // قسم "قصتي"
                  SliverToBoxAdapter(
                    child: _buildMyStorySection(context),
                  ),

                  // فاصل
                  const SliverToBoxAdapter(
                    child: Divider(height: 1, color: Colors.grey),
                  ),

                  // قائمة قصص الأصدقاء
                  if (userStories.isEmpty)
                    const SliverFillRemaining(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.auto_stories_outlined,
                              size: 80,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No stories yet',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Stories from your contacts will appear here',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final userStory = userStories[index];
                          return _buildStoryItem(context, userStory);
                        },
                        childCount: userStories.length,
                      ),
                    ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  /// بناء قسم "قصتي"
  Widget _buildMyStorySection(BuildContext context) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final storyService = Provider.of<StoryService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) return const SizedBox.shrink();

    final myStories = storyService.getCurrentUserStories();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // صورة المستخدم مع زر الإضافة
          Stack(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: myStories.isNotEmpty
                        ? const Color(0xFFD32F2F)
                        : Colors.grey,
                    width: 3,
                  ),
                ),
                child: CircleAvatar(
                  radius: 27,
                  backgroundImage: currentUser.profileImageUrl != null
                      ? NetworkImage(currentUser.profileImageUrl!)
                      : null,
                  backgroundColor: Colors.grey[300],
                  child: currentUser.profileImageUrl == null
                      ? Text(
                          currentUser.name.isNotEmpty
                              ? currentUser.name[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
              ),

              // زر الإضافة
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => _showAddStoryOptions(context),
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Color(0xFFD32F2F),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(width: 16),

          // معلومات القصة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'My Story',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  myStories.isNotEmpty
                      ? 'Tap to view or add to your story'
                      : 'Tap to add to your story',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // زر عرض قصصي
          if (myStories.isNotEmpty)
            IconButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => MyStoriesScreen(),
                  ),
                );
              },
              icon: const Icon(
                Icons.more_vert,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عنصر قصة
  Widget _buildStoryItem(BuildContext context, UserStoriesModel userStory) {
    final latestStory = userStory.latestStory;
    if (latestStory == null) return const SizedBox.shrink();

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Stack(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: userStory.hasUnviewedStories
                    ? const Color(0xFFD32F2F)
                    : Colors.grey,
                width: 3,
              ),
            ),
            child: CircleAvatar(
              radius: 27,
              backgroundImage: userStory.userProfileImage != null
                  ? NetworkImage(userStory.userProfileImage!)
                  : null,
              backgroundColor: Colors.grey[300],
              child: userStory.userProfileImage == null
                  ? Text(
                      userStory.userName.isNotEmpty
                          ? userStory.userName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
          ),

          // مؤشر عدد القصص
          if (userStory.stories.length > 1)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Color(0xFFD32F2F),
                  shape: BoxShape.circle,
                ),
                child: Text(
                  '${userStory.stories.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      title: Text(
        userStory.userName,
        style: TextStyle(
          fontWeight: userStory.hasUnviewedStories
              ? FontWeight.w600
              : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        _getStoryPreview(latestStory),
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 13,
        ),
      ),
      trailing: Text(
        _getTimeAgo(latestStory.createdAt),
        style: TextStyle(
          color: Colors.grey[500],
          fontSize: 12,
        ),
      ),
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => StoryViewerScreen(
              userStories: userStory,
              initialIndex: 0,
            ),
          ),
        );
      },
    );
  }

  /// عرض خيارات إضافة قصة
  void _showAddStoryOptions(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateStoryScreen(),
      ),
    );
  }

  /// إضافة قصة جديدة
  void _addStory(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Add to Story',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // الخيارات
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.text_fields, color: Colors.blue),
              ),
              title: const Text('Text Story'),
              subtitle: const Text('Create a story with text'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CreateStoryScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.photo_library, color: Colors.green),
              ),
              title: const Text('Photo Story'),
              subtitle: const Text('Share a photo from gallery'),
              onTap: () async {
                Navigator.of(context).pop();
                final mediaService = MediaService();
                final imagePath = await mediaService.pickImage();
                if (imagePath != null && context.mounted) {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => CreateStoryScreen(
                        initialMediaPath: imagePath,
                        mediaType: 'image',
                      ),
                    ),
                  );
                }
              },
            ),

            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.videocam, color: Colors.red),
              ),
              title: const Text('Video Story'),
              subtitle: const Text('Share a video from gallery'),
              onTap: () async {
                Navigator.of(context).pop();
                final mediaService = MediaService();
                final videoPath = await mediaService.pickVideo();
                if (videoPath != null && context.mounted) {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => CreateStoryScreen(
                        initialMediaPath: videoPath,
                        mediaType: 'video',
                      ),
                    ),
                  );
                }
              },
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// الحصول على معاينة القصة
  String _getStoryPreview(StoryModel story) {
    switch (story.type) {
      case StoryType.text:
        return story.content ?? '';
      case StoryType.image:
        return '📷 Photo';
      case StoryType.video:
        return '🎥 Video';
    }
  }

  /// الحصول على النص الزمني
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نوع الثيم المتقدم
enum AdvancedTheme {
  light,
  dark,
  system,
  red,
  blue,
  green,
  purple,
  orange,
  pink,
  teal,
}

/// خدمة الثيمات المتقدمة
class AdvancedThemeService extends ChangeNotifier {
  static const String _themeKey = 'advanced_theme';
  static const String _fontSizeKey = 'font_size';
  static const String _fontFamilyKey = 'font_family';
  static const String _customColorsKey = 'custom_colors';
  
  AdvancedTheme _currentTheme = AdvancedTheme.system;
  double _fontSize = 16.0;
  String _fontFamily = 'Roboto';
  final Map<String, Color> _customColors = {};
  
  // Getters
  AdvancedTheme get currentTheme => _currentTheme;
  double get fontSize => _fontSize;
  String get fontFamily => _fontFamily;
  Map<String, Color> get customColors => Map.from(_customColors);
  
  bool get isDarkMode {
    switch (_currentTheme) {
      case AdvancedTheme.dark:
        return true;
      case AdvancedTheme.light:
        return false;
      case AdvancedTheme.system:
        return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
      default:
        return false;
    }
  }
  
  ThemeMode get themeMode {
    switch (_currentTheme) {
      case AdvancedTheme.dark:
        return ThemeMode.dark;
      case AdvancedTheme.light:
        return ThemeMode.light;
      case AdvancedTheme.system:
        return ThemeMode.system;
      default:
        return ThemeMode.light;
    }
  }

  /// تحميل الثيم
  Future<void> loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final themeIndex = prefs.getInt(_themeKey) ?? AdvancedTheme.system.index;
      _currentTheme = AdvancedTheme.values[themeIndex];
      
      _fontSize = prefs.getDouble(_fontSizeKey) ?? 16.0;
      _fontFamily = prefs.getString(_fontFamilyKey) ?? 'Roboto';
      
      // تحميل الألوان المخصصة
      final customColorsString = prefs.getString(_customColorsKey);
      if (customColorsString != null) {
        _loadCustomColors(customColorsString);
      }
      
      notifyListeners();
      debugPrint('🎨 Advanced theme loaded: $_currentTheme');
    } catch (e) {
      debugPrint('❌ Error loading advanced theme: $e');
    }
  }
  
  /// تعيين الثيم
  Future<void> setTheme(AdvancedTheme theme) async {
    try {
      _currentTheme = theme;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, theme.index);
      notifyListeners();
      debugPrint('🎨 Advanced theme changed to: $theme');
    } catch (e) {
      debugPrint('❌ Error setting advanced theme: $e');
    }
  }
  
  /// تعيين حجم الخط
  Future<void> setFontSize(double size) async {
    try {
      _fontSize = size.clamp(12.0, 24.0);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_fontSizeKey, _fontSize);
      notifyListeners();
      debugPrint('🎨 Font size changed to: $_fontSize');
    } catch (e) {
      debugPrint('❌ Error setting font size: $e');
    }
  }
  
  /// تعيين عائلة الخط
  Future<void> setFontFamily(String family) async {
    try {
      _fontFamily = family;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_fontFamilyKey, family);
      notifyListeners();
      debugPrint('🎨 Font family changed to: $family');
    } catch (e) {
      debugPrint('❌ Error setting font family: $e');
    }
  }
  
  /// تعيين لون مخصص
  Future<void> setCustomColor(String key, Color color) async {
    try {
      _customColors[key] = color;
      await _saveCustomColors();
      notifyListeners();
      debugPrint('🎨 Custom color set: $key = ${color.value}');
    } catch (e) {
      debugPrint('❌ Error setting custom color: $e');
    }
  }
  
  /// الحصول على لون مخصص
  Color? getCustomColor(String key) {
    return _customColors[key];
  }
  
  /// حفظ الألوان المخصصة
  Future<void> _saveCustomColors() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colorsMap = _customColors.map((key, color) => MapEntry(key, color.value));
      final colorsString = colorsMap.entries.map((e) => '${e.key}:${e.value}').join(',');
      await prefs.setString(_customColorsKey, colorsString);
    } catch (e) {
      debugPrint('❌ Error saving custom colors: $e');
    }
  }
  
  /// تحميل الألوان المخصصة
  void _loadCustomColors(String colorsString) {
    try {
      _customColors.clear();
      final pairs = colorsString.split(',');
      for (final pair in pairs) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          final key = parts[0];
          final colorValue = int.tryParse(parts[1]);
          if (colorValue != null) {
            _customColors[key] = Color(colorValue);
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error loading custom colors: $e');
    }
  }
  
  /// الحصول على اللون الأساسي
  Color getPrimaryColor() {
    switch (_currentTheme) {
      case AdvancedTheme.red:
        return const Color(0xFFD32F2F);
      case AdvancedTheme.blue:
        return const Color(0xFF1976D2);
      case AdvancedTheme.green:
        return const Color(0xFF388E3C);
      case AdvancedTheme.purple:
        return const Color(0xFF7B1FA2);
      case AdvancedTheme.orange:
        return const Color(0xFFF57C00);
      case AdvancedTheme.pink:
        return const Color(0xFFE91E63);
      case AdvancedTheme.teal:
        return const Color(0xFF00796B);
      default:
        return _customColors['primary'] ?? const Color(0xFFD32F2F);
    }
  }
  
  /// الحصول على ثيم فاتح
  ThemeData getLightTheme() {
    final primaryColor = getPrimaryColor();
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: Colors.white,
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 1,
        centerTitle: true,
      ),
      textTheme: _getTextTheme(Brightness.light),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      ),
    );
  }
  
  /// الحصول على ثيم داكن
  ThemeData getDarkTheme() {
    final primaryColor = getPrimaryColor();
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: const Color(0xFF121212),
      appBarTheme: AppBarTheme(
        backgroundColor: const Color(0xFF1F1F1F),
        foregroundColor: Colors.white,
        elevation: 1,
        centerTitle: true,
      ),
      textTheme: _getTextTheme(Brightness.dark),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        color: const Color(0xFF1E1E1E),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
      ),
    );
  }
  
  /// الحصول على نسق النصوص
  TextTheme _getTextTheme(Brightness brightness) {
    final baseTheme = brightness == Brightness.light 
        ? ThemeData.light().textTheme 
        : ThemeData.dark().textTheme;
    
    return baseTheme.copyWith(
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: _fontSize,
        fontFamily: _fontFamily,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: _fontSize - 2,
        fontFamily: _fontFamily,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: _fontSize - 4,
        fontFamily: _fontFamily,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontFamily: _fontFamily,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontFamily: _fontFamily,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontFamily: _fontFamily,
      ),
    );
  }
  
  /// إنشاء MaterialColor
  MaterialColor _createMaterialColor(Color color) {
    final strengths = <double>[.05];
    final swatch = <int, Color>{};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    
    for (final strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    
    return MaterialColor(color.value, swatch);
  }
  
  /// الحصول على الثيمات المتاحة
  List<Map<String, dynamic>> getAvailableThemes() {
    return [
      {'name': 'System', 'theme': AdvancedTheme.system, 'color': Colors.grey, 'icon': Icons.brightness_auto},
      {'name': 'Light', 'theme': AdvancedTheme.light, 'color': Colors.white, 'icon': Icons.brightness_7},
      {'name': 'Dark', 'theme': AdvancedTheme.dark, 'color': Colors.black, 'icon': Icons.brightness_2},
      {'name': 'Red', 'theme': AdvancedTheme.red, 'color': const Color(0xFFD32F2F), 'icon': Icons.favorite},
      {'name': 'Blue', 'theme': AdvancedTheme.blue, 'color': const Color(0xFF1976D2), 'icon': Icons.water_drop},
      {'name': 'Green', 'theme': AdvancedTheme.green, 'color': const Color(0xFF388E3C), 'icon': Icons.eco},
      {'name': 'Purple', 'theme': AdvancedTheme.purple, 'color': const Color(0xFF7B1FA2), 'icon': Icons.auto_awesome},
      {'name': 'Orange', 'theme': AdvancedTheme.orange, 'color': const Color(0xFFF57C00), 'icon': Icons.wb_sunny},
      {'name': 'Pink', 'theme': AdvancedTheme.pink, 'color': const Color(0xFFE91E63), 'icon': Icons.favorite_border},
      {'name': 'Teal', 'theme': AdvancedTheme.teal, 'color': const Color(0xFF00796B), 'icon': Icons.waves},
    ];
  }
  
  /// إعادة تعيين الثيم للافتراضي
  Future<void> resetToDefault() async {
    try {
      _currentTheme = AdvancedTheme.system;
      _fontSize = 16.0;
      _fontFamily = 'Roboto';
      _customColors.clear();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_themeKey);
      await prefs.remove(_fontSizeKey);
      await prefs.remove(_fontFamilyKey);
      await prefs.remove(_customColorsKey);
      
      notifyListeners();
      debugPrint('🎨 Advanced theme reset to default');
    } catch (e) {
      debugPrint('❌ Error resetting advanced theme: $e');
    }
  }
}

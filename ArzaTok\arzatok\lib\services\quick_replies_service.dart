import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// رد سريع
class QuickReply {
  final String id;
  final String title;
  final String content;
  final String category;
  final DateTime createdAt;
  final int usageCount;
  final bool isDefault;

  const QuickReply({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.createdAt,
    this.usageCount = 0,
    this.isDefault = false,
  });

  QuickReply copyWith({
    String? id,
    String? title,
    String? content,
    String? category,
    DateTime? createdAt,
    int? usageCount,
    bool? isDefault,
  }) {
    return QuickReply(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      usageCount: usageCount ?? this.usageCount,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'usageCount': usageCount,
      'isDefault': isDefault,
    };
  }

  factory QuickReply.fromMap(Map<String, dynamic> map) {
    return QuickReply(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      content: map['content'] ?? '',
      category: map['category'] ?? 'general',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      usageCount: map['usageCount'] ?? 0,
      isDefault: map['isDefault'] ?? false,
    );
  }
}

/// فئات الردود السريعة
class QuickReplyCategory {
  static const String general = 'general';
  static const String greetings = 'greetings';
  static const String work = 'work';
  static const String social = 'social';
  static const String emergency = 'emergency';
  static const String emoji = 'emoji';
  static const String questions = 'questions';
  static const String confirmations = 'confirmations';
}

/// خدمة الردود السريعة
class QuickRepliesService extends ChangeNotifier {
  static final QuickRepliesService _instance = QuickRepliesService._internal();
  factory QuickRepliesService() => _instance;
  QuickRepliesService._internal();

  final List<QuickReply> _quickReplies = [];
  final List<String> _recentlyUsed = [];

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadQuickReplies();
    if (_quickReplies.isEmpty) {
      await _addDefaultReplies();
    }
  }

  /// إضافة الردود الافتراضية
  Future<void> _addDefaultReplies() async {
    final defaultReplies = [
      // تحيات
      QuickReply(
        id: 'greeting_1',
        title: 'Hello',
        content: 'Hello! How are you? 👋',
        category: QuickReplyCategory.greetings,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'greeting_2',
        title: 'Good Morning',
        content: 'Good morning! Have a great day! ☀️',
        category: QuickReplyCategory.greetings,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'greeting_3',
        title: 'Good Night',
        content: 'Good night! Sweet dreams! 🌙',
        category: QuickReplyCategory.greetings,
        createdAt: DateTime.now(),
        isDefault: true,
      ),

      // عمل
      QuickReply(
        id: 'work_1',
        title: 'In Meeting',
        content: 'I\'m currently in a meeting. I\'ll get back to you soon! 💼',
        category: QuickReplyCategory.work,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'work_2',
        title: 'Running Late',
        content: 'I\'m running a bit late. Will be there in 10 minutes! 🏃‍♂️',
        category: QuickReplyCategory.work,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'work_3',
        title: 'Task Complete',
        content: 'Task completed successfully! ✅',
        category: QuickReplyCategory.work,
        createdAt: DateTime.now(),
        isDefault: true,
      ),

      // اجتماعي
      QuickReply(
        id: 'social_1',
        title: 'Thank You',
        content: 'Thank you so much! I really appreciate it! 🙏',
        category: QuickReplyCategory.social,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'social_2',
        title: 'Congratulations',
        content: 'Congratulations! That\'s amazing news! 🎉',
        category: QuickReplyCategory.social,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'social_3',
        title: 'Happy Birthday',
        content: 'Happy Birthday! Hope you have a wonderful day! 🎂',
        category: QuickReplyCategory.social,
        createdAt: DateTime.now(),
        isDefault: true,
      ),

      // أسئلة
      QuickReply(
        id: 'question_1',
        title: 'What Time?',
        content: 'What time works best for you? ⏰',
        category: QuickReplyCategory.questions,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'question_2',
        title: 'Where?',
        content: 'Where should we meet? 📍',
        category: QuickReplyCategory.questions,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'question_3',
        title: 'How Are You?',
        content: 'How are you doing? Hope everything is well! 😊',
        category: QuickReplyCategory.questions,
        createdAt: DateTime.now(),
        isDefault: true,
      ),

      // تأكيدات
      QuickReply(
        id: 'confirm_1',
        title: 'Yes',
        content: 'Yes, sounds good! 👍',
        category: QuickReplyCategory.confirmations,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'confirm_2',
        title: 'No Problem',
        content: 'No problem at all! 👌',
        category: QuickReplyCategory.confirmations,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'confirm_3',
        title: 'Will Do',
        content: 'Will do! Consider it done! ✅',
        category: QuickReplyCategory.confirmations,
        createdAt: DateTime.now(),
        isDefault: true,
      ),

      // إيموجي
      QuickReply(
        id: 'emoji_1',
        title: 'Thumbs Up',
        content: '👍',
        category: QuickReplyCategory.emoji,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'emoji_2',
        title: 'Heart',
        content: '❤️',
        category: QuickReplyCategory.emoji,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'emoji_3',
        title: 'Laughing',
        content: '😂',
        category: QuickReplyCategory.emoji,
        createdAt: DateTime.now(),
        isDefault: true,
      ),

      // طوارئ
      QuickReply(
        id: 'emergency_1',
        title: 'Call Me',
        content: 'Please call me urgently! 📞',
        category: QuickReplyCategory.emergency,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
      QuickReply(
        id: 'emergency_2',
        title: 'Need Help',
        content: 'I need help! Please respond ASAP! 🆘',
        category: QuickReplyCategory.emergency,
        createdAt: DateTime.now(),
        isDefault: true,
      ),
    ];

    for (final reply in defaultReplies) {
      _quickReplies.add(reply);
    }

    await _saveQuickReplies();
    notifyListeners();
    debugPrint('📝 Added ${defaultReplies.length} default quick replies');
  }

  /// إضافة رد سريع جديد
  Future<bool> addQuickReply({
    required String title,
    required String content,
    required String category,
  }) async {
    try {
      final reply = QuickReply(
        id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        content: content,
        category: category,
        createdAt: DateTime.now(),
      );

      _quickReplies.add(reply);
      await _saveQuickReplies();
      notifyListeners();

      debugPrint('📝 Quick reply added: $title');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding quick reply: $e');
      return false;
    }
  }

  /// تحديث رد سريع
  Future<bool> updateQuickReply(String id, {
    String? title,
    String? content,
    String? category,
  }) async {
    try {
      final index = _quickReplies.indexWhere((reply) => reply.id == id);
      if (index == -1) return false;

      final oldReply = _quickReplies[index];
      final updatedReply = oldReply.copyWith(
        title: title,
        content: content,
        category: category,
      );

      _quickReplies[index] = updatedReply;
      await _saveQuickReplies();
      notifyListeners();

      debugPrint('📝 Quick reply updated: $id');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating quick reply: $e');
      return false;
    }
  }

  /// حذف رد سريع
  Future<bool> deleteQuickReply(String id) async {
    try {
      final index = _quickReplies.indexWhere((reply) => reply.id == id);
      if (index == -1) return false;

      // منع حذف الردود الافتراضية
      if (_quickReplies[index].isDefault) {
        debugPrint('📝 Cannot delete default quick reply: $id');
        return false;
      }

      _quickReplies.removeAt(index);
      await _saveQuickReplies();
      notifyListeners();

      debugPrint('📝 Quick reply deleted: $id');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting quick reply: $e');
      return false;
    }
  }

  /// استخدام رد سريع
  Future<void> useQuickReply(String id) async {
    try {
      final index = _quickReplies.indexWhere((reply) => reply.id == id);
      if (index == -1) return;

      // زيادة عداد الاستخدام
      final oldReply = _quickReplies[index];
      final updatedReply = oldReply.copyWith(usageCount: oldReply.usageCount + 1);
      _quickReplies[index] = updatedReply;

      // إضافة للمستخدمة مؤخراً
      _recentlyUsed.remove(id);
      _recentlyUsed.insert(0, id);
      if (_recentlyUsed.length > 10) {
        _recentlyUsed.removeRange(10, _recentlyUsed.length);
      }

      await _saveQuickReplies();
      notifyListeners();

      debugPrint('📝 Quick reply used: $id');
    } catch (e) {
      debugPrint('❌ Error using quick reply: $e');
    }
  }

  /// الحصول على جميع الردود السريعة
  List<QuickReply> getAllQuickReplies() {
    return List.from(_quickReplies);
  }

  /// الحصول على الردود حسب الفئة
  List<QuickReply> getQuickRepliesByCategory(String category) {
    return _quickReplies.where((reply) => reply.category == category).toList();
  }

  /// الحصول على الردود الأكثر استخداماً
  List<QuickReply> getMostUsedReplies({int limit = 10}) {
    final sortedReplies = List<QuickReply>.from(_quickReplies);
    sortedReplies.sort((a, b) => b.usageCount.compareTo(a.usageCount));
    return sortedReplies.take(limit).toList();
  }

  /// الحصول على الردود المستخدمة مؤخراً
  List<QuickReply> getRecentlyUsedReplies() {
    final recentReplies = <QuickReply>[];
    for (final id in _recentlyUsed) {
      final reply = _quickReplies.firstWhere(
        (r) => r.id == id,
        orElse: () => QuickReply(
          id: '',
          title: '',
          content: '',
          category: '',
          createdAt: DateTime.now(),
        ),
      );
      if (reply.id.isNotEmpty) {
        recentReplies.add(reply);
      }
    }
    return recentReplies;
  }

  /// البحث في الردود السريعة
  List<QuickReply> searchQuickReplies(String query) {
    if (query.trim().isEmpty) return getAllQuickReplies();

    final lowerQuery = query.toLowerCase();
    return _quickReplies.where((reply) {
      return reply.title.toLowerCase().contains(lowerQuery) ||
             reply.content.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// الحصول على الفئات المتاحة
  List<String> getAvailableCategories() {
    final categories = _quickReplies.map((reply) => reply.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// الحصول على رد سريع بالمعرف
  QuickReply? getQuickReplyById(String id) {
    try {
      return _quickReplies.firstWhere((reply) => reply.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تحميل الردود السريعة
  Future<void> _loadQuickReplies() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final repliesJson = prefs.getString('quick_replies');
      final recentJson = prefs.getString('recent_replies');

      if (repliesJson != null) {
        final repliesList = json.decode(repliesJson) as List;
        _quickReplies.clear();
        for (final replyMap in repliesList) {
          _quickReplies.add(QuickReply.fromMap(replyMap));
        }
      }

      if (recentJson != null) {
        final recentList = json.decode(recentJson) as List;
        _recentlyUsed.clear();
        _recentlyUsed.addAll(recentList.cast<String>());
      }

      debugPrint('📝 Loaded ${_quickReplies.length} quick replies');
    } catch (e) {
      debugPrint('❌ Error loading quick replies: $e');
    }
  }

  /// حفظ الردود السريعة
  Future<void> _saveQuickReplies() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final repliesList = _quickReplies.map((reply) => reply.toMap()).toList();
      final repliesJson = json.encode(repliesList);
      await prefs.setString('quick_replies', repliesJson);

      final recentJson = json.encode(_recentlyUsed);
      await prefs.setString('recent_replies', recentJson);
    } catch (e) {
      debugPrint('❌ Error saving quick replies: $e');
    }
  }

  /// إحصائيات الردود السريعة
  Map<String, dynamic> getQuickRepliesStats() {
    final stats = <String, dynamic>{};

    stats['total'] = _quickReplies.length;
    stats['custom'] = _quickReplies.where((r) => !r.isDefault).length;
    stats['default'] = _quickReplies.where((r) => r.isDefault).length;

    // إحصائيات حسب الفئة
    final categoryStats = <String, int>{};
    for (final reply in _quickReplies) {
      categoryStats[reply.category] = (categoryStats[reply.category] ?? 0) + 1;
    }
    stats['byCategory'] = categoryStats;

    // الأكثر استخداماً
    final mostUsed = getMostUsedReplies(limit: 5);
    stats['mostUsed'] = mostUsed.map((r) => {
      'title': r.title,
      'usageCount': r.usageCount,
    }).toList();

    return stats;
  }

  // Getters
  int get totalReplies => _quickReplies.length;
  int get customReplies => _quickReplies.where((r) => !r.isDefault).length;
  int get defaultReplies => _quickReplies.where((r) => r.isDefault).length;
}

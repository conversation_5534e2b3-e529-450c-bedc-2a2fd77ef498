import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إعدادات الفيديو (السرعة والجودة)
class VideoSettingsService extends ChangeNotifier {
  double _playbackSpeed = 1.0;
  String _videoQuality = 'Auto';
  
  static const String _playbackSpeedKey = 'video_playback_speed';
  static const String _videoQualityKey = 'video_quality';

  /// الحصول على سرعة التشغيل الحالية
  double get playbackSpeed => _playbackSpeed;

  /// الحصول على جودة الفيديو الحالية
  String get videoQuality => _videoQuality;

  /// خيارات سرعة التشغيل المتاحة
  static const List<double> availableSpeeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  /// خيارات جودة الفيديو المتاحة
  static const List<String> availableQualities = ['Auto', '1080p', '720p', '480p', '360p', '240p'];

  /// تغيير سرعة التشغيل
  Future<void> setPlaybackSpeed(double speed) async {
    if (availableSpeeds.contains(speed)) {
      _playbackSpeed = speed;
      await _savePlaybackSpeed();
      notifyListeners();
    }
  }

  /// تغيير جودة الفيديو
  Future<void> setVideoQuality(String quality) async {
    if (availableQualities.contains(quality)) {
      _videoQuality = quality;
      await _saveVideoQuality();
      notifyListeners();
    }
  }

  /// إعادة تعيين الإعدادات للافتراضية
  Future<void> resetToDefaults() async {
    _playbackSpeed = 1.0;
    _videoQuality = 'Auto';
    await _savePlaybackSpeed();
    await _saveVideoQuality();
    notifyListeners();
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _playbackSpeed = prefs.getDouble(_playbackSpeedKey) ?? 1.0;
      _videoQuality = prefs.getString(_videoQualityKey) ?? 'Auto';
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading video settings: $e');
    }
  }

  /// حفظ سرعة التشغيل
  Future<void> _savePlaybackSpeed() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_playbackSpeedKey, _playbackSpeed);
    } catch (e) {
      debugPrint('Error saving playback speed: $e');
    }
  }

  /// حفظ جودة الفيديو
  Future<void> _saveVideoQuality() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_videoQualityKey, _videoQuality);
    } catch (e) {
      debugPrint('Error saving video quality: $e');
    }
  }

  /// الحصول على نص سرعة التشغيل
  String getSpeedText(double speed) {
    if (speed == 1.0) {
      return 'Normal';
    } else if (speed < 1.0) {
      return '${speed}x (Slow)';
    } else {
      return '${speed}x (Fast)';
    }
  }

  /// الحصول على أيقونة سرعة التشغيل
  String getSpeedIcon(double speed) {
    if (speed == 1.0) {
      return '▶️';
    } else if (speed < 1.0) {
      return '🐌';
    } else {
      return '⚡';
    }
  }

  /// الحصول على نص جودة الفيديو
  String getQualityDescription(String quality) {
    switch (quality) {
      case 'Auto':
        return 'Automatic quality based on connection';
      case '1080p':
        return 'Full HD (1920x1080)';
      case '720p':
        return 'HD (1280x720)';
      case '480p':
        return 'SD (854x480)';
      case '360p':
        return 'Low (640x360)';
      case '240p':
        return 'Very Low (426x240)';
      default:
        return 'Unknown quality';
    }
  }

  /// الحصول على أيقونة جودة الفيديو
  String getQualityIcon(String quality) {
    switch (quality) {
      case 'Auto':
        return '🔄';
      case '1080p':
        return '🎬';
      case '720p':
        return '📺';
      case '480p':
        return '📱';
      case '360p':
        return '📹';
      case '240p':
        return '📼';
      default:
        return '❓';
    }
  }

  /// التحقق من كون السرعة هي الافتراضية
  bool get isDefaultSpeed => _playbackSpeed == 1.0;

  /// التحقق من كون الجودة هي الافتراضية
  bool get isDefaultQuality => _videoQuality == 'Auto';

  /// التحقق من كون جميع الإعدادات افتراضية
  bool get isAllDefault => isDefaultSpeed && isDefaultQuality;

  /// الحصول على ملخص الإعدادات الحالية
  Map<String, dynamic> get currentSettings {
    return {
      'playbackSpeed': _playbackSpeed,
      'playbackSpeedText': getSpeedText(_playbackSpeed),
      'videoQuality': _videoQuality,
      'qualityDescription': getQualityDescription(_videoQuality),
      'isDefault': isAllDefault,
    };
  }

  /// تطبيق إعدادات مخصصة
  Future<void> applyCustomSettings({
    double? speed,
    String? quality,
  }) async {
    bool changed = false;

    if (speed != null && speed != _playbackSpeed && availableSpeeds.contains(speed)) {
      _playbackSpeed = speed;
      await _savePlaybackSpeed();
      changed = true;
    }

    if (quality != null && quality != _videoQuality && availableQualities.contains(quality)) {
      _videoQuality = quality;
      await _saveVideoQuality();
      changed = true;
    }

    if (changed) {
      notifyListeners();
    }
  }

  /// الحصول على إعدادات محفوظة للمشاركة
  Map<String, dynamic> exportSettings() {
    return {
      'playbackSpeed': _playbackSpeed,
      'videoQuality': _videoQuality,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// استيراد إعدادات من ملف
  Future<bool> importSettings(Map<String, dynamic> settings) async {
    try {
      final speed = settings['playbackSpeed'] as double?;
      final quality = settings['videoQuality'] as String?;

      if (speed != null && availableSpeeds.contains(speed)) {
        _playbackSpeed = speed;
        await _savePlaybackSpeed();
      }

      if (quality != null && availableQualities.contains(quality)) {
        _videoQuality = quality;
        await _saveVideoQuality();
      }

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error importing video settings: $e');
      return false;
    }
  }
}

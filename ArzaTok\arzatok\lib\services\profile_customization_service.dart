import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:convert';

/// خدمة تخصيص الملف الشخصي
class ProfileCustomizationService extends ChangeNotifier {
  static final ProfileCustomizationService _instance = ProfileCustomizationService._internal();
  factory ProfileCustomizationService() => _instance;
  ProfileCustomizationService._internal();

  final ImagePicker _picker = ImagePicker();
  
  // بيانات الصور والثيمات
  final Map<String, String> _profileImages = {};
  final Map<String, String> _coverImages = {};
  final Map<String, ProfileTheme> _userThemes = {};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadCustomizations();
  }

  /// رفع صورة الملف الشخصي
  Future<bool> uploadProfileImage(String userId) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 500,
        maxHeight: 500,
        imageQuality: 80,
      );

      if (image != null) {
        // في التطبيق الحقيقي، ارفع الصورة للخادم
        // هنا سنحفظ المسار محلياً
        _profileImages[userId] = image.path;
        await _saveCustomizations();
        notifyListeners();
        
        debugPrint('✅ Profile image uploaded for user: $userId');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ Error uploading profile image: $e');
      return false;
    }
  }

  /// رفع صورة الغلاف
  Future<bool> uploadCoverImage(String userId) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 600,
        imageQuality: 85,
      );

      if (image != null) {
        _coverImages[userId] = image.path;
        await _saveCustomizations();
        notifyListeners();
        
        debugPrint('✅ Cover image uploaded for user: $userId');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ Error uploading cover image: $e');
      return false;
    }
  }

  /// تحديث ثيم الملف الشخصي
  Future<bool> updateProfileTheme(String userId, ProfileTheme theme) async {
    try {
      _userThemes[userId] = theme;
      await _saveCustomizations();
      notifyListeners();
      
      debugPrint('✅ Profile theme updated for user: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating profile theme: $e');
      return false;
    }
  }

  /// الحصول على صورة الملف الشخصي
  String? getProfileImage(String userId) {
    return _profileImages[userId];
  }

  /// الحصول على صورة الغلاف
  String? getCoverImage(String userId) {
    return _coverImages[userId];
  }

  /// الحصول على ثيم المستخدم
  ProfileTheme getUserTheme(String userId) {
    return _userThemes[userId] ?? ProfileTheme.defaultTheme();
  }

  /// حذف صورة الملف الشخصي
  Future<bool> removeProfileImage(String userId) async {
    try {
      _profileImages.remove(userId);
      await _saveCustomizations();
      notifyListeners();
      
      debugPrint('✅ Profile image removed for user: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error removing profile image: $e');
      return false;
    }
  }

  /// حذف صورة الغلاف
  Future<bool> removeCoverImage(String userId) async {
    try {
      _coverImages.remove(userId);
      await _saveCustomizations();
      notifyListeners();
      
      debugPrint('✅ Cover image removed for user: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error removing cover image: $e');
      return false;
    }
  }

  /// الحصول على الثيمات المتاحة
  List<ProfileTheme> getAvailableThemes() {
    return [
      ProfileTheme.defaultTheme(),
      ProfileTheme.darkTheme(),
      ProfileTheme.blueTheme(),
      ProfileTheme.greenTheme(),
      ProfileTheme.purpleTheme(),
      ProfileTheme.orangeTheme(),
    ];
  }

  /// تحميل التخصيصات
  Future<void> _loadCustomizations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل صور الملف الشخصي
      final profileImagesJson = prefs.getString('profile_images') ?? '{}';
      final profileImagesMap = json.decode(profileImagesJson) as Map<String, dynamic>;
      _profileImages.clear();
      profileImagesMap.forEach((key, value) {
        _profileImages[key] = value.toString();
      });
      
      // تحميل صور الغلاف
      final coverImagesJson = prefs.getString('cover_images') ?? '{}';
      final coverImagesMap = json.decode(coverImagesJson) as Map<String, dynamic>;
      _coverImages.clear();
      coverImagesMap.forEach((key, value) {
        _coverImages[key] = value.toString();
      });
      
      // تحميل الثيمات
      final themesJson = prefs.getString('user_themes') ?? '{}';
      final themesMap = json.decode(themesJson) as Map<String, dynamic>;
      _userThemes.clear();
      themesMap.forEach((key, value) {
        _userThemes[key] = ProfileTheme.fromMap(value);
      });
      
      debugPrint('📊 Loaded profile customizations');
    } catch (e) {
      debugPrint('❌ Error loading profile customizations: $e');
    }
  }

  /// حفظ التخصيصات
  Future<void> _saveCustomizations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ صور الملف الشخصي
      await prefs.setString('profile_images', json.encode(_profileImages));
      
      // حفظ صور الغلاف
      await prefs.setString('cover_images', json.encode(_coverImages));
      
      // حفظ الثيمات
      final themesMap = <String, Map<String, dynamic>>{};
      _userThemes.forEach((key, value) {
        themesMap[key] = value.toMap();
      });
      await prefs.setString('user_themes', json.encode(themesMap));
      
    } catch (e) {
      debugPrint('❌ Error saving profile customizations: $e');
    }
  }

  // Getters
  Map<String, String> get allProfileImages => Map.from(_profileImages);
  Map<String, String> get allCoverImages => Map.from(_coverImages);
  Map<String, ProfileTheme> get allUserThemes => Map.from(_userThemes);
}

/// نموذج ثيم الملف الشخصي
class ProfileTheme {
  final String name;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final Gradient? backgroundGradient;

  ProfileTheme({
    required this.name,
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
    required this.textColor,
    this.backgroundGradient,
  });

  /// الثيم الافتراضي (أحمر)
  factory ProfileTheme.defaultTheme() {
    return ProfileTheme(
      name: 'Default Red',
      primaryColor: const Color(0xFFD32F2F),
      secondaryColor: const Color(0xFFB71C1C),
      backgroundColor: Colors.white,
      textColor: Colors.black,
      backgroundGradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFD32F2F), Color(0xFFB71C1C)],
      ),
    );
  }

  /// الثيم الداكن
  factory ProfileTheme.darkTheme() {
    return ProfileTheme(
      name: 'Dark',
      primaryColor: const Color(0xFF212121),
      secondaryColor: const Color(0xFF424242),
      backgroundColor: const Color(0xFF121212),
      textColor: Colors.white,
      backgroundGradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF212121), Color(0xFF424242)],
      ),
    );
  }

  /// الثيم الأزرق
  factory ProfileTheme.blueTheme() {
    return ProfileTheme(
      name: 'Ocean Blue',
      primaryColor: const Color(0xFF1976D2),
      secondaryColor: const Color(0xFF1565C0),
      backgroundColor: Colors.white,
      textColor: Colors.black,
      backgroundGradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
      ),
    );
  }

  /// الثيم الأخضر
  factory ProfileTheme.greenTheme() {
    return ProfileTheme(
      name: 'Nature Green',
      primaryColor: const Color(0xFF388E3C),
      secondaryColor: const Color(0xFF2E7D32),
      backgroundColor: Colors.white,
      textColor: Colors.black,
      backgroundGradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF388E3C), Color(0xFF2E7D32)],
      ),
    );
  }

  /// الثيم البنفسجي
  factory ProfileTheme.purpleTheme() {
    return ProfileTheme(
      name: 'Royal Purple',
      primaryColor: const Color(0xFF7B1FA2),
      secondaryColor: const Color(0xFF6A1B9A),
      backgroundColor: Colors.white,
      textColor: Colors.black,
      backgroundGradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF7B1FA2), Color(0xFF6A1B9A)],
      ),
    );
  }

  /// الثيم البرتقالي
  factory ProfileTheme.orangeTheme() {
    return ProfileTheme(
      name: 'Sunset Orange',
      primaryColor: const Color(0xFFFF6F00),
      secondaryColor: const Color(0xFFE65100),
      backgroundColor: Colors.white,
      textColor: Colors.black,
      backgroundGradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFFF6F00), Color(0xFFE65100)],
      ),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'primaryColor': primaryColor.value,
      'secondaryColor': secondaryColor.value,
      'backgroundColor': backgroundColor.value,
      'textColor': textColor.value,
    };
  }

  /// إنشاء من Map
  factory ProfileTheme.fromMap(Map<String, dynamic> map) {
    return ProfileTheme(
      name: map['name'] ?? 'Custom',
      primaryColor: Color(map['primaryColor'] ?? 0xFFD32F2F),
      secondaryColor: Color(map['secondaryColor'] ?? 0xFFB71C1C),
      backgroundColor: Color(map['backgroundColor'] ?? 0xFFFFFFFF),
      textColor: Color(map['textColor'] ?? 0xFF000000),
    );
  }
}

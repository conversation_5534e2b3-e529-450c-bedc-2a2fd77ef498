import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../../models/user_model.dart';
import '../../services/call_service.dart';
import '../../services/ringtone_service.dart';

class CallScreen extends StatefulWidget {
  final UserModel otherUser;
  final CallType callType;
  final bool isIncoming;

  const CallScreen({
    super.key,
    required this.otherUser,
    required this.callType,
    this.isIncoming = false,
  });

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  CallStatus _callStatus = CallStatus.calling;
  Timer? _callTimer;
  int _callDuration = 0;
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isVideoOn = true;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _initializeCall();
  }

  void _initializeCall() async {
    final callService = Provider.of<CallService>(context, listen: false);
    final ringtoneService = Provider.of<RingtoneService>(context, listen: false);

    if (widget.isIncoming) {
      // Play incoming call ringtone
      await ringtoneService.playCallRingtone();
      _pulseController.repeat(reverse: true);
    } else {
      // Start outgoing call
      setState(() {
        _callStatus = CallStatus.ringing;
      });
      _pulseController.repeat(reverse: true);

      // Simulate call connection after 3-5 seconds
      Timer(Duration(seconds: 3 + (DateTime.now().millisecond % 3)), () {
        if (mounted) {
          _acceptCall();
        }
      });
    }
  }

  void _acceptCall() async {
    final ringtoneService = Provider.of<RingtoneService>(context, listen: false);
    await ringtoneService.stopRingtone();

    setState(() {
      _callStatus = CallStatus.connected;
    });

    _pulseController.stop();
    _startCallTimer();
  }

  void _startCallTimer() {
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _callDuration++;
        });
      }
    });
  }

  void _endCall() async {
    final callService = Provider.of<CallService>(context, listen: false);
    final ringtoneService = Provider.of<RingtoneService>(context, listen: false);

    await ringtoneService.stopRingtone();
    _callTimer?.cancel();
    _pulseController.stop();

    // Save call to history
    await callService.endCall();

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
  }

  void _toggleSpeaker() {
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
    });
  }

  void _toggleVideo() {
    if (widget.callType == CallType.video) {
      setState(() {
        _isVideoOn = !_isVideoOn;
      });
    }
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _callTimer?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.callType == CallType.video
          ? Colors.black
          : const Color(0xFF1a1a1a),
      body: SafeArea(
        child: Column(
          children: [
            // Top section with user info
            Expanded(
              flex: 2,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Call status
                    Text(
                      _getStatusText(),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Call duration (if connected)
                    if (_callStatus == CallStatus.connected)
                      Text(
                        _formatDuration(_callDuration),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),

                    const SizedBox(height: 32),

                    // User avatar
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _callStatus == CallStatus.ringing || _callStatus == CallStatus.calling
                              ? _pulseAnimation.value
                              : 1.0,
                          child: CircleAvatar(
                            radius: 80,
                            backgroundColor: const Color(0xFFD32F2F),
                            backgroundImage: widget.otherUser.profileImageUrl != null
                                ? NetworkImage(widget.otherUser.profileImageUrl!)
                                : null,
                            child: widget.otherUser.profileImageUrl == null
                                ? Text(
                                    widget.otherUser.name.isNotEmpty
                                        ? widget.otherUser.name[0].toUpperCase()
                                        : '?',
                                    style: const TextStyle(
                                      fontSize: 50,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : null,
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 24),

                    // User name
                    Text(
                      widget.otherUser.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // Phone number
                    Text(
                      widget.otherUser.phoneNumber,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Video preview (for video calls)
            if (widget.callType == CallType.video && _isVideoOn)
              Expanded(
                flex: 1,
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(
                    child: Text(
                      'Video Preview\n(Camera simulation)',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),

            // Control buttons
            Container(
              padding: const EdgeInsets.all(24),
              child: _buildControlButtons(),
            ),
          ],
        ),
      ),
    );
  }

  String _getStatusText() {
    switch (_callStatus) {
      case CallStatus.calling:
        return 'Calling...';
      case CallStatus.ringing:
        return widget.isIncoming ? 'Incoming call' : 'Ringing...';
      case CallStatus.connected:
        return widget.callType == CallType.video ? 'Video call' : 'Voice call';
      case CallStatus.ended:
        return 'Call ended';
      case CallStatus.declined:
        return 'Call declined';
      case CallStatus.missed:
        return 'Call missed';
    }
  }

  Widget _buildControlButtons() {
    if (widget.isIncoming && _callStatus != CallStatus.connected) {
      // Incoming call buttons
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Decline button
          FloatingActionButton(
            onPressed: _endCall,
            backgroundColor: Colors.red,
            heroTag: "decline",
            child: const Icon(Icons.call_end, color: Colors.white),
          ),

          // Accept button
          FloatingActionButton(
            onPressed: _acceptCall,
            backgroundColor: Colors.green,
            heroTag: "accept",
            child: const Icon(Icons.call, color: Colors.white),
          ),
        ],
      );
    } else {
      // Active call buttons
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Mute button
          FloatingActionButton(
            onPressed: _toggleMute,
            backgroundColor: _isMuted ? Colors.red : Colors.grey[700],
            heroTag: "mute",
            child: Icon(
              _isMuted ? Icons.mic_off : Icons.mic,
              color: Colors.white,
            ),
          ),

          // Speaker button (voice call only)
          if (widget.callType == CallType.voice)
            FloatingActionButton(
              onPressed: _toggleSpeaker,
              backgroundColor: _isSpeakerOn ? const Color(0xFFD32F2F) : Colors.grey[700],
              heroTag: "speaker",
              child: Icon(
                _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                color: Colors.white,
              ),
            ),

          // Video toggle (video call only)
          if (widget.callType == CallType.video)
            FloatingActionButton(
              onPressed: _toggleVideo,
              backgroundColor: _isVideoOn ? const Color(0xFFD32F2F) : Colors.grey[700],
              heroTag: "video",
              child: Icon(
                _isVideoOn ? Icons.videocam : Icons.videocam_off,
                color: Colors.white,
              ),
            ),

          // End call button
          FloatingActionButton(
            onPressed: _endCall,
            backgroundColor: Colors.red,
            heroTag: "end_call",
            child: const Icon(Icons.call_end, color: Colors.white),
          ),
        ],
      );
    }
  }
}

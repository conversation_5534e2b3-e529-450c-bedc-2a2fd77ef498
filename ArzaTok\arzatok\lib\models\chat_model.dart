// import 'package:cloud_firestore/cloud_firestore.dart';
import 'message_model.dart';
import 'user_model.dart';

class ChatModel {
  final String chatId;
  final List<String> participants;
  final MessageModel? lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;
  final bool isArchived;
  final bool isMuted;
  final UserModel? otherUser; // المستخدم الآخر في المحادثة

  ChatModel({
    required this.chatId,
    required this.participants,
    this.lastMessage,
    required this.lastMessageTime,
    this.unreadCount = 0,
    this.isArchived = false,
    this.isMuted = false,
    this.otherUser,
  });

  // Getter for id (same as chatId for compatibility)
  String get id => chatId;

  // تحويل من Map إلى ChatModel
  factory ChatModel.fromMap(Map<String, dynamic> map, String documentId) {
    return ChatModel(
      chatId: documentId,
      participants: List<String>.from(map['participants'] ?? []),
      lastMessage: map['lastMessage'] != null
          ? MessageModel.fromMap(map['lastMessage'], '')
          : null,
      lastMessageTime: DateTime.parse(map['lastMessageTime'] ?? DateTime.now().toIso8601String()),
      unreadCount: map['unreadCount'] ?? 0,
      isArchived: map['isArchived'] ?? false,
      isMuted: map['isMuted'] ?? false,
    );
  }

  // تحويل من ChatModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'participants': participants,
      'lastMessage': lastMessage?.toMap(),
      'lastMessageTime': lastMessageTime.toIso8601String(),
      'unreadCount': unreadCount,
      'isArchived': isArchived,
      'isMuted': isMuted,
    };
  }

  // نسخ ChatModel مع تعديل بعض الخصائص
  ChatModel copyWith({
    String? chatId,
    List<String>? participants,
    MessageModel? lastMessage,
    DateTime? lastMessageTime,
    int? unreadCount,
    bool? isArchived,
    bool? isMuted,
    UserModel? otherUser,
  }) {
    return ChatModel(
      chatId: chatId ?? this.chatId,
      participants: participants ?? this.participants,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
      isArchived: isArchived ?? this.isArchived,
      isMuted: isMuted ?? this.isMuted,
      otherUser: otherUser ?? this.otherUser,
    );
  }

  // إنشاء معرف المحادثة من معرفي المشاركين
  static String generateChatId(String userId1, String userId2) {
    final List<String> ids = [userId1, userId2];
    ids.sort();
    return ids.join('_');
  }

  // الحصول على معرف المستخدم الآخر
  String getOtherUserId(String currentUserId) {
    return participants.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
  }

  @override
  String toString() {
    return 'ChatModel(chatId: $chatId, participants: $participants, lastMessage: $lastMessage, lastMessageTime: $lastMessageTime, unreadCount: $unreadCount, otherUser: $otherUser)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatModel && other.chatId == chatId;
  }

  @override
  int get hashCode {
    return chatId.hashCode;
  }
}

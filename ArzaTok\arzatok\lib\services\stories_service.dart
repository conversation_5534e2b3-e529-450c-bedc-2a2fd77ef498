import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../models/story_model.dart';
import 'media_service.dart';

/// خدمة القصص الحقيقية
class StoriesService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final MediaService _mediaService = MediaService();

  List<UserStoriesModel> _userStories = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<UserStoriesModel> get userStories => _userStories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// تحميل جميع القصص
  Future<void> loadStories() async {
    _setLoading(true);
    try {
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(hours: 24));

      // جلب القصص من آخر 24 ساعة
      final snapshot = await _firestore
          .collection('stories')
          .where('createdAt', isGreaterThan: Timestamp.fromDate(yesterday))
          .orderBy('createdAt', descending: true)
          .get();

      // تجميع القصص حسب المستخدم
      final Map<String, List<StoryModel>> storiesByUser = {};

      for (final doc in snapshot.docs) {
        final story = StoryModel.fromMap(doc.data());
        if (!story.isExpired) {
          if (!storiesByUser.containsKey(story.userId)) {
            storiesByUser[story.userId] = [];
          }
          storiesByUser[story.userId]!.add(story);
        }
      }

      // تحويل إلى UserStoriesModel
      _userStories = storiesByUser.entries.map((entry) {
        final stories = entry.value;
        stories.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        return UserStoriesModel(
          userId: entry.key,
          userName: stories.first.userName,
          userProfileImage: stories.first.userProfileImage,
          stories: stories,
          hasUnviewedStories: stories.any((s) => !s.viewedBy.contains(entry.key)),
        );
      }).toList();

      // ترتيب حسب آخر قصة
      _userStories.sort((a, b) {
        final aLatest = a.latestStory?.createdAt ?? DateTime(1970);
        final bLatest = b.latestStory?.createdAt ?? DateTime(1970);
        return bLatest.compareTo(aLatest);
      });

      _error = null;
    } catch (e) {
      _error = 'Failed to load stories: $e';
      debugPrint('❌ Error loading stories: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء قصة نصية
  Future<bool> createTextStory({
    required String userId,
    required String userName,
    String? userProfileImage,
    required String content,
    Color? backgroundColor,
    Color? textColor,
    String? fontFamily,
  }) async {
    try {
      final storyId = _firestore.collection('stories').doc().id;

      final story = StoryModel.text(
        id: storyId,
        userId: userId,
        userName: userName,
        userProfileImage: userProfileImage,
        content: content,
        backgroundColor: backgroundColor,
        textColor: textColor,
        fontFamily: fontFamily,
      );

      await _firestore
          .collection('stories')
          .doc(storyId)
          .set(story.toMap());

      await loadStories();
      return true;
    } catch (e) {
      _error = 'Failed to create text story: $e';
      debugPrint('❌ Error creating text story: $e');
      return false;
    }
  }

  /// إنشاء قصة صورة
  Future<bool> createImageStory({
    required String userId,
    required String userName,
    String? userProfileImage,
    required File imageFile,
  }) async {
    try {
      final storyId = _firestore.collection('stories').doc().id;

      // رفع الصورة إلى Firebase Storage
      final imageUrl = await _uploadStoryMedia(
        file: imageFile,
        storyId: storyId,
        mediaType: 'image',
      );

      if (imageUrl == null) {
        _error = 'Failed to upload image';
        return false;
      }

      final story = StoryModel.image(
        id: storyId,
        userId: userId,
        userName: userName,
        userProfileImage: userProfileImage,
        imageUrl: imageUrl,
      );

      await _firestore
          .collection('stories')
          .doc(storyId)
          .set(story.toMap());

      await loadStories();
      return true;
    } catch (e) {
      _error = 'Failed to create image story: $e';
      debugPrint('❌ Error creating image story: $e');
      return false;
    }
  }

  /// إنشاء قصة فيديو
  Future<bool> createVideoStory({
    required String userId,
    required String userName,
    String? userProfileImage,
    required File videoFile,
  }) async {
    try {
      final storyId = _firestore.collection('stories').doc().id;

      // رفع الفيديو إلى Firebase Storage
      final videoUrl = await _uploadStoryMedia(
        file: videoFile,
        storyId: storyId,
        mediaType: 'video',
      );

      if (videoUrl == null) {
        _error = 'Failed to upload video';
        return false;
      }

      // إنشاء thumbnail للفيديو
      final thumbnailFile = await _mediaService.generateVideoThumbnail(videoFile.path);
      String? thumbnailUrl;

      if (thumbnailFile != null) {
        thumbnailUrl = await _uploadStoryMedia(
          file: thumbnailFile,
          storyId: storyId,
          mediaType: 'thumbnail',
        );
      }

      final story = StoryModel.video(
        id: storyId,
        userId: userId,
        userName: userName,
        userProfileImage: userProfileImage,
        videoUrl: videoUrl,
        thumbnailUrl: thumbnailUrl,
      );

      await _firestore
          .collection('stories')
          .doc(storyId)
          .set(story.toMap());

      await loadStories();
      return true;
    } catch (e) {
      _error = 'Failed to create video story: $e';
      debugPrint('❌ Error creating video story: $e');
      return false;
    }
  }

  /// رفع وسائط القصة
  Future<String?> _uploadStoryMedia({
    required File file,
    required String storyId,
    required String mediaType,
  }) async {
    try {
      final fileName = '${storyId}_$mediaType.${file.path.split('.').last}';
      final ref = _storage.ref().child('stories').child(fileName);

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      debugPrint('❌ Error uploading story media: $e');
      return null;
    }
  }

  /// تسجيل مشاهدة القصة
  Future<void> markStoryAsViewed(String storyId, String viewerId) async {
    try {
      await _firestore.collection('stories').doc(storyId).update({
        'viewedBy': FieldValue.arrayUnion([viewerId]),
      });

      // تحديث القائمة المحلية
      for (int i = 0; i < _userStories.length; i++) {
        final userStories = _userStories[i];
        for (int j = 0; j < userStories.stories.length; j++) {
          if (userStories.stories[j].id == storyId) {
            final updatedStory = userStories.stories[j].copyWith(
              viewedBy: [...userStories.stories[j].viewedBy, viewerId],
              isViewed: true,
            );

            final updatedStories = [...userStories.stories];
            updatedStories[j] = updatedStory;

            _userStories[i] = userStories.copyWith(stories: updatedStories);
            notifyListeners();
            return;
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error marking story as viewed: $e');
    }
  }

  /// حذف القصة
  Future<bool> deleteStory(String storyId) async {
    try {
      await _firestore.collection('stories').doc(storyId).delete();
      await loadStories();
      return true;
    } catch (e) {
      _error = 'Failed to delete story: $e';
      debugPrint('❌ Error deleting story: $e');
      return false;
    }
  }

  /// الحصول على قصص مستخدم معين
  List<StoryModel> getUserStories(String userId) {
    try {
      final userStories = _userStories.firstWhere(
        (stories) => stories.userId == userId,
      );
      return userStories.stories;
    } catch (e) {
      return [];
    }
  }

  /// تنظيف القصص المنتهية الصلاحية
  Future<void> cleanupExpiredStories() async {
    try {
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(hours: 24));

      final snapshot = await _firestore
          .collection('stories')
          .where('expiresAt', isLessThan: Timestamp.fromDate(now))
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      await loadStories();
    } catch (e) {
      debugPrint('❌ Error cleaning up expired stories: $e');
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }
}

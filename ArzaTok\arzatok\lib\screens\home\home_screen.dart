import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../services/group_service.dart';
import '../../services/contact_service.dart';
import '../auth/phone_auth_screen.dart';
import 'menu_screen.dart';
import 'chats_tab.dart';
import 'groups_tab.dart';
import 'contacts_tab.dart';
import 'stories_tab.dart';
import '../social/social_feed_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    WidgetsBinding.instance.addObserver(this);

    // Update user online status
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authService = Provider.of<AuthService>(context, listen: false);
      authService.updateUserOnlineStatus(true);

      // Initialize demo data - DISABLED for Firebase
      // GroupService.addDemoGroups();
      // ContactService.addDemoContacts();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final authService = Provider.of<AuthService>(context, listen: false);
    switch (state) {
      case AppLifecycleState.resumed:
        authService.updateUserOnlineStatus(true);
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        authService.updateUserOnlineStatus(false);
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }





  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Search chats, groups, contacts...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => GroupService()),
        ChangeNotifierProvider(create: (_) => ContactService()),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, child) {
          final currentUser = authService.currentUser;
          if (currentUser == null) {
            return const PhoneAuthScreen();
          }

          return Scaffold(
            appBar: AppBar(
              title: const Text(
                'ArzaTok',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                  color: Color(0xFFD32F2F), // أحمر
                ),
              ),
              backgroundColor: Colors.white,
              elevation: 1,
              shadowColor: Colors.black.withValues(alpha: 0.1),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(60),
                child: Container(
                  color: Colors.white,
                  child: TabBar(
                    controller: _tabController,
                    indicatorColor: const Color(0xFF1877F2), // Facebook blue
                    indicatorWeight: 3,
                    labelColor: const Color(0xFF1877F2),
                    unselectedLabelColor: const Color(0xFF65676B), // Facebook gray
                    tabs: [
                      // Chat - مثل Facebook Messenger
                      Tab(
                        icon: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.chat_bubble,
                            size: 24,
                          ),
                        ),
                      ),
                      // Stories - مثل Facebook Stories
                      Tab(
                        icon: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.auto_awesome_motion,
                            size: 24,
                          ),
                        ),
                      ),
                      // Groups - مثل Facebook Groups
                      Tab(
                        icon: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.groups,
                            size: 24,
                          ),
                        ),
                      ),
                      // Contacts - مثل Facebook Friends
                      Tab(
                        icon: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.people,
                            size: 24,
                          ),
                        ),
                      ),
                      // Social - مثل Facebook Feed
                      Tab(
                        icon: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.dynamic_feed,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                // Search button with Facebook style
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF0F2F5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.search,
                        color: Color(0xFF65676B),
                        size: 20,
                      ),
                    ),
                    onPressed: _showSearchDialog,
                  ),
                ),
                // Menu button with Facebook style - Navigate to Menu Screen
                Container(
                  margin: const EdgeInsets.only(right: 12),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const MenuScreen(),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF0F2F5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.menu,
                        color: Color(0xFF65676B),
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            body: Container(
              margin: EdgeInsets.zero, // إزالة أي مساحة خارجية
              padding: EdgeInsets.zero, // إزالة أي مساحة داخلية
              child: TabBarView(
                controller: _tabController,
                children: const [
                  ChatsTab(),
                  StoriesTab(),
                  GroupsTab(),
                  ContactsTab(),
                  SocialFeedScreen(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
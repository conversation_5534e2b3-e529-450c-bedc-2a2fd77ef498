import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/ai_service.dart';
import '../../services/app_lock_service.dart';
import '../../services/advanced_theme_service.dart';
import '../../services/usage_stats_service.dart';
import '../../services/quick_replies_service.dart';
import 'ai_settings_screen.dart';
import 'app_lock_settings_screen.dart';
import 'theme_settings_screen.dart';
import 'usage_stats_screen.dart';
import 'quick_replies_screen.dart';

/// شاشة الإعدادات المتقدمة
class AdvancedSettingsScreen extends StatefulWidget {
  const AdvancedSettingsScreen({super.key});

  @override
  State<AdvancedSettingsScreen> createState() => _AdvancedSettingsScreenState();
}

class _AdvancedSettingsScreenState extends State<AdvancedSettingsScreen> {
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// تهيئة الخدمات
  void _initializeServices() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appLockService = Provider.of<AppLockService>(context, listen: false);
      final themeService = Provider.of<AdvancedThemeService>(context, listen: false);
      final usageService = Provider.of<UsageStatsService>(context, listen: false);
      final quickRepliesService = Provider.of<QuickRepliesService>(context, listen: false);

      // تهيئة الخدمات
      appLockService.initialize();
      themeService.loadTheme();
      usageService.initialize();
      quickRepliesService.initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Advanced Settings',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFFD32F2F),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // قسم الذكاء الاصطناعي
          _buildSectionHeader('🧠 Artificial Intelligence'),
          _buildSettingCard(
            icon: Icons.psychology,
            title: 'AI Features',
            subtitle: 'Smart replies, translation, emotion analysis',
            onTap: () => _navigateToScreen(const AISettingsScreen()),
            trailing: Consumer<AIService>(
              builder: (context, aiService, child) {
                final stats = aiService.getAIStats();
                return Text(
                  '${stats['features'].length} features',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // قسم الردود السريعة
          _buildSectionHeader('💬 Quick Features'),
          _buildSettingCard(
            icon: Icons.reply,
            title: 'Quick Replies',
            subtitle: 'Manage your quick response templates',
            onTap: () => _navigateToScreen(const QuickRepliesScreen()),
            trailing: Consumer<QuickRepliesService>(
              builder: (context, quickRepliesService, child) {
                return Text(
                  '${quickRepliesService.totalReplies} replies',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // قسم الأمان والخصوصية
          _buildSectionHeader('🔐 Security & Privacy'),
          _buildSettingCard(
            icon: Icons.lock,
            title: 'App Lock',
            subtitle: 'PIN, password, fingerprint, face unlock',
            onTap: () => _navigateToScreen(const AppLockSettingsScreen()),
            trailing: Consumer<AppLockService>(
              builder: (context, appLockService, child) {
                return Icon(
                  appLockService.lockType != LockType.none
                      ? Icons.lock
                      : Icons.lock_open,
                  color: appLockService.lockType != LockType.none
                      ? Colors.green
                      : Colors.grey,
                  size: 20,
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // قسم التخصيص
          _buildSectionHeader('🎨 Customization'),
          _buildSettingCard(
            icon: Icons.palette,
            title: 'Advanced Themes',
            subtitle: 'Colors, fonts, and appearance',
            onTap: () => _navigateToScreen(const ThemeSettingsScreen()),
            trailing: Consumer<AdvancedThemeService>(
              builder: (context, themeService, child) {
                return Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: themeService.getPrimaryColor(),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // قسم الإحصائيات
          _buildSectionHeader('📊 Analytics'),
          _buildSettingCard(
            icon: Icons.analytics,
            title: 'Usage Statistics',
            subtitle: 'Track your app usage and activity',
            onTap: () => _navigateToScreen(const UsageStatsScreen()),
            trailing: Consumer<UsageStatsService>(
              builder: (context, usageService, child) {
                final todayStats = usageService.getTodaysStats();
                final totalMessages = todayStats.messagesSent + todayStats.messagesReceived;
                return Text(
                  '$totalMessages today',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // معلومات الميزات
          _buildInfoCard(),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Color(0xFFD32F2F),
        ),
      ),
    );
  }

  /// بناء بطاقة إعداد
  Widget _buildSettingCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFFD32F2F).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFD32F2F),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        trailing: trailing ?? const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
      ),
    );
  }

  /// بناء بطاقة المعلومات
  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: const Color(0xFFD32F2F).withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Advanced Features',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFD32F2F),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'These advanced features enhance your ArzaTalk experience with AI-powered capabilities, improved security, and detailed analytics.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildFeatureChip('🧠 AI Powered'),
                _buildFeatureChip('🔍 Smart Search'),
                _buildFeatureChip('🔐 Secure'),
                _buildFeatureChip('📊 Analytics'),
                _buildFeatureChip('🎨 Customizable'),
                _buildFeatureChip('⚡ Fast'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رقاقة الميزة
  Widget _buildFeatureChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFFD32F2F).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFD32F2F).withOpacity(0.3),
        ),
      ),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFFD32F2F),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// التنقل إلى شاشة
  void _navigateToScreen(Widget screen) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => screen),
    );
  }
}

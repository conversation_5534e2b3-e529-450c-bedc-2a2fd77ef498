import 'package:flutter/material.dart';
import '../models/post_model.dart';
import 'premium_ui_enhancements.dart';

/// ويدجت إحصائيات المنشور مع العدادات المحسنة
class PostStatsWidget extends StatelessWidget {
  final PostModel post;
  final VoidCallback? onReactionsPressed;
  final VoidCallback? onCommentsPressed;
  final VoidCallback? onSharesPressed;
  final VoidCallback? onRepostsPressed;

  const PostStatsWidget({
    Key? key,
    required this.post,
    this.onReactionsPressed,
    this.onCommentsPressed,
    this.onSharesPressed,
    this.onRepostsPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // إخفاء الإحصائيات إذا كانت كلها صفر
    if (post.totalReactions == 0 && 
        post.commentsCount == 0 && 
        post.sharesCount == 0 && 
        post.repostsCount == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // إحصائيات التفاعلات
          if (post.totalReactions > 0) ...[
            PremiumUIEnhancements.premiumTapEffect(
              onTap: onReactionsPressed ?? () {},
              child: _buildReactionsStats(),
            ),
            const Spacer(),
          ],
          
          // إحصائيات التعليقات والمشاركات وإعادة النشر
          Row(
            children: [
              // عدد التعليقات
              if (post.commentsCount > 0)
                PremiumUIEnhancements.premiumTapEffect(
                  onTap: onCommentsPressed ?? () {},
                  child: _buildStatItem(
                    count: post.commentsCount,
                    label: 'comment',
                    color: Colors.grey[600]!,
                  ),
                ),
              
              // فاصل
              if (post.commentsCount > 0 && (post.repostsCount > 0 || post.sharesCount > 0))
                _buildDivider(),
              
              // عدد إعادات النشر
              if (post.repostsCount > 0)
                PremiumUIEnhancements.premiumTapEffect(
                  onTap: onRepostsPressed ?? () {},
                  child: _buildStatItem(
                    count: post.repostsCount,
                    label: 'repost',
                    color: Colors.grey[600]!,
                  ),
                ),
              
              // فاصل
              if (post.repostsCount > 0 && post.sharesCount > 0)
                _buildDivider(),
              
              // عدد المشاركات
              if (post.sharesCount > 0)
                PremiumUIEnhancements.premiumTapEffect(
                  onTap: onSharesPressed ?? () {},
                  child: _buildStatItem(
                    count: post.sharesCount,
                    label: 'share',
                    color: Colors.grey[600]!,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء إحصائيات التفاعلات مع الملصقات
  Widget _buildReactionsStats() {
    final topReactions = post.topReactions.take(3).toList();
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // ملصقات التفاعلات المكدسة
        SizedBox(
          width: topReactions.length * 12.0 + 8,
          height: 20,
          child: Stack(
            children: topReactions.asMap().entries.map((entry) {
              final index = entry.key;
              final emoji = entry.value;
              return Positioned(
                left: index * 12.0,
                child: _buildReactionSticker(emoji),
              );
            }).toList(),
          ),
        ),
        
        const SizedBox(width: 6),
        
        // العدد الإجمالي
        Text(
          _formatCount(post.totalReactions),
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF65676B),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// بناء ملصق تفاعل واحد
  Widget _buildReactionSticker(String emoji) {
    Color stickerColor;
    IconData stickerIcon;

    switch (emoji) {
      case '👍':
        stickerColor = const Color(0xFF1877F2);
        stickerIcon = Icons.thumb_up;
        break;
      case '❤️':
        stickerColor = const Color(0xFFE91E63);
        stickerIcon = Icons.favorite;
        break;
      case '😂':
        stickerColor = const Color(0xFFFFC107);
        stickerIcon = Icons.sentiment_very_satisfied;
        break;
      case '😮':
        stickerColor = const Color(0xFFFF9800);
        stickerIcon = Icons.sentiment_very_satisfied;
        break;
      case '😢':
        stickerColor = const Color(0xFF607D8B);
        stickerIcon = Icons.sentiment_very_dissatisfied;
        break;
      case '😡':
        stickerColor = const Color(0xFFFF5722);
        stickerIcon = Icons.sentiment_very_dissatisfied;
        break;
      default:
        stickerColor = const Color(0xFF1877F2);
        stickerIcon = Icons.thumb_up;
    }

    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: stickerColor,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: stickerColor.withOpacity(0.3),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(
        stickerIcon,
        size: 11,
        color: Colors.white,
      ),
    );
  }

  /// بناء عنصر إحصائية واحد
  Widget _buildStatItem({
    required int count,
    required String label,
    required Color color,
  }) {
    return Text(
      '${_formatCount(count)} ${_getLabel(count, label)}',
      style: TextStyle(
        fontSize: 11,
        color: color,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// بناء فاصل بين الإحصائيات
  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6),
      width: 2,
      height: 2,
      decoration: const BoxDecoration(
        color: Color(0xFF65676B),
        shape: BoxShape.circle,
      ),
    );
  }

  /// تنسيق العدد (1K, 1M, etc.)
  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      final k = count / 1000;
      return k % 1 == 0 ? '${k.toInt()}K' : '${k.toStringAsFixed(1)}K';
    } else {
      final m = count / 1000000;
      return m % 1 == 0 ? '${m.toInt()}M' : '${m.toStringAsFixed(1)}M';
    }
  }

  /// الحصول على التسمية الصحيحة (مفرد/جمع)
  String _getLabel(int count, String label) {
    if (count == 1) {
      return label;
    } else {
      switch (label) {
        case 'comment':
          return 'comments';
        case 'repost':
          return 'reposts';
        case 'share':
          return 'shares';
        default:
          return '${label}s';
      }
    }
  }
}

/// ويدجت أزرار التفاعل مع العدادات
class PostActionButtonsWidget extends StatelessWidget {
  final PostModel post;
  final Widget reactionButton;
  final VoidCallback onComment;
  final VoidCallback onRepost;
  final VoidCallback onShare;

  const PostActionButtonsWidget({
    Key? key,
    required this.post,
    required this.reactionButton,
    required this.onComment,
    required this.onRepost,
    required this.onShare,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // زر التفاعل
          Expanded(child: reactionButton),

          // زر التعليق مع العداد
          Expanded(
            child: _buildActionButtonWithCount(
              icon: Icons.chat_bubble_outline,
              label: 'Comment',
              count: post.commentsCount,
              color: const Color(0xFFFF6B35),
              onTap: onComment,
            ),
          ),

          // زر إعادة النشر مع العداد
          Expanded(
            child: _buildActionButtonWithCount(
              icon: Icons.repeat,
              label: 'Repost',
              count: post.repostsCount,
              color: const Color(0xFF42B883),
              onTap: onRepost,
            ),
          ),

          // زر المشاركة مع العداد
          Expanded(
            child: _buildActionButtonWithCount(
              icon: Icons.share,
              label: 'Share',
              count: post.sharesCount,
              color: const Color(0xFF9C27B0),
              onTap: onShare,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر تفاعل مع عداد
  Widget _buildActionButtonWithCount({
    required IconData icon,
    required String label,
    required int count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return PremiumUIEnhancements.premiumTapEffect(
      onTap: onTap,
      scaleDown: 0.92,
      duration: const Duration(milliseconds: 150),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // العداد فوق الزر
            if (count > 0) ...[
              Text(
                _formatCount(count),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              const SizedBox(height: 2),
            ],
            
            // الزر
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    size: 14,
                    color: color,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق العدد
  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      final k = count / 1000;
      return k % 1 == 0 ? '${k.toInt()}K' : '${k.toStringAsFixed(1)}K';
    } else {
      final m = count / 1000000;
      return m % 1 == 0 ? '${m.toInt()}M' : '${m.toStringAsFixed(1)}M';
    }
  }
}

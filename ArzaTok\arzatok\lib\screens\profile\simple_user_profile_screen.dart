import 'package:flutter/material.dart';
import '../../models/user_model.dart';
import '../chat/chat_screen.dart';
import '../calls/voice_call_screen.dart';
import '../calls/video_call_screen.dart';

/// شاشة ملف شخصي مبسطة مع أزرار الاتصال
class SimpleUserProfileScreen extends StatefulWidget {
  final UserModel user;

  const SimpleUserProfileScreen({
    super.key,
    required this.user,
  });

  @override
  State<SimpleUserProfileScreen> createState() => _SimpleUserProfileScreenState();
}

class _SimpleUserProfileScreenState extends State<SimpleUserProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(widget.user.name),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header with gradient background
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFD32F2F),
                    Color(0xFFB71C1C),
                  ],
                ),
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),

                  // Profile Image
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 4),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Colors.white,
                      backgroundImage: widget.user.profileImageUrl != null
                          ? NetworkImage(widget.user.profileImageUrl!)
                          : null,
                      child: widget.user.profileImageUrl == null
                          ? Text(
                              widget.user.name.isNotEmpty
                                  ? widget.user.name[0].toUpperCase()
                                  : '?',
                              style: const TextStyle(
                                fontSize: 40,
                                color: Color(0xFFD32F2F),
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Name
                  Text(
                    widget.user.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Online Status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          color: widget.user.isOnline ? Colors.green : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.user.isOnline ? 'Online now' : 'Offline',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 30),

                  // Action Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Message Button
                      _buildActionButton(
                        icon: Icons.message,
                        label: 'Message',
                        color: Colors.white,
                        textColor: const Color(0xFFD32F2F),
                        onPressed: () => _startChat(),
                      ),

                      // Voice Call Button
                      _buildActionButton(
                        icon: Icons.call,
                        label: 'Call',
                        color: const Color(0xFF4CAF50),
                        textColor: Colors.white,
                        onPressed: () => _startVoiceCall(),
                      ),

                      // Video Call Button
                      _buildActionButton(
                        icon: Icons.videocam,
                        label: 'Video',
                        color: const Color(0xFF2196F3),
                        textColor: Colors.white,
                        onPressed: () => _startVideoCall(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 30),
                ],
              ),
            ),

            // User Info Card
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'User Information',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFD32F2F),
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildInfoRow(Icons.phone, 'Phone', widget.user.phoneNumber),
                  const SizedBox(height: 12),

                  _buildInfoRow(
                    widget.user.gender == Gender.male ? Icons.male : Icons.female,
                    'Gender',
                    widget.user.gender == Gender.male ? 'Male' : 'Female',
                  ),
                  const SizedBox(height: 12),

                  _buildInfoRow(Icons.cake, 'Age', '${widget.user.age} years old'),
                  const SizedBox(height: 12),

                  _buildInfoRow(Icons.location_on, 'Location', '${widget.user.city}, ${widget.user.country}'),
                ],
              ),
            ),

            // Content Tabs Placeholder
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.photo_library,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Content Tabs',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Photos • Videos • Links • Posts',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر تفاعل
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required Color textColor,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(30),
              child: Icon(
                icon,
                color: textColor,
                size: 28,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xFFD32F2F), size: 20),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  /// بدء دردشة حقيقية
  void _startChat() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChatScreen(otherUser: widget.user),
      ),
    );
  }

  /// بدء مكالمة صوتية حقيقية
  void _startVoiceCall() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.call, color: const Color(0xFF4CAF50)),
            const SizedBox(width: 8),
            const Text('Voice Call'),
          ],
        ),
        content: Text('Start voice call with ${widget.user.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // فتح شاشة المكالمة الصوتية الحقيقية
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => VoiceCallScreen(otherUser: widget.user),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
            child: const Text('Call'),
          ),
        ],
      ),
    );
  }

  /// بدء مكالمة فيديو حقيقية
  void _startVideoCall() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.videocam, color: const Color(0xFF2196F3)),
            const SizedBox(width: 8),
            const Text('Video Call'),
          ],
        ),
        content: Text('Start video call with ${widget.user.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // فتح شاشة مكالمة الفيديو الحقيقية
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => VideoCallScreen(otherUser: widget.user),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2196F3),
              foregroundColor: Colors.white,
            ),
            child: const Text('Call'),
          ),
        ],
      ),
    );
  }
}

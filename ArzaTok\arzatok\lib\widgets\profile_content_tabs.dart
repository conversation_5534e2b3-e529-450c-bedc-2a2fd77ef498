import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../services/social_feed_service.dart';
import '../services/privacy_service.dart';
import 'premium_ui_enhancements.dart';

/// نظام تصنيف المحتوى في الملفات الشخصية
class ProfileContentTabs extends StatefulWidget {
  final UserModel user;
  final bool isCurrentUser;

  const ProfileContentTabs({
    Key? key,
    required this.user,
    this.isCurrentUser = false,
  }) : super(key: key);

  @override
  State<ProfileContentTabs> createState() => _ProfileContentTabsState();
}

class _ProfileContentTabsState extends State<ProfileContentTabs>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<PostModel> _userPosts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadUserContent();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserContent() async {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final posts = await socialService.getUserPosts(widget.user.phoneNumber);
    
    if (mounted) {
      setState(() {
        _userPosts = posts;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط التبويبات
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: const Color(0xFFD32F2F),
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: const Color(0xFFD32F2F),
            indicatorWeight: 3,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
            tabs: [
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.photo_library, size: 18),
                    const SizedBox(width: 6),
                    Text('Photos'),
                    const SizedBox(width: 4),
                    _buildCountBadge(_getPhotosCount()),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.video_library, size: 18),
                    const SizedBox(width: 6),
                    Text('Videos'),
                    const SizedBox(width: 4),
                    _buildCountBadge(_getVideosCount()),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.link, size: 18),
                    const SizedBox(width: 6),
                    Text('Links'),
                    const SizedBox(width: 4),
                    _buildCountBadge(_getLinksCount()),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.article, size: 18),
                    const SizedBox(width: 6),
                    Text('Posts'),
                    const SizedBox(width: 4),
                    _buildCountBadge(_getTextPostsCount()),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.live_tv, size: 18),
                    const SizedBox(width: 6),
                    Text('Live'),
                    const SizedBox(width: 4),
                    _buildCountBadge(_getLiveStreamsCount()),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.repeat, size: 18),
                    const SizedBox(width: 6),
                    Text('Shared'),
                    const SizedBox(width: 4),
                    _buildCountBadge(_getSharedPostsCount()),
                  ],
                ),
              ),
            ],
          ),
        ),

        // محتوى التبويبات
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPhotosGrid(),
                    _buildVideosGrid(),
                    _buildLinksGrid(),
                    _buildTextPostsGrid(),
                    _buildLiveStreamsGrid(),
                    _buildSharedPostsGrid(),
                  ],
                ),
        ),
      ],
    );
  }

  /// بناء شارة العدد
  Widget _buildCountBadge(int count) {
    if (count == 0) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: const Color(0xFFD32F2F),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// شبكة الصور
  Widget _buildPhotosGrid() {
    final photos = _userPosts.where((post) => 
        post.imageUrls.isNotEmpty && post.videoUrls.isEmpty).toList();
    
    if (photos.isEmpty) {
      return _buildEmptyState(
        icon: Icons.photo_library,
        title: 'No Photos',
        subtitle: widget.isCurrentUser 
            ? 'Share your first photo!'
            : '${widget.user.name} hasn\'t shared any photos yet.',
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: photos.length,
      itemBuilder: (context, index) {
        final post = photos[index];
        return PremiumUIEnhancements.premiumTapEffect(
          onTap: () => _showPostDetail(post),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Image.network(
                    post.imageUrls.first,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: const Icon(Icons.broken_image, color: Colors.grey),
                      );
                    },
                  ),
                  if (post.imageUrls.length > 1)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '+${post.imageUrls.length - 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// شبكة الفيديوهات
  Widget _buildVideosGrid() {
    final videos = _userPosts.where((post) => post.videoUrls.isNotEmpty).toList();
    
    if (videos.isEmpty) {
      return _buildEmptyState(
        icon: Icons.video_library,
        title: 'No Videos',
        subtitle: widget.isCurrentUser 
            ? 'Share your first video!'
            : '${widget.user.name} hasn\'t shared any videos yet.',
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 16/9,
      ),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final post = videos[index];
        return PremiumUIEnhancements.premiumTapEffect(
          onTap: () => _showPostDetail(post),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Container(
                    color: Colors.black,
                    child: const Icon(
                      Icons.play_circle_fill,
                      color: Colors.white,
                      size: 50,
                    ),
                  ),
                  Positioned(
                    bottom: 8,
                    left: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        post.content.isNotEmpty 
                            ? post.content.length > 50
                                ? '${post.content.substring(0, 50)}...'
                                : post.content
                            : 'Video',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// شبكة الروابط
  Widget _buildLinksGrid() {
    final links = _userPosts.where((post) => 
        post.linkUrl != null && post.linkUrl!.isNotEmpty).toList();
    
    if (links.isEmpty) {
      return _buildEmptyState(
        icon: Icons.link,
        title: 'No Links',
        subtitle: widget.isCurrentUser 
            ? 'Share your first link!'
            : '${widget.user.name} hasn\'t shared any links yet.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: links.length,
      itemBuilder: (context, index) {
        final post = links[index];
        return PremiumUIEnhancements.premiumTapEffect(
          onTap: () => _showPostDetail(post),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFFD32F2F).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.link,
                    color: Color(0xFFD32F2F),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.linkTitle ?? 'Link',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        post.linkUrl!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// شبكة المنشورات النصية
  Widget _buildTextPostsGrid() {
    final textPosts = _userPosts.where((post) => 
        post.imageUrls.isEmpty && 
        post.videoUrls.isEmpty && 
        (post.linkUrl == null || post.linkUrl!.isEmpty) &&
        post.content.isNotEmpty).toList();
    
    if (textPosts.isEmpty) {
      return _buildEmptyState(
        icon: Icons.article,
        title: 'No Text Posts',
        subtitle: widget.isCurrentUser 
            ? 'Share your thoughts!'
            : '${widget.user.name} hasn\'t shared any text posts yet.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: textPosts.length,
      itemBuilder: (context, index) {
        final post = textPosts[index];
        return PremiumUIEnhancements.premiumTapEffect(
          onTap: () => _showPostDetail(post),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  post.content,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.favorite,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${post.likesCount}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.comment,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${post.commentsCount}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      post.timeAgo,
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// شبكة البث المباشر
  Widget _buildLiveStreamsGrid() {
    // مؤقت - لا توجد بث مباشر حالياً
    return _buildEmptyState(
      icon: Icons.live_tv,
      title: 'No Live Streams',
      subtitle: widget.isCurrentUser 
          ? 'Start your first live stream!'
          : '${widget.user.name} hasn\'t done any live streams yet.',
    );
  }

  /// شبكة المنشورات المشاركة
  Widget _buildSharedPostsGrid() {
    final sharedPosts = _userPosts.where((post) => post.isRepost).toList();
    
    if (sharedPosts.isEmpty) {
      return _buildEmptyState(
        icon: Icons.repeat,
        title: 'No Shared Posts',
        subtitle: widget.isCurrentUser 
            ? 'Share your first post!'
            : '${widget.user.name} hasn\'t shared any posts yet.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sharedPosts.length,
      itemBuilder: (context, index) {
        final post = sharedPosts[index];
        return PremiumUIEnhancements.premiumTapEffect(
          onTap: () => _showPostDetail(post),
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF42B883).withOpacity(0.3)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.repeat,
                      color: const Color(0xFF42B883),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Shared',
                      style: TextStyle(
                        color: const Color(0xFF42B883),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      post.timeAgo,
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  post.content.isNotEmpty ? post.content : 'Shared a post',
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل المنشور
  void _showPostDetail(PostModel post) {
    // TODO: فتح شاشة تفاصيل المنشور
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening post: ${post.content.substring(0, 20)}...'),
      ),
    );
  }

  // دوال العد
  int _getPhotosCount() => _userPosts.where((post) => 
      post.imageUrls.isNotEmpty && post.videoUrls.isEmpty).length;
  
  int _getVideosCount() => _userPosts.where((post) => 
      post.videoUrls.isNotEmpty).length;
  
  int _getLinksCount() => _userPosts.where((post) => 
      post.linkUrl != null && post.linkUrl!.isNotEmpty).length;
  
  int _getTextPostsCount() => _userPosts.where((post) => 
      post.imageUrls.isEmpty && 
      post.videoUrls.isEmpty && 
      (post.linkUrl == null || post.linkUrl!.isEmpty) &&
      post.content.isNotEmpty).length;
  
  int _getLiveStreamsCount() => 0; // مؤقت
  
  int _getSharedPostsCount() => _userPosts.where((post) => 
      post.isRepost).length;
}

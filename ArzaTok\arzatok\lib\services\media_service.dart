import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';

/// خدمة الوسائط المتقدمة
class MediaService extends ChangeNotifier {
  static final MediaService _instance = MediaService._internal();
  factory MediaService() => _instance;
  MediaService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  final ImagePicker _picker = ImagePicker();

  /// اختيار صورة من المعرض وحفظها في Firebase
  Future<String?> pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        debugPrint('✅ Image picked: ${image.path}');

        // رفع الصورة إلى Firebase Storage
        final file = File(image.path);
        final fileName = 'images/${DateTime.now().millisecondsSinceEpoch}_${image.name}';
        final downloadUrl = await uploadFileToStorage(file, fileName, 'chat_media');

        if (downloadUrl != null) {
          debugPrint('✅ Image uploaded to Firebase: $downloadUrl');
          return downloadUrl; // إرجاع رابط Firebase
        } else {
          debugPrint('❌ CRITICAL: Failed to upload image to Firebase - Operation cancelled');
          return null; // فشل العملية بدلاً من العودة للمسار المحلي
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error picking image: $e');
      return null;
    }
  }

  /// التقاط صورة من الكاميرا وحفظها في Firebase
  Future<String?> captureImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        debugPrint('✅ Image captured: ${image.path}');

        // رفع الصورة إلى Firebase Storage
        final file = File(image.path);
        final fileName = 'images/${DateTime.now().millisecondsSinceEpoch}_captured_${image.name}';
        final downloadUrl = await uploadFileToStorage(file, fileName, 'chat_media');

        if (downloadUrl != null) {
          debugPrint('✅ Captured image uploaded to Firebase: $downloadUrl');
          return downloadUrl; // إرجاع رابط Firebase
        } else {
          debugPrint('❌ CRITICAL: Failed to upload captured image to Firebase - Operation cancelled');
          return null; // فشل العملية بدلاً من العودة للمسار المحلي
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error capturing image: $e');
      return null;
    }
  }

  /// رفع ملف إلى Firebase Storage مع تسجيل مفصل
  Future<String?> uploadFileToStorage(File file, String fileName, String folder) async {
    try {
      debugPrint('📤 Starting Firebase Storage upload...');
      debugPrint('📁 File path: ${file.path}');
      debugPrint('📁 File name: $fileName');
      debugPrint('📁 Folder: $folder');
      debugPrint('📁 File exists: ${await file.exists()}');

      if (await file.exists()) {
        final fileSize = await file.length();
        debugPrint('📁 File size: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB');
      }

      if (!await file.exists()) {
        debugPrint('❌ File does not exist!');
        return null;
      }

      final ref = _storage.ref().child('$folder/$fileName');
      debugPrint('📁 Storage reference: ${ref.fullPath}');

      final uploadTask = ref.putFile(file);

      // مراقبة تقدم الرفع
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        debugPrint('📊 Upload progress: ${progress.toStringAsFixed(1)}%');
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      debugPrint('✅ File uploaded successfully to Firebase Storage!');
      debugPrint('🔗 Download URL: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      debugPrint('❌ CRITICAL ERROR uploading to Firebase Storage: $e');
      debugPrint('🔍 Error type: ${e.runtimeType}');
      debugPrint('🔍 Error details: ${e.toString()}');
      return null;
    }
  }

  /// رفع فيديو من المعرض وحفظه في Firebase
  Future<String?> pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5), // حد أقصى 5 دقائق
      );

      if (video != null) {
        debugPrint('✅ Video picked: ${video.path}');

        // رفع الفيديو إلى Firebase Storage
        final file = File(video.path);
        final fileName = 'videos/${DateTime.now().millisecondsSinceEpoch}_${video.name}';
        final downloadUrl = await uploadFileToStorage(file, fileName, 'chat_media');

        if (downloadUrl != null) {
          debugPrint('✅ Video uploaded to Firebase: $downloadUrl');
          return downloadUrl; // إرجاع رابط Firebase
        } else {
          debugPrint('❌ CRITICAL: Failed to upload video to Firebase - Operation cancelled');
          return null; // فشل العملية بدلاً من العودة للمسار المحلي
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error picking video: $e');
      return null;
    }
  }

  /// تسجيل فيديو بالكاميرا وحفظه في Firebase
  Future<String?> recordVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        debugPrint('✅ Video recorded: ${video.path}');

        // رفع الفيديو إلى Firebase Storage
        final file = File(video.path);
        final fileName = 'videos/${DateTime.now().millisecondsSinceEpoch}_recorded_${video.name}';
        final downloadUrl = await uploadFileToStorage(file, fileName, 'chat_media');

        if (downloadUrl != null) {
          debugPrint('✅ Recorded video uploaded to Firebase: $downloadUrl');
          return downloadUrl; // إرجاع رابط Firebase
        } else {
          debugPrint('❌ CRITICAL: Failed to upload recorded video to Firebase - Operation cancelled');
          return null; // فشل العملية بدلاً من العودة للمسار المحلي
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error recording video: $e');
      return null;
    }
  }

  /// بدء البث المباشر (محاكاة)
  Future<String?> startLiveStream() async {
    try {
      // في التطبيق الحقيقي، استخدم خدمة بث مثل Agora أو WebRTC
      await Future.delayed(const Duration(seconds: 2));

      final streamId = 'live_${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('🔴 Live stream started: $streamId');

      return streamId;
    } catch (e) {
      debugPrint('❌ Error starting live stream: $e');
      return null;
    }
  }

  /// إيقاف البث المباشر
  Future<bool> stopLiveStream(String streamId) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      debugPrint('⏹️ Live stream stopped: $streamId');
      return true;
    } catch (e) {
      debugPrint('❌ Error stopping live stream: $e');
      return false;
    }
  }

  /// الحصول على الموقع الحالي
  Future<Map<String, dynamic>?> getCurrentLocation() async {
    try {
      // طلب إذن الموقع
      final permission = await Permission.location.request();
      if (permission != PermissionStatus.granted) {
        debugPrint('❌ Location permission denied');
        return null;
      }

      // التحقق من تفعيل خدمات الموقع
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ Location services are disabled');
        return null;
      }

      // الحصول على الموقع
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // الحصول على اسم المكان (محاكاة)
      final locationName = await _getLocationName(position.latitude, position.longitude);

      return {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'name': locationName,
        'accuracy': position.accuracy,
      };
    } catch (e) {
      debugPrint('❌ Error getting location: $e');
      return null;
    }
  }

  /// الحصول على اسم المكان (محاكاة)
  Future<String> _getLocationName(double latitude, double longitude) async {
    // في التطبيق الحقيقي، استخدم خدمة Geocoding
    await Future.delayed(const Duration(seconds: 1));

    final locations = [
      'Downtown',
      'City Center',
      'Shopping Mall',
      'University',
      'Park',
      'Restaurant',
      'Coffee Shop',
      'Home',
      'Office',
      'Gym',
    ];

    return locations[DateTime.now().millisecondsSinceEpoch % locations.length];
  }

  /// الحصول على جهات الاتصال (تجريبية)
  Future<List<Map<String, String>>> getContacts() async {
    try {
      // محاكاة تحميل جهات الاتصال
      await Future.delayed(const Duration(seconds: 1));

      debugPrint('✅ Loaded demo contacts');
      return _getDemoContacts();
    } catch (e) {
      debugPrint('❌ Error getting contacts: $e');
      return _getDemoContacts();
    }
  }

  /// جهات اتصال حقيقية
  List<Map<String, String>> _getDemoContacts() {
    return [
      // الأصدقاء الحقيقيون
      {'id': '1', 'name': 'محمد', 'phone': '+212716233701'},
      {'id': '2', 'name': 'سعيد', 'phone': '+212638813823'},
      {'id': '3', 'name': 'معاد', 'phone': '+212681626960'},
      {'id': '4', 'name': 'أشرف', 'phone': '+212646380642'},

      // جهات اتصال إضافية
      {'id': '5', 'name': 'فاطمة الزهراء', 'phone': '+212600111001'},
      {'id': '6', 'name': 'عبد الرحمن', 'phone': '+212600111002'},
      {'id': '7', 'name': 'عائشة', 'phone': '+212600111003'},
      {'id': '8', 'name': 'يوسف', 'phone': '+212600111004'},
      {'id': '9', 'name': 'خديجة', 'phone': '+212600111005'},
      {'id': '10', 'name': 'حسام', 'phone': '+212600111006'},
      {'id': '11', 'name': 'زينب', 'phone': '+212600111007'},
      {'id': '12', 'name': 'عمر', 'phone': '+212600111008'},
      {'id': '13', 'name': 'سلمى', 'phone': '+212600111009'},
      {'id': '14', 'name': 'كريم', 'phone': '+212600111010'},
      {'id': '15', 'name': 'نادية', 'phone': '+212600111011'},
    ];
  }

  /// الحصول على قائمة المشاعر
  List<Map<String, dynamic>> getFeelings() {
    return [
      {'emoji': '😊', 'name': 'Happy', 'color': 0xFFFFC107},
      {'emoji': '😢', 'name': 'Sad', 'color': 0xFF2196F3},
      {'emoji': '😍', 'name': 'In Love', 'color': 0xFFE91E63},
      {'emoji': '😴', 'name': 'Sleepy', 'color': 0xFF9C27B0},
      {'emoji': '🤔', 'name': 'Thinking', 'color': 0xFF607D8B},
      {'emoji': '😎', 'name': 'Cool', 'color': 0xFF795548},
      {'emoji': '🤗', 'name': 'Grateful', 'color': 0xFF4CAF50},
      {'emoji': '😤', 'name': 'Frustrated', 'color': 0xFFFF5722},
      {'emoji': '🥳', 'name': 'Celebrating', 'color': 0xFFFF9800},
      {'emoji': '😌', 'name': 'Blessed', 'color': 0xFF00BCD4},
      {'emoji': '🤩', 'name': 'Excited', 'color': 0xFFFFEB3B},
      {'emoji': '😇', 'name': 'Peaceful', 'color': 0xFF03DAC6},
    ];
  }

  /// الحصول على ألوان الخلفية
  List<Map<String, dynamic>> getBackgroundColors() {
    return [
      {'name': 'Red Gradient', 'colors': [0xFFD32F2F, 0xFFB71C1C]},
      {'name': 'Blue Gradient', 'colors': [0xFF1976D2, 0xFF1565C0]},
      {'name': 'Green Gradient', 'colors': [0xFF388E3C, 0xFF2E7D32]},
      {'name': 'Purple Gradient', 'colors': [0xFF7B1FA2, 0xFF6A1B9A]},
      {'name': 'Orange Gradient', 'colors': [0xFFFF6F00, 0xFFE65100]},
      {'name': 'Pink Gradient', 'colors': [0xFFE91E63, 0xFFC2185B]},
      {'name': 'Teal Gradient', 'colors': [0xFF00796B, 0xFF00695C]},
      {'name': 'Indigo Gradient', 'colors': [0xFF303F9F, 0xFF283593]},
      {'name': 'Brown Gradient', 'colors': [0xFF5D4037, 0xFF4E342E]},
      {'name': 'Dark Gradient', 'colors': [0xFF424242, 0xFF212121]},
    ];
  }

  /// اختيار ملف موسيقى حقيقي
  Future<Map<String, String>?> pickMusic() async {
    try {
      // استخدام file_picker لاختيار ملفات الصوت الحقيقية
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          // استخراج اسم الملف بدون الامتداد
          final fileName = file.name.split('.').first;

          debugPrint('🎵 Real music file selected: ${file.name}');
          debugPrint('🎵 File path: $filePath');
          debugPrint('🎵 File size: ${(file.size / 1024 / 1024).toStringAsFixed(2)} MB');

          return {
            'title': fileName,
            'artist': 'Unknown Artist',
            'url': filePath,
            'originalName': file.name,
            'size': '${(file.size / 1024 / 1024).toStringAsFixed(2)} MB',
          };
        }
      }

      debugPrint('❌ No music file selected');
      return null;
    } catch (e) {
      debugPrint('❌ Error picking music: $e');
      return null;
    }
  }

  /// التحقق من صحة الفيديو
  Future<bool> isValidVideo(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) return false;

      final size = await file.length();
      const maxSize = 100 * 1024 * 1024; // 100MB

      return size <= maxSize;
    } catch (e) {
      debugPrint('❌ Error validating video: $e');
      return false;
    }
  }

  /// الحصول على معلومات الفيديو
  Future<Map<String, dynamic>?> getVideoInfo(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) return null;

      final size = await file.length();
      final sizeInMB = (size / (1024 * 1024)).toStringAsFixed(2);

      return {
        'path': path,
        'size': size,
        'sizeText': '${sizeInMB}MB',
        'duration': '00:30', // في التطبيق الحقيقي، احصل على المدة الفعلية
      };
    } catch (e) {
      debugPrint('❌ Error getting video info: $e');
      return null;
    }
  }

  /// إنشاء thumbnail للفيديو
  Future<File?> generateVideoThumbnail(String videoPath) async {
    try {
      // في التطبيق الحقيقي، استخدم مكتبة مثل video_thumbnail
      // هنا سنقوم بمحاكاة إنشاء thumbnail
      await Future.delayed(const Duration(seconds: 1));

      final file = File(videoPath);
      if (!await file.exists()) return null;

      // محاكاة إنشاء thumbnail
      final thumbnailPath = videoPath.replaceAll('.mp4', '_thumbnail.jpg');
      final thumbnailFile = File(thumbnailPath);

      // في التطبيق الحقيقي، ستقوم بإنشاء thumbnail حقيقي
      // هنا سنقوم بنسخ الملف الأصلي كمحاكاة
      debugPrint('🖼️ Generated video thumbnail: $thumbnailPath');

      return thumbnailFile;
    } catch (e) {
      debugPrint('❌ Error generating video thumbnail: $e');
      return null;
    }
  }
}

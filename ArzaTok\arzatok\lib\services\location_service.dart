class LocationService {
  static final Map<String, List<String>> _countriesAndCities = {
    'United States': [
      'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
      'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
      'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis',
      'Seattle', 'Denver', 'Washington', 'Boston', 'El Paso', 'Nashville',
      'Detroit', 'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 'Louisville',
      'Baltimore', 'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento',
      'Kansas City', 'Mesa', 'Atlanta', 'Omaha', 'Colorado Springs', 'Raleigh',
      'Miami', 'Virginia Beach', 'Oakland', 'Minneapolis', 'Tulsa', 'Arlington',
      'Tampa', 'New Orleans', 'Wichita', 'Cleveland'
    ],
    'United Kingdom': [
      'London', 'Birmingham', 'Manchester', 'Glasgow', 'Liverpool', 'Leeds',
      'Sheffield', 'Edinburgh', 'Bristol', 'Cardiff', 'Leicester', 'Belfast',
      'Nottingham', 'Newcastle', 'Brighton', 'Hull', 'Plymouth', 'Stoke-on-Trent',
      'Wolverhampton', 'Derby', 'Swansea', 'Southampton', 'Salford', 'Aberdeen',
      'Westminster', 'Portsmouth', 'York', 'Peterborough', 'Dundee', 'Lancaster',
      'Oxford', 'Newport', 'Preston', 'St Albans', 'Norwich', 'Chester',
      'Cambridge', 'Salisbury', 'Exeter', 'Gloucester', 'Lisburn', 'Chichester'
    ],
    'Canada': [
      'Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton', 'Ottawa',
      'Winnipeg', 'Quebec City', 'Hamilton', 'Kitchener', 'London', 'Victoria',
      'Halifax', 'Oshawa', 'Windsor', 'Saskatoon', 'St. Catharines', 'Regina',
      'Sherbrooke', 'Barrie', 'Kelowna', 'Abbotsford', 'Kingston', 'Sudbury',
      'Saguenay', 'Trois-Rivières', 'Guelph', 'Cambridge', 'Whitby', 'Coquitlam',
      'Saanich', 'Chatham-Kent', 'Mississauga', 'Brampton', 'Markham', 'Vaughan'
    ],
    'Australia': [
      'Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast',
      'Newcastle', 'Canberra', 'Central Coast', 'Wollongong', 'Logan City',
      'Geelong', 'Hobart', 'Townsville', 'Cairns', 'Darwin', 'Toowoomba',
      'Ballarat', 'Bendigo', 'Albury', 'Launceston', 'Mackay', 'Rockhampton',
      'Bunbury', 'Bundaberg', 'Coffs Harbour', 'Wagga Wagga', 'Hervey Bay',
      'Mildura', 'Shepparton', 'Port Macquarie', 'Gladstone', 'Tamworth'
    ],
    'Germany': [
      'Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt', 'Stuttgart',
      'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig', 'Bremen', 'Dresden',
      'Hanover', 'Nuremberg', 'Duisburg', 'Bochum', 'Wuppertal', 'Bielefeld',
      'Bonn', 'Münster', 'Karlsruhe', 'Mannheim', 'Augsburg', 'Wiesbaden',
      'Gelsenkirchen', 'Mönchengladbach', 'Braunschweig', 'Chemnitz', 'Kiel',
      'Aachen', 'Halle', 'Magdeburg', 'Freiburg', 'Krefeld', 'Lübeck'
    ],
    'France': [
      'Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg',
      'Montpellier', 'Bordeaux', 'Lille', 'Rennes', 'Reims', 'Le Havre',
      'Saint-Étienne', 'Toulon', 'Angers', 'Grenoble', 'Dijon', 'Nîmes',
      'Aix-en-Provence', 'Saint-Quentin-en-Yvelines', 'Brest', 'Le Mans',
      'Amiens', 'Tours', 'Limoges', 'Clermont-Ferrand', 'Villeurbanne',
      'Besançon', 'Orléans', 'Metz', 'Rouen', 'Mulhouse', 'Perpignan', 'Caen'
    ],
    'Italy': [
      'Rome', 'Milan', 'Naples', 'Turin', 'Palermo', 'Genoa', 'Bologna',
      'Florence', 'Bari', 'Catania', 'Venice', 'Verona', 'Messina', 'Padua',
      'Trieste', 'Taranto', 'Brescia', 'Prato', 'Parma', 'Modena', 'Reggio Calabria',
      'Reggio Emilia', 'Perugia', 'Livorno', 'Ravenna', 'Cagliari', 'Foggia',
      'Rimini', 'Salerno', 'Ferrara', 'Sassari', 'Latina', 'Giugliano', 'Monza'
    ],
    'Spain': [
      'Madrid', 'Barcelona', 'Valencia', 'Seville', 'Zaragoza', 'Málaga',
      'Murcia', 'Palma', 'Las Palmas', 'Bilbao', 'Alicante', 'Córdoba',
      'Valladolid', 'Vigo', 'Gijón', 'Hospitalet', 'A Coruña', 'Vitoria-Gasteiz',
      'Granada', 'Elche', 'Oviedo', 'Badalona', 'Cartagena', 'Terrassa',
      'Jerez de la Frontera', 'Sabadell', 'Móstoles', 'Santa Cruz de Tenerife',
      'Pamplona', 'Almería', 'Alcalá de Henares', 'Fuenlabrada', 'Leganés'
    ],
    'Japan': [
      'Tokyo', 'Yokohama', 'Osaka', 'Nagoya', 'Sapporo', 'Fukuoka', 'Kobe',
      'Kawasaki', 'Kyoto', 'Saitama', 'Hiroshima', 'Sendai', 'Kitakyushu',
      'Chiba', 'Sakai', 'Niigata', 'Hamamatsu', 'Okayama', 'Sagamihara',
      'Shizuoka', 'Kumamoto', 'Kagoshima', 'Matsuyama', 'Kanazawa', 'Utsunomiya',
      'Matsudo', 'Kawaguchi', 'Ichikawa', 'Suita', 'Toyama', 'Toyonaka',
      'Nagasaki', 'Machida', 'Gifu', 'Fujisawa', 'Kashiwa', 'Toyohashi'
    ],
    'China': [
      'Shanghai', 'Beijing', 'Chongqing', 'Tianjin', 'Guangzhou', 'Shenzhen',
      'Wuhan', 'Dongguan', 'Chengdu', 'Nanjing', 'Foshan', 'Shenyang',
      'Hangzhou', 'Xian', 'Harbin', 'Qingdao', 'Changchun', 'Jinan',
      'Zhengzhou', 'Kunming', 'Dalian', 'Changsha', 'Taiyuan', 'Hefei',
      'Shijiazhuang', 'Urumqi', 'Suzhou', 'Wuxi', 'Xiamen', 'Ningbo',
      'Lanzhou', 'Tangshan', 'Zibo', 'Weifang', 'Baotou', 'Xuzhou'
    ],
    'India': [
      'Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Ahmedabad', 'Chennai',
      'Kolkata', 'Surat', 'Pune', 'Jaipur', 'Lucknow', 'Kanpur', 'Nagpur',
      'Indore', 'Thane', 'Bhopal', 'Visakhapatnam', 'Pimpri-Chinchwad',
      'Patna', 'Vadodara', 'Ghaziabad', 'Ludhiana', 'Agra', 'Nashik',
      'Faridabad', 'Meerut', 'Rajkot', 'Kalyan-Dombivali', 'Vasai-Virar',
      'Varanasi', 'Srinagar', 'Dhanbad', 'Jodhpur', 'Amritsar', 'Raipur'
    ],
    'Brazil': [
      'São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza',
      'Belo Horizonte', 'Manaus', 'Curitiba', 'Recife', 'Goiânia',
      'Belém', 'Porto Alegre', 'Guarulhos', 'Campinas', 'São Luís',
      'São Gonçalo', 'Maceió', 'Duque de Caxias', 'Nova Iguaçu', 'Teresina',
      'Natal', 'Campo Grande', 'São Bernardo do Campo', 'João Pessoa',
      'Santo André', 'Osasco', 'Jaboatão dos Guararapes', 'Contagem',
      'São José dos Campos', 'Uberlândia', 'Sorocaba', 'Cuiabá'
    ],
    'Mexico': [
      'Mexico City', 'Guadalajara', 'Monterrey', 'Puebla', 'Tijuana',
      'León', 'Juárez', 'Torreón', 'Querétaro', 'San Luis Potosí',
      'Mérida', 'Mexicali', 'Aguascalientes', 'Cuernavaca', 'Saltillo',
      'Hermosillo', 'Culiacán', 'Morelia', 'Cancún', 'Xalapa',
      'Chihuahua', 'Tampico', 'Reynosa', 'Toluca', 'Veracruz',
      'Villahermosa', 'Irapuato', 'Mazatlán', 'Nuevo Laredo', 'Acapulco'
    ],
    'Russia': [
      'Moscow', 'Saint Petersburg', 'Novosibirsk', 'Yekaterinburg', 'Nizhny Novgorod',
      'Kazan', 'Chelyabinsk', 'Omsk', 'Samara', 'Rostov-on-Don',
      'Ufa', 'Krasnoyarsk', 'Perm', 'Voronezh', 'Volgograd',
      'Krasnodar', 'Saratov', 'Tyumen', 'Tolyatti', 'Izhevsk',
      'Barnaul', 'Ulyanovsk', 'Irkutsk', 'Vladivostok', 'Yaroslavl',
      'Habarovsk', 'Makhachkala', 'Tomsk', 'Orenburg', 'Kemerovo'
    ],
    'South Korea': [
      'Seoul', 'Busan', 'Incheon', 'Daegu', 'Daejeon', 'Gwangju',
      'Suwon', 'Ulsan', 'Changwon', 'Goyang', 'Yongin', 'Seongnam',
      'Bucheon', 'Cheongju', 'Ansan', 'Jeonju', 'Anyang', 'Cheonan',
      'Pohang', 'Uijeongbu', 'Siheung', 'Paju', 'Gimhae', 'Pyeongtaek',
      'Jinju', 'Yangsan', 'Icheon', 'Asan', 'Gunpo', 'Gwangmyeong'
    ],
    'Morocco': [
      'Casablanca', 'Rabat', 'Fez', 'Marrakech', 'Agadir', 'Tangier',
      'Meknes', 'Oujda', 'Kenitra', 'Tetouan', 'Safi', 'Mohammedia',
      'Khouribga', 'El Jadida', 'Beni Mellal', 'Nador', 'Taza',
      'Settat', 'Larache', 'Ksar El Kebir', 'Khemisset', 'Guelmim',
      'Berrechid', 'Wazzane', 'Ouarzazate', 'Tiznit', 'Taroudant',
      'Sidi Kacem', 'Khenifra', 'Errachidia', 'Ouezzane', 'Azrou'
    ],
    'Egypt': [
      'Cairo', 'Alexandria', 'Giza', 'Shubra El Kheima', 'Port Said',
      'Suez', 'Luxor', 'Mansoura', 'El Mahalla El Kubra', 'Tanta',
      'Asyut', 'Ismailia', 'Fayyum', 'Zagazig', 'Aswan',
      'Damietta', 'Damanhur', 'Minya', 'Beni Suef', 'Hurghada',
      'Qena', 'Sohag', 'Shibin El Kom', 'Banha', 'Kafr El Sheikh',
      'Arish', 'Mallawi', 'Bilbays', 'Mit Ghamr', 'Al Fashn'
    ],
    'Saudi Arabia': [
      'Riyadh', 'Jeddah', 'Mecca', 'Medina', 'Dammam', 'Khobar',
      'Tabuk', 'Buraidah', 'Khamis Mushait', 'Hail', 'Hafar Al-Batin',
      'Jubail', 'Dhahran', 'Taif', 'Najran', 'Yanbu', 'Al Qatif',
      'Abha', 'Unaizah', 'Arar', 'Sakakah', 'Jizan', 'Qurayyat',
      'Al Bahah', 'Ras Tanura', 'Bisha', 'Dawadmi', 'Rafha',
      'Al Majmaah', 'Al Kharj', 'Wadi Al Dawasir', 'Ar Rass'
    ],
    'Turkey': [
      'Istanbul', 'Ankara', 'Izmir', 'Bursa', 'Adana', 'Gaziantep',
      'Konya', 'Antalya', 'Kayseri', 'Mersin', 'Eskisehir', 'Diyarbakir',
      'Samsun', 'Denizli', 'Sanliurfa', 'Adapazari', 'Malatya', 'Kahramanmaras',
      'Erzurum', 'Van', 'Batman', 'Elazig', 'Erzincan', 'Sivas',
      'Manisa', 'Tarsus', 'Trabzon', 'Ordu', 'Afyon', 'Isparta'
    ],
    'Nigeria': [
      'Lagos', 'Kano', 'Ibadan', 'Abuja', 'Port Harcourt', 'Benin City',
      'Maiduguri', 'Zaria', 'Aba', 'Jos', 'Ilorin', 'Oyo',
      'Enugu', 'Abeokuta', 'Kaduna', 'Ogbomoso', 'Sokoto', 'Katsina',
      'Bauchi', 'Minna', 'Warri', 'Okene', 'Calabar', 'Uyo',
      'Akure', 'Makurdi', 'Lafia', 'Gombe', 'Umuahia', 'Damaturu'
    ],
    'South Africa': [
      'Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth',
      'Bloemfontein', 'East London', 'Pietermaritzburg', 'Benoni', 'Tembisa',
      'Germiston', 'Soweto', 'Randburg', 'Centurion', 'Roodepoort',
      'Boksburg', 'Welkom', 'Newcastle', 'Krugersdorp', 'Diepsloot',
      'Botshabelo', 'Brakpan', 'Witbank', 'Oberholzer', 'Kempton Park'
    ]
  };

  static List<String> getAllCountries() {
    return _countriesAndCities.keys.toList()..sort();
  }

  static List<String> getCitiesForCountry(String country) {
    return _countriesAndCities[country] ?? [];
  }

  static bool isValidCountry(String country) {
    return _countriesAndCities.containsKey(country);
  }

  static bool isValidCityForCountry(String city, String country) {
    final cities = _countriesAndCities[country];
    return cities != null && cities.contains(city);
  }
}

<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- خلفية دائرية حمراء متدرجة -->
  <defs>
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF4444;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E53E3E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C53030;stop-opacity:1" />
    </linearGradient>
    
    <!-- ظل داخلي -->
    <filter id="innerShadow">
      <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
      <feOffset dx="2" dy="2" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="offset" operator="in"/>
      <feComposite in2="SourceGraphic" operator="over"/>
    </filter>
    
    <!-- ظل خارجي -->
    <filter id="dropShadow">
      <feDropShadow dx="4" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- الخلفية الدائرية -->
  <circle cx="256" cy="256" r="240" fill="url(#redGradient)" filter="url(#dropShadow)"/>
  
  <!-- حلقة داخلية للعمق -->
  <circle cx="256" cy="256" r="220" fill="none" stroke="#FFFFFF" stroke-width="4" opacity="0.3"/>
  
  <!-- أيقونة الدردشة الرئيسية -->
  <g transform="translate(256,256)">
    <!-- فقاعة الدردشة الكبيرة -->
    <path d="M-80,-60 Q-80,-100 -40,-100 L40,-100 Q80,-100 80,-60 L80,20 Q80,60 40,60 L-20,60 L-60,90 L-60,60 Q-80,60 -80,20 Z" 
          fill="#FFFFFF" 
          filter="url(#innerShadow)"/>
    
    <!-- فقاعة الدردشة الصغيرة -->
    <path d="M20,-20 Q20,-40 35,-40 L65,-40 Q80,-40 80,-25 L80,-5 Q80,10 65,10 L45,10 L30,25 L30,10 Q20,10 20,-5 Z" 
          fill="#FFE5E5" 
          opacity="0.8"/>
    
    <!-- نقاط الدردشة -->
    <circle cx="-40" cy="-40" r="8" fill="#E53E3E"/>
    <circle cx="-10" cy="-40" r="8" fill="#E53E3E"/>
    <circle cx="20" cy="-40" r="8" fill="#E53E3E"/>
    
    <!-- خطوط النص في الفقاعة الكبيرة -->
    <rect x="-60" y="-20" width="80" height="6" rx="3" fill="#E53E3E" opacity="0.6"/>
    <rect x="-60" y="-5" width="60" height="6" rx="3" fill="#E53E3E" opacity="0.4"/>
    <rect x="-60" y="10" width="70" height="6" rx="3" fill="#E53E3E" opacity="0.3"/>
    
    <!-- أيقونة القلب الصغيرة -->
    <path d="M45,-15 Q45,-20 50,-20 Q55,-20 55,-15 Q55,-10 50,-5 Q45,-10 45,-15 Z" 
          fill="#FF6B6B" 
          opacity="0.8"/>
  </g>
  
  <!-- تأثير اللمعان -->
  <ellipse cx="200" cy="180" rx="60" ry="30" fill="#FFFFFF" opacity="0.3" transform="rotate(-30 200 180)"/>
  
  <!-- نص ArzaTalk في الأسفل -->
  <text x="256" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF" opacity="0.9">
    ArzaTalk
  </text>
</svg>

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/user_model.dart';

/// شاشة المكالمة الصوتية الحقيقية
class VoiceCallScreen extends StatefulWidget {
  final UserModel otherUser;

  const VoiceCallScreen({
    super.key,
    required this.otherUser,
  });

  @override
  State<VoiceCallScreen> createState() => _VoiceCallScreenState();
}

class _VoiceCallScreenState extends State<VoiceCallScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isConnected = false;
  String _callStatus = 'Calling...';
  int _callDuration = 0;

  @override
  void initState() {
    super.initState();
    
    // تحضير الأنيميشن
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);
    
    // بدء الأنيميشن
    _pulseController.repeat(reverse: true);
    _fadeController.forward();
    
    // محاكاة الاتصال
    _simulateCall();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  /// محاكاة عملية الاتصال
  void _simulateCall() async {
    // انتظار 3 ثواني ثم الاتصال
    await Future.delayed(const Duration(seconds: 3));
    
    if (mounted) {
      setState(() {
        _isConnected = true;
        _callStatus = 'Connected';
      });
      
      // بدء عداد المكالمة
      _startCallTimer();
    }
  }

  /// بدء عداد وقت المكالمة
  void _startCallTimer() {
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted && _isConnected) {
        setState(() {
          _callDuration++;
        });
        return true;
      }
      return false;
    });
  }

  /// تنسيق وقت المكالمة
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // شريط علوي مع معلومات المكالمة
              _buildTopBar(),
              
              // المحتوى الرئيسي
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // صورة المتصل مع أنيميشن
                    _buildCallerAvatar(),
                    
                    const SizedBox(height: 40),
                    
                    // اسم المتصل
                    Text(
                      widget.otherUser.name,
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // حالة المكالمة
                    Text(
                      _isConnected ? _formatDuration(_callDuration) : _callStatus,
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              
              // أزرار التحكم
              _buildControlButtons(),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء الشريط العلوي
  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // زر الرجوع (مخفي أثناء المكالمة)
          if (!_isConnected)
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back, color: Colors.white),
            ),
          
          const Spacer(),
          
          // أيقونة المكالمة
          Icon(
            Icons.call,
            color: _isConnected ? Colors.green : Colors.white,
            size: 24,
          ),
          
          const SizedBox(width: 8),
          
          Text(
            'Voice Call',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صورة المتصل مع الأنيميشن
  Widget _buildCallerAvatar() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _isConnected ? 1.0 : _pulseAnimation.value,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _isConnected ? Colors.green : Colors.white,
                width: 4,
              ),
              boxShadow: [
                BoxShadow(
                  color: (_isConnected ? Colors.green : Colors.white)
                      .withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 96,
              backgroundColor: const Color(0xFFD32F2F),
              backgroundImage: widget.otherUser.profileImageUrl != null
                  ? NetworkImage(widget.otherUser.profileImageUrl!)
                  : null,
              child: widget.otherUser.profileImageUrl == null
                  ? Text(
                      widget.otherUser.name.isNotEmpty
                          ? widget.otherUser.name[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        fontSize: 60,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
          ),
        );
      },
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // زر كتم الصوت
          _buildControlButton(
            icon: _isMuted ? Icons.mic_off : Icons.mic,
            color: _isMuted ? Colors.red : Colors.grey[700]!,
            onPressed: () {
              setState(() {
                _isMuted = !_isMuted;
              });
              
              HapticFeedback.lightImpact();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(_isMuted ? '🔇 Microphone muted' : '🎤 Microphone unmuted'),
                  duration: const Duration(seconds: 1),
                ),
              );
            },
          ),
          
          // زر إنهاء المكالمة
          _buildControlButton(
            icon: Icons.call_end,
            color: Colors.red,
            size: 80,
            onPressed: () {
              HapticFeedback.heavyImpact();
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('📞 Call with ${widget.otherUser.name} ended'),
                  backgroundColor: Colors.red,
                ),
              );
            },
          ),
          
          // زر السماعة
          _buildControlButton(
            icon: _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
            color: _isSpeakerOn ? Colors.blue : Colors.grey[700]!,
            onPressed: () {
              setState(() {
                _isSpeakerOn = !_isSpeakerOn;
              });
              
              HapticFeedback.lightImpact();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(_isSpeakerOn ? '🔊 Speaker on' : '🔉 Speaker off'),
                  duration: const Duration(seconds: 1),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// بناء زر تحكم
  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
    double size = 70,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Icon(
            icon,
            color: Colors.white,
            size: size * 0.4,
          ),
        ),
      ),
    );
  }
}

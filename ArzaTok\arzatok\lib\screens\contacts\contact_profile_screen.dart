import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/contact_model.dart';
import '../../models/user_model.dart';
import '../../services/contact_service.dart';
import '../chat/chat_screen.dart';

class ContactProfileScreen extends StatefulWidget {
  final ContactModel contact;

  const ContactProfileScreen({
    super.key,
    required this.contact,
  });

  @override
  State<ContactProfileScreen> createState() => _ContactProfileScreenState();
}

class _ContactProfileScreenState extends State<ContactProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Contact Info'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _showEditContactDialog();
                  break;
                case 'delete':
                  _showDeleteContactDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.black),
                    SizedBox(width: 8),
                    Text('Edit Contact'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete Contact', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: const Color(0xFFD32F2F),
                    backgroundImage: widget.contact.profileImageUrl != null
                        ? NetworkImage(widget.contact.profileImageUrl!)
                        : null,
                    child: widget.contact.profileImageUrl == null
                        ? Text(
                            widget.contact.name.isNotEmpty
                                ? widget.contact.name[0].toUpperCase()
                                : '?',
                            style: const TextStyle(
                              fontSize: 40,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.contact.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.contact.phoneNumber,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (widget.contact.status != null && widget.contact.isOnArzaTalk) ...[
                    const SizedBox(height: 8),
                    Text(
                      widget.contact.status!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  if (widget.contact.isOnArzaTalk && widget.contact.lastSeen != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Last seen: ${_formatLastSeen(widget.contact.lastSeen!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const Divider(),

            // Action buttons
            if (widget.contact.isOnArzaTalk) ...[
              _buildActionTile(
                icon: Icons.chat,
                title: 'Message',
                subtitle: 'Send a message',
                onTap: () {
                  final user = UserModel(
                    phoneNumber: widget.contact.phoneNumber,
                    name: widget.contact.name,
                    profileImageUrl: widget.contact.profileImageUrl,
                    status: widget.contact.status,
                    lastSeen: widget.contact.lastSeen ?? DateTime.now(),
                    isOnline: false,
                    gender: Gender.male, // Default value
                    age: 25, // Default value
                    country: 'Unknown', // Default value
                    city: 'Unknown', // Default value
                    registrationDate: DateTime.now(),
                  );

                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ChatScreen(otherUser: user),
                    ),
                  );
                },
              ),


              const Divider(),

              // Notification settings
              Consumer<ContactService>(
                builder: (context, contactService, child) {
                  final isMuted = contactService.isContactMuted(widget.contact.phoneNumber);
                  return SwitchListTile(
                    secondary: Icon(
                      isMuted ? Icons.volume_off : Icons.volume_up,
                      color: const Color(0xFFD32F2F),
                    ),
                    title: const Text('Notifications'),
                    subtitle: Text(isMuted ? 'Muted' : 'Enabled'),
                    value: !isMuted,
                    activeColor: const Color(0xFFD32F2F),
                    onChanged: (value) {
                      if (value) {
                        contactService.unmuteContact(widget.contact.phoneNumber);
                      } else {
                        contactService.muteContact(widget.contact.phoneNumber);
                      }
                    },
                  );
                },
              ),

              const Divider(),

              // Privacy and security
              _buildActionTile(
                icon: Icons.block,
                title: 'Block Contact',
                subtitle: 'Block this contact',
                textColor: Colors.red,
                onTap: () {
                  _showBlockContactDialog();
                },
              ),
              _buildActionTile(
                icon: Icons.report,
                title: 'Report Contact',
                subtitle: 'Report this contact',
                textColor: Colors.red,
                onTap: () {
                  _showReportContactDialog();
                },
              ),

              _buildActionTile(
                icon: Icons.clear,
                title: 'Clear Chat',
                subtitle: 'Clear all messages',
                onTap: () {
                  _showClearChatDialog();
                },
              ),
            ] else ...[
              _buildActionTile(
                icon: Icons.person_add,
                title: 'Invite to ArzaTalk',
                subtitle: 'Invite this contact to join ArzaTalk',
                onTap: () {
                  _showInviteDialog();
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? const Color(0xFFD32F2F),
      ),
      title: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  void _showEditContactDialog() {
    final nameController = TextEditingController(text: widget.contact.name);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Contact'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: 'Name',
            prefixIcon: Icon(Icons.person),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty) {
                Provider.of<ContactService>(context, listen: false)
                    .updateContact(
                  phoneNumber: widget.contact.phoneNumber,
                  name: nameController.text.trim(),
                );
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Contact updated')),
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showDeleteContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Contact'),
        content: Text('Are you sure you want to delete ${widget.contact.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<ContactService>(context, listen: false)
                  .deleteContact(widget.contact.phoneNumber);
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Contact deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showBlockContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Contact'),
        content: Text('Are you sure you want to block ${widget.contact.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<ContactService>(context, listen: false)
                  .blockUser(widget.contact.phoneNumber);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Contact blocked')),
              );
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showReportContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Contact'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Why are you reporting ${widget.contact.name}?'),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                hintText: 'Reason for reporting...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<ContactService>(context, listen: false)
                  .reportUser(widget.contact.phoneNumber, 'User reported');
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Contact reported')),
              );
            },
            child: const Text('Report', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showClearChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat'),
        content: Text('Are you sure you want to clear all messages with ${widget.contact.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Clear chat messages
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Chat cleared')),
              );
            },
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showInviteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Invite to ArzaTalk'),
        content: Text('Invite ${widget.contact.name} to join ArzaTalk?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Invitation sent to ${widget.contact.name}')),
              );
            },
            child: const Text('Invite'),
          ),
        ],
      ),
    );
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return 'over a week ago';
    }
  }
}

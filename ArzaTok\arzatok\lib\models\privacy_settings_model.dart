/// مستويات رؤية المحتوى
enum ContentVisibility {
  everyone,    // الجميع
  friends,     // الأصدقاء فقط
  onlyMe,      // أنا فقط
}

/// أنواع المحتوى
enum ContentType {
  photos,      // الصور
  videos,      // الفيديوهات
  links,       // الروابط
  textPosts,   // المنشورات النصية
  sharedPosts, // المنشورات المشاركة
  liveStreams, // البث المباشر
}

class PrivacySettings {
  final bool showPhoneNumber;
  final bool showAge;
  final bool showGender;
  final bool showLocation;
  final bool showRegistrationDate;
  final bool showLastSeen;
  final bool showOnlineStatus;

  // إعدادات خصوصية المحتوى
  final ContentVisibility photosVisibility;
  final ContentVisibility videosVisibility;
  final ContentVisibility linksVisibility;
  final ContentVisibility textPostsVisibility;
  final ContentVisibility sharedPostsVisibility;
  final ContentVisibility liveStreamsVisibility;

  const PrivacySettings({
    this.showPhoneNumber = true,
    this.showAge = true,
    this.showGender = true,
    this.showLocation = true,
    this.showRegistrationDate = true,
    this.showLastSeen = true,
    this.showOnlineStatus = true,
    this.photosVisibility = ContentVisibility.everyone,
    this.videosVisibility = ContentVisibility.everyone,
    this.linksVisibility = ContentVisibility.everyone,
    this.textPostsVisibility = ContentVisibility.everyone,
    this.sharedPostsVisibility = ContentVisibility.everyone,
    this.liveStreamsVisibility = ContentVisibility.everyone,
  });

  PrivacySettings copyWith({
    bool? showPhoneNumber,
    bool? showAge,
    bool? showGender,
    bool? showLocation,
    bool? showRegistrationDate,
    bool? showLastSeen,
    bool? showOnlineStatus,
    ContentVisibility? photosVisibility,
    ContentVisibility? videosVisibility,
    ContentVisibility? linksVisibility,
    ContentVisibility? textPostsVisibility,
    ContentVisibility? sharedPostsVisibility,
    ContentVisibility? liveStreamsVisibility,
  }) {
    return PrivacySettings(
      showPhoneNumber: showPhoneNumber ?? this.showPhoneNumber,
      showAge: showAge ?? this.showAge,
      showGender: showGender ?? this.showGender,
      showLocation: showLocation ?? this.showLocation,
      showRegistrationDate: showRegistrationDate ?? this.showRegistrationDate,
      showLastSeen: showLastSeen ?? this.showLastSeen,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
      photosVisibility: photosVisibility ?? this.photosVisibility,
      videosVisibility: videosVisibility ?? this.videosVisibility,
      linksVisibility: linksVisibility ?? this.linksVisibility,
      textPostsVisibility: textPostsVisibility ?? this.textPostsVisibility,
      sharedPostsVisibility: sharedPostsVisibility ?? this.sharedPostsVisibility,
      liveStreamsVisibility: liveStreamsVisibility ?? this.liveStreamsVisibility,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'showPhoneNumber': showPhoneNumber,
      'showAge': showAge,
      'showGender': showGender,
      'showLocation': showLocation,
      'showRegistrationDate': showRegistrationDate,
      'showLastSeen': showLastSeen,
      'showOnlineStatus': showOnlineStatus,
      'photosVisibility': photosVisibility.toString().split('.').last,
      'videosVisibility': videosVisibility.toString().split('.').last,
      'linksVisibility': linksVisibility.toString().split('.').last,
      'textPostsVisibility': textPostsVisibility.toString().split('.').last,
      'sharedPostsVisibility': sharedPostsVisibility.toString().split('.').last,
      'liveStreamsVisibility': liveStreamsVisibility.toString().split('.').last,
    };
  }

  factory PrivacySettings.fromJson(Map<String, dynamic> json) {
    return PrivacySettings(
      showPhoneNumber: json['showPhoneNumber'] ?? true,
      showAge: json['showAge'] ?? true,
      showGender: json['showGender'] ?? true,
      showLocation: json['showLocation'] ?? true,
      showRegistrationDate: json['showRegistrationDate'] ?? true,
      showLastSeen: json['showLastSeen'] ?? true,
      showOnlineStatus: json['showOnlineStatus'] ?? true,
      photosVisibility: _parseContentVisibility(json['photosVisibility']),
      videosVisibility: _parseContentVisibility(json['videosVisibility']),
      linksVisibility: _parseContentVisibility(json['linksVisibility']),
      textPostsVisibility: _parseContentVisibility(json['textPostsVisibility']),
      sharedPostsVisibility: _parseContentVisibility(json['sharedPostsVisibility']),
      liveStreamsVisibility: _parseContentVisibility(json['liveStreamsVisibility']),
    );
  }

  static ContentVisibility _parseContentVisibility(String? value) {
    switch (value) {
      case 'friends':
        return ContentVisibility.friends;
      case 'onlyMe':
        return ContentVisibility.onlyMe;
      default:
        return ContentVisibility.everyone;
    }
  }

  /// التحقق من إمكانية رؤية نوع محتوى معين
  bool canViewContent(ContentType contentType, String viewerId, String ownerId) {
    // إذا كان المالك نفسه، يمكنه رؤية كل شيء
    if (viewerId == ownerId) return true;

    ContentVisibility visibility;
    switch (contentType) {
      case ContentType.photos:
        visibility = photosVisibility;
        break;
      case ContentType.videos:
        visibility = videosVisibility;
        break;
      case ContentType.links:
        visibility = linksVisibility;
        break;
      case ContentType.textPosts:
        visibility = textPostsVisibility;
        break;
      case ContentType.sharedPosts:
        visibility = sharedPostsVisibility;
        break;
      case ContentType.liveStreams:
        visibility = liveStreamsVisibility;
        break;
    }

    switch (visibility) {
      case ContentVisibility.everyone:
        return true;
      case ContentVisibility.friends:
        // TODO: التحقق من الصداقة
        return true; // مؤقتاً
      case ContentVisibility.onlyMe:
        return false;
    }
  }
}

/// امتدادات مساعدة
extension ContentVisibilityExtension on ContentVisibility {
  String get displayName {
    switch (this) {
      case ContentVisibility.everyone:
        return 'Everyone';
      case ContentVisibility.friends:
        return 'Friends';
      case ContentVisibility.onlyMe:
        return 'Only me';
    }
  }

  String get description {
    switch (this) {
      case ContentVisibility.everyone:
        return 'Anyone can see this content';
      case ContentVisibility.friends:
        return 'Only your friends can see this content';
      case ContentVisibility.onlyMe:
        return 'Only you can see this content';
    }
  }
}

extension ContentTypeExtension on ContentType {
  String get displayName {
    switch (this) {
      case ContentType.photos:
        return 'Photos';
      case ContentType.videos:
        return 'Videos';
      case ContentType.links:
        return 'Links';
      case ContentType.textPosts:
        return 'Text Posts';
      case ContentType.sharedPosts:
        return 'Shared Posts';
      case ContentType.liveStreams:
        return 'Live Streams';
    }
  }

  String get description {
    switch (this) {
      case ContentType.photos:
        return 'Photos you\'ve shared';
      case ContentType.videos:
        return 'Videos you\'ve posted';
      case ContentType.links:
        return 'Links you\'ve shared';
      case ContentType.textPosts:
        return 'Text posts you\'ve written';
      case ContentType.sharedPosts:
        return 'Posts you\'ve reposted';
      case ContentType.liveStreams:
        return 'Live streams you\'ve hosted';
    }
  }
}

class GroupModel {
  final String id;
  final String name;
  final String? description;
  final String? groupImageUrl;
  final List<String> members;
  final List<String> admins;
  final String createdBy;
  final DateTime createdAt;
  final DateTime lastMessageTime;
  final String? lastMessage;
  final int unreadCount;
  final bool isArchived;
  final bool isMuted;

  GroupModel({
    required this.id,
    required this.name,
    this.description,
    this.groupImageUrl,
    required this.members,
    required this.admins,
    required this.createdBy,
    required this.createdAt,
    required this.lastMessageTime,
    this.lastMessage,
    this.unreadCount = 0,
    this.isArchived = false,
    this.isMuted = false,
  });

  factory GroupModel.fromMap(Map<String, dynamic> map) {
    return GroupModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      groupImageUrl: map['groupImageUrl'],
      members: List<String>.from(map['members'] ?? []),
      admins: List<String>.from(map['admins'] ?? []),
      createdBy: map['createdBy'] ?? '',
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      lastMessageTime: DateTime.parse(map['lastMessageTime'] ?? DateTime.now().toIso8601String()),
      lastMessage: map['lastMessage'],
      unreadCount: map['unreadCount'] ?? 0,
      isArchived: map['isArchived'] ?? false,
      isMuted: map['isMuted'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'groupImageUrl': groupImageUrl,
      'members': members,
      'admins': admins,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'lastMessageTime': lastMessageTime.toIso8601String(),
      'lastMessage': lastMessage,
      'unreadCount': unreadCount,
      'isArchived': isArchived,
      'isMuted': isMuted,
    };
  }

  GroupModel copyWith({
    String? id,
    String? name,
    String? description,
    String? groupImageUrl,
    List<String>? members,
    List<String>? admins,
    String? createdBy,
    DateTime? createdAt,
    DateTime? lastMessageTime,
    String? lastMessage,
    int? unreadCount,
    bool? isArchived,
    bool? isMuted,
  }) {
    return GroupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      groupImageUrl: groupImageUrl ?? this.groupImageUrl,
      members: members ?? this.members,
      admins: admins ?? this.admins,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      isArchived: isArchived ?? this.isArchived,
      isMuted: isMuted ?? this.isMuted,
    );
  }

  bool isAdmin(String phoneNumber) {
    return admins.contains(phoneNumber);
  }

  bool isMember(String phoneNumber) {
    return members.contains(phoneNumber);
  }

  @override
  String toString() {
    return 'GroupModel(id: $id, name: $name, members: ${members.length}, admins: ${admins.length})';
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppTheme { light, dark, system }

class ThemeService extends ChangeNotifier {
  AppTheme _currentTheme = AppTheme.system;
  bool _isDarkMode = false;

  AppTheme get currentTheme => _currentTheme;
  bool get isDarkMode => _isDarkMode;

  ThemeService() {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt('app_theme') ?? 2; // Default to system
      _currentTheme = AppTheme.values[themeIndex];
      _updateDarkMode();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme: $e');
    }
  }

  Future<void> setTheme(AppTheme theme) async {
    try {
      _currentTheme = theme;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('app_theme', theme.index);
      _updateDarkMode();
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving theme: $e');
    }
  }

  void _updateDarkMode() {
    switch (_currentTheme) {
      case AppTheme.light:
        _isDarkMode = false;
        break;
      case AppTheme.dark:
        _isDarkMode = true;
        break;
      case AppTheme.system:
        // This will be updated by the system brightness
        _isDarkMode = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
        break;
    }
  }

  void updateSystemBrightness(Brightness brightness) {
    if (_currentTheme == AppTheme.system) {
      final newDarkMode = brightness == Brightness.dark;
      if (_isDarkMode != newDarkMode) {
        _isDarkMode = newDarkMode;
        notifyListeners();
      }
    }
  }

  ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primarySwatch: Colors.red,
    primaryColor: const Color(0xFFD32F2F),
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFFD32F2F),
      foregroundColor: Colors.white,
      elevation: 1,
    ),
    cardTheme: CardTheme(
      color: Colors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
    listTileTheme: const ListTileThemeData(
      iconColor: Color(0xFFD32F2F),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: Color(0xFFD32F2F),
      foregroundColor: Colors.white,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
      ),
    ),
  );

  ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primarySwatch: Colors.red,
    primaryColor: const Color(0xFFD32F2F),
    scaffoldBackgroundColor: const Color(0xFF121212),
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFFD32F2F),
      brightness: Brightness.dark,
      surface: const Color(0xFF1E1E1E),
      onSurface: Colors.white,
      primary: const Color(0xFFD32F2F),
      onPrimary: Colors.white,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF1F1F1F),
      foregroundColor: Colors.white,
      elevation: 1,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.w500,
      ),
    ),
    cardTheme: CardTheme(
      color: const Color(0xFF1E1E1E),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
    listTileTheme: const ListTileThemeData(
      iconColor: Color(0xFFD32F2F),
      textColor: Colors.white,
      titleTextStyle: TextStyle(color: Colors.white, fontSize: 16),
      subtitleTextStyle: TextStyle(color: Colors.white70, fontSize: 14),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: Color(0xFFD32F2F),
      foregroundColor: Colors.white,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      labelStyle: const TextStyle(color: Colors.white70),
      hintStyle: const TextStyle(color: Colors.white54),
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Colors.white54),
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xFFD32F2F)),
        borderRadius: BorderRadius.circular(8),
      ),
      filled: true,
      fillColor: const Color(0xFF2A2A2A),
      prefixIconColor: Colors.white70,
      suffixIconColor: Colors.white70,
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: Colors.white, fontSize: 16),
      bodyMedium: TextStyle(color: Colors.white, fontSize: 14),
      bodySmall: TextStyle(color: Colors.white70, fontSize: 12),
      titleLarge: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 22),
      titleMedium: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 18),
      titleSmall: TextStyle(color: Colors.white, fontSize: 16),
      headlineLarge: TextStyle(color: Colors.white, fontSize: 32),
      headlineMedium: TextStyle(color: Colors.white, fontSize: 28),
      headlineSmall: TextStyle(color: Colors.white, fontSize: 24),
      displayLarge: TextStyle(color: Colors.white, fontSize: 36),
      displayMedium: TextStyle(color: Colors.white, fontSize: 32),
      displaySmall: TextStyle(color: Colors.white, fontSize: 28),
      labelLarge: TextStyle(color: Colors.white, fontSize: 14),
      labelMedium: TextStyle(color: Colors.white, fontSize: 12),
      labelSmall: TextStyle(color: Colors.white70, fontSize: 10),
    ),
    iconTheme: const IconThemeData(color: Colors.white),
    primaryIconTheme: const IconThemeData(color: Colors.white),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF1F1F1F),
      selectedItemColor: Color(0xFFD32F2F),
      unselectedItemColor: Colors.grey,
      selectedLabelStyle: TextStyle(color: Color(0xFFD32F2F)),
      unselectedLabelStyle: TextStyle(color: Colors.grey),
    ),
    tabBarTheme: const TabBarTheme(
      labelColor: Color(0xFFD32F2F),
      unselectedLabelColor: Colors.grey,
      indicatorColor: Color(0xFFD32F2F),
    ),
    dividerColor: Colors.grey[800],
    dialogTheme: const DialogTheme(
      backgroundColor: Color(0xFF1E1E1E),
      titleTextStyle: TextStyle(color: Colors.white, fontSize: 20),
      contentTextStyle: TextStyle(color: Colors.white),
    ),
  );

  String get themeDisplayName {
    switch (_currentTheme) {
      case AppTheme.light:
        return 'Light';
      case AppTheme.dark:
        return 'Dark';
      case AppTheme.system:
        return 'System';
    }
  }
}

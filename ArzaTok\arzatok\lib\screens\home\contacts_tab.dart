import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/contact_service.dart';
import '../../models/contact_model.dart';
import '../../models/user_model.dart';
import '../contacts/contact_profile_screen.dart';
import '../chat/chat_screen.dart';
// import '../profile/simple_user_profile_screen.dart'; // غير مستخدم

class ContactsTab extends StatefulWidget {
  const ContactsTab({super.key});

  @override
  State<ContactsTab> createState() => _ContactsTabState();
}

class _ContactsTabState extends State<ContactsTab> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _showOnlyArzaTalkUsers = false;

  @override
  void initState() {
    super.initState();
    // Sync contacts with ArzaTalk users
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ContactService>(context, listen: false).syncWithArzaTalkUsers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search contacts...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Filter toggle
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: SwitchListTile(
                    title: const Text('Show only ArzaTalk users'),
                    value: _showOnlyArzaTalkUsers,
                    onChanged: (value) {
                      setState(() {
                        _showOnlyArzaTalkUsers = value;
                      });
                    },
                    activeColor: const Color(0xFFD32F2F),
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Contacts list
          Expanded(
            child: Consumer<ContactService>(
              builder: (context, contactService, child) {
                List<ContactModel> contacts;

                if (_showOnlyArzaTalkUsers) {
                  contacts = contactService.getContactsOnArzaTok();
                } else {
                  contacts = contactService.getAllContacts();
                }

                if (_searchQuery.isNotEmpty) {
                  contacts = contactService.searchContacts(_searchQuery);
                  if (_showOnlyArzaTalkUsers) {
                    contacts = contacts.where((c) => c.isOnArzaTalk).toList();
                  }
                }

                if (contacts.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _searchQuery.isNotEmpty ? Icons.search_off : Icons.contacts,
                          size: 80,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isNotEmpty
                              ? 'No contacts found'
                              : _showOnlyArzaTalkUsers
                                  ? 'No ArzaTalk contacts'
                                  : 'No contacts yet',
                          style: const TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        if (_searchQuery.isEmpty) ...[
                          const SizedBox(height: 8),
                          Text(
                            _showOnlyArzaTalkUsers
                                ? 'Invite your friends to join ArzaTalk'
                                : 'Add contacts to start chatting',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: contacts.length,
                  itemBuilder: (context, index) {
                    final contact = contacts[index];
                    return _buildContactTile(contact);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddContactDialog();
        },
        backgroundColor: const Color(0xFFD32F2F),
        child: const Icon(Icons.person_add, color: Colors.white),
      ),
    );
  }

  Widget _buildContactTile(ContactModel contact) {
    return ListTile(
      leading: Stack(
        children: [
          CircleAvatar(
            backgroundColor: const Color(0xFFD32F2F),
            backgroundImage: contact.profileImageUrl != null
                ? NetworkImage(contact.profileImageUrl!)
                : null,
            child: contact.profileImageUrl == null
                ? Text(
                    contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          if (contact.isOnArzaTalk)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
              ),
            ),
        ],
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              contact.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          if (contact.isBlocked)
            const Icon(Icons.block, size: 16, color: Colors.red),
          if (contact.isMuted)
            const Icon(Icons.volume_off, size: 16, color: Colors.grey),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            contact.phoneNumber,
            style: TextStyle(color: Colors.grey[600]),
          ),
          if (contact.status != null && contact.isOnArzaTalk)
            Text(
              contact.status!,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      trailing: contact.isOnArzaTalk
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.chat, color: Color(0xFFD32F2F)),
                  onPressed: () {
                    // Convert contact to user and navigate to chat
                    final user = UserModel(
                      phoneNumber: contact.phoneNumber,
                      name: contact.name,
                      profileImageUrl: contact.profileImageUrl,
                      status: contact.status,
                      lastSeen: contact.lastSeen ?? DateTime.now(),
                      isOnline: false,
                      gender: Gender.male, // Default value
                      age: 25, // Default value
                      country: 'Unknown', // Default value
                      city: 'Unknown', // Default value
                      registrationDate: DateTime.now(),
                    );
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ChatScreen(otherUser: user),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.info_outline, color: Colors.grey),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ContactProfileScreen(contact: contact),
                      ),
                    );
                  },
                ),
              ],
            )
          : TextButton(
              onPressed: () {
                _showInviteDialog(contact);
              },
              child: const Text('Invite'),
            ),
      onTap: () {
        if (contact.isOnArzaTalk) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ContactProfileScreen(contact: contact),
            ),
          );
        } else {
          _showInviteDialog(contact);
        }
      },
    );
  }

  void _showAddContactDialog() {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Contact'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                prefixIcon: Icon(Icons.phone),
                hintText: '+1234567890',
              ),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty && phoneController.text.isNotEmpty) {
                final contact = ContactModel(
                  phoneNumber: phoneController.text.trim(),
                  name: nameController.text.trim(),
                );

                Provider.of<ContactService>(context, listen: false)
                    .addContact(contact);

                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Contact added')),
                );
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showInviteDialog(ContactModel contact) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Invite to ArzaTalk'),
        content: Text('Invite ${contact.name} to join ArzaTalk?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // In a real app, this would send an invitation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Invitation sent to ${contact.name}')),
              );
            },
            child: const Text('Invite'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}



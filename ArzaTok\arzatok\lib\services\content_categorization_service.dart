import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/post_model.dart';
import '../models/privacy_settings_model.dart';
import '../services/auth_service.dart';

/// خدمة تصنيف المحتوى في الملف الشخصي
class ContentCategorizationService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // قوائم المحتوى المصنف
  List<PostModel> _allPosts = [];
  List<PostModel> _photos = [];
  List<PostModel> _videos = [];
  List<PostModel> _links = [];
  List<PostModel> _textPosts = [];
  List<PostModel> _sharedPosts = [];
  List<PostModel> _liveStreams = [];

  // Getters
  List<PostModel> get allPosts => _allPosts;
  List<PostModel> get photos => _photos;
  List<PostModel> get videos => _videos;
  List<PostModel> get links => _links;
  List<PostModel> get textPosts => _textPosts;
  List<PostModel> get sharedPosts => _sharedPosts;
  List<PostModel> get liveStreams => _liveStreams;

  /// تحميل وتصنيف محتوى المستخدم
  Future<void> loadUserContent(String userId) async {
    try {
      debugPrint('🔄 Loading content for user: $userId');
      
      // تحميل جميع منشورات المستخدم من Firebase
      final querySnapshot = await _firestore
          .collection('posts')
          .where('authorId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      _allPosts = querySnapshot.docs
          .map((doc) => PostModel.fromMap(doc.data()))
          .toList();

      // تصنيف المحتوى
      _categorizeContent();
      
      debugPrint('✅ Content loaded and categorized:');
      debugPrint('   📸 Photos: ${_photos.length}');
      debugPrint('   🎥 Videos: ${_videos.length}');
      debugPrint('   🔗 Links: ${_links.length}');
      debugPrint('   📝 Text Posts: ${_textPosts.length}');
      debugPrint('   🔄 Shared Posts: ${_sharedPosts.length}');
      debugPrint('   📺 Live Streams: ${_liveStreams.length}');
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error loading user content: $e');
    }
  }

  /// تصنيف المحتوى حسب النوع
  void _categorizeContent() {
    _photos.clear();
    _videos.clear();
    _links.clear();
    _textPosts.clear();
    _sharedPosts.clear();
    _liveStreams.clear();

    for (final post in _allPosts) {
      // المنشورات المشاركة (إعادة النشر)
      if (post.isRepost) {
        _sharedPosts.add(post);
        continue;
      }

      // البث المباشر
      if (post.isLiveStream) {
        _liveStreams.add(post);
        continue;
      }

      // الصور
      if (post.images.isNotEmpty) {
        _photos.add(post);
        continue;
      }

      // الفيديوهات
      if (post.videos.isNotEmpty) {
        _videos.add(post);
        continue;
      }

      // الروابط
      if (post.linkPreviews.isNotEmpty) {
        _links.add(post);
        continue;
      }

      // المنشورات النصية (إذا لم تحتوي على وسائط أو روابط)
      if (post.content.isNotEmpty) {
        _textPosts.add(post);
      }
    }
  }

  /// الحصول على محتوى مصنف حسب النوع مع مراعاة الخصوصية
  List<PostModel> getContentByType(
    ContentType contentType, 
    String viewerId, 
    String ownerId,
    PrivacySettings privacySettings,
  ) {
    // التحقق من إمكانية الرؤية
    if (!privacySettings.canViewContent(contentType, viewerId, ownerId)) {
      return [];
    }

    switch (contentType) {
      case ContentType.photos:
        return _photos;
      case ContentType.videos:
        return _videos;
      case ContentType.links:
        return _links;
      case ContentType.textPosts:
        return _textPosts;
      case ContentType.sharedPosts:
        return _sharedPosts;
      case ContentType.liveStreams:
        return _liveStreams;
    }
  }

  /// الحصول على عدد العناصر لكل تصنيف
  Map<ContentType, int> getContentCounts(
    String viewerId, 
    String ownerId,
    PrivacySettings privacySettings,
  ) {
    return {
      ContentType.photos: privacySettings.canViewContent(ContentType.photos, viewerId, ownerId) 
          ? _photos.length : 0,
      ContentType.videos: privacySettings.canViewContent(ContentType.videos, viewerId, ownerId) 
          ? _videos.length : 0,
      ContentType.links: privacySettings.canViewContent(ContentType.links, viewerId, ownerId) 
          ? _links.length : 0,
      ContentType.textPosts: privacySettings.canViewContent(ContentType.textPosts, viewerId, ownerId) 
          ? _textPosts.length : 0,
      ContentType.sharedPosts: privacySettings.canViewContent(ContentType.sharedPosts, viewerId, ownerId) 
          ? _sharedPosts.length : 0,
      ContentType.liveStreams: privacySettings.canViewContent(ContentType.liveStreams, viewerId, ownerId) 
          ? _liveStreams.length : 0,
    };
  }

  /// البحث في محتوى معين
  List<PostModel> searchInContent(
    ContentType contentType, 
    String query,
    String viewerId, 
    String ownerId,
    PrivacySettings privacySettings,
  ) {
    final content = getContentByType(contentType, viewerId, ownerId, privacySettings);
    
    if (query.isEmpty) return content;

    return content.where((post) {
      final searchText = '${post.content} ${post.authorName}'.toLowerCase();
      return searchText.contains(query.toLowerCase());
    }).toList();
  }

  /// تحديث المحتوى عند إضافة منشور جديد
  void addNewPost(PostModel post) {
    _allPosts.insert(0, post);
    _categorizeContent();
    notifyListeners();
  }

  /// تحديث المحتوى عند حذف منشور
  void removePost(String postId) {
    _allPosts.removeWhere((post) => post.id == postId);
    _categorizeContent();
    notifyListeners();
  }

  /// تحديث المحتوى عند تعديل منشور
  void updatePost(PostModel updatedPost) {
    final index = _allPosts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _allPosts[index] = updatedPost;
      _categorizeContent();
      notifyListeners();
    }
  }

  /// مسح البيانات
  void clear() {
    _allPosts.clear();
    _photos.clear();
    _videos.clear();
    _links.clear();
    _textPosts.clear();
    _sharedPosts.clear();
    _liveStreams.clear();
    notifyListeners();
  }

  /// الحصول على إحصائيات شاملة
  Map<String, dynamic> getContentStatistics(
    String viewerId, 
    String ownerId,
    PrivacySettings privacySettings,
  ) {
    final counts = getContentCounts(viewerId, ownerId, privacySettings);
    final totalVisible = counts.values.fold(0, (sum, count) => sum + count);
    
    return {
      'totalPosts': _allPosts.length,
      'visiblePosts': totalVisible,
      'breakdown': counts,
      'percentages': counts.map((type, count) => MapEntry(
        type, 
        totalVisible > 0 ? (count / totalVisible * 100).round() : 0,
      )),
    };
  }

  /// تحديث البيانات من مصادر متعددة (الدردشة، المجموعات، إلخ)
  Future<void> loadContentFromAllSources(String userId) async {
    try {
      // تحميل من منشورات الشبكة الاجتماعية
      await loadUserContent(userId);
      
      // TODO: تحميل من الدردشات
      // await _loadChatMedia(userId);
      
      // TODO: تحميل من المجموعات
      // await _loadGroupMedia(userId);
      
      // TODO: تحميل من القصص
      // await _loadStoriesMedia(userId);
      
      debugPrint('✅ Content loaded from all sources');
    } catch (e) {
      debugPrint('❌ Error loading content from all sources: $e');
    }
  }
}

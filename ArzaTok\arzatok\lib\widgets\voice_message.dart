import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

/// رسالة صوتية محسنة مثل WhatsApp
class VoiceMessage extends StatefulWidget {
  final String audioUrl;
  final int duration;
  final bool isMe;

  const VoiceMessage({
    super.key,
    required this.audioUrl,
    required this.duration,
    required this.isMe,
  });

  @override
  State<VoiceMessage> createState() => _VoiceMessageState();
}

class _VoiceMessageState extends State<VoiceMessage>
    with TickerProviderStateMixin {
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;
  Timer? _progressTimer;

  @override
  void initState() {
    super.initState();
    _totalDuration = Duration(seconds: widget.duration);
    
    // إعداد أنيميشن الموجات الصوتية
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _waveAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _waveController.dispose();
    _progressTimer?.cancel();
    super.dispose();
  }

  /// تبديل حالة التشغيل
  Future<void> _togglePlayback() async {
    HapticFeedback.lightImpact();
    
    if (_isPlaying) {
      _stopPlayback();
    } else {
      _startPlayback();
    }
  }

  /// بدء تشغيل الرسالة الصوتية
  void _startPlayback() {
    setState(() {
      _isPlaying = true;
      _isLoading = true;
    });

    // بدء أنيميشن الموجات
    _waveController.repeat(reverse: true);

    // محاكاة تحميل الملف الصوتي
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // محاكاة تقدم التشغيل
        _simulatePlayback();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🎵 Playing voice message'),
            duration: Duration(seconds: 1),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  /// إيقاف تشغيل الرسالة الصوتية
  void _stopPlayback() {
    setState(() {
      _isPlaying = false;
      _isLoading = false;
    });

    // إيقاف الأنيميشن
    _waveController.stop();
    _waveController.reset();

    // إيقاف التايمر
    _progressTimer?.cancel();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('⏸️ Voice message paused'),
        duration: Duration(seconds: 1),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// محاكاة تقدم تشغيل الصوت
  void _simulatePlayback() {
    const updateInterval = Duration(milliseconds: 100);
    final totalMs = _totalDuration.inMilliseconds;
    final stepMs = updateInterval.inMilliseconds;

    _progressTimer = Timer.periodic(updateInterval, (timer) {
      if (!mounted || !_isPlaying) {
        timer.cancel();
        return;
      }

      setState(() {
        _currentPosition = Duration(
          milliseconds: _currentPosition.inMilliseconds + stepMs,
        );
      });

      // انتهاء التشغيل
      if (_currentPosition.inMilliseconds >= totalMs) {
        timer.cancel();
        setState(() {
          _isPlaying = false;
          _currentPosition = Duration.zero;
        });
        
        _waveController.stop();
        _waveController.reset();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Voice message finished'),
            duration: Duration(seconds: 1),
            backgroundColor: Colors.blue,
          ),
        );
      }
    });
  }

  /// تنسيق الوقت
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      constraints: const BoxConstraints(
        maxWidth: 280,
        minWidth: 220,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: _togglePlayback,
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: widget.isMe 
                    ? Colors.white.withOpacity(0.2) 
                    : const Color(0xFFD32F2F).withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: widget.isMe 
                      ? Colors.white.withOpacity(0.3)
                      : const Color(0xFFD32F2F).withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.isMe ? Colors.white : const Color(0xFFD32F2F),
                        ),
                      ),
                    )
                  : Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: widget.isMe ? Colors.white : const Color(0xFFD32F2F),
                      size: 24,
                    ),
            ),
          ),

          const SizedBox(width: 12),

          // موجات صوتية ومعلومات
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الموجات الصوتية
                AnimatedBuilder(
                  animation: _waveAnimation,
                  builder: (context, child) {
                    return Container(
                      height: 32,
                      child: Row(
                        children: List.generate(25, (index) {
                          final progress = _totalDuration.inMilliseconds > 0
                              ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds
                              : 0.0;
                          final isActive = index < (25 * progress);
                          
                          // تأثير الموجة أثناء التشغيل
                          final baseHeight = (index % 4 == 0) ? 24.0 : 
                                (index % 3 == 0) ? 18.0 :
                                (index % 2 == 0) ? 12.0 : 8.0;
                          
                          final waveHeight = _isPlaying 
                              ? baseHeight * _waveAnimation.value
                              : baseHeight * 0.7;

                          return Expanded(
                            child: Container(
                              margin: const EdgeInsets.symmetric(horizontal: 0.5),
                              height: waveHeight,
                              decoration: BoxDecoration(
                                color: isActive
                                    ? (widget.isMe ? Colors.white : const Color(0xFFD32F2F))
                                    : (widget.isMe 
                                        ? Colors.white.withOpacity(0.3) 
                                        : Colors.grey[400]),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          );
                        }),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 8),

                // مدة التشغيل ومعلومات إضافية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // الوقت
                    Text(
                      _isPlaying
                          ? '${_formatDuration(_currentPosition)} / ${_formatDuration(_totalDuration)}'
                          : _formatDuration(_totalDuration),
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: widget.isMe ? Colors.white70 : Colors.grey[600],
                      ),
                    ),
                    
                    // أيقونة الصوت
                    Icon(
                      Icons.mic,
                      size: 12,
                      color: widget.isMe ? Colors.white60 : Colors.grey[500],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

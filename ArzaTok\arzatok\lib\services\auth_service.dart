import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/privacy_settings_model.dart';
import 'dart:convert';

class AuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _verificationId;

  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _currentUser != null;

  // قائمة المستخدمين المحلية (بدلاً من Firebase مؤقتاً)
  static final List<UserModel> _localUsers = [];

  // Constructor - Demo users disabled for Firebase
  AuthService() {
    // _addDemoUsers(); // Disabled for Firebase
  }

  /// إرسال رمز التحقق الحقيقي
  Future<bool> sendVerificationCode(String phoneNumber) async {
    try {
      _isLoading = true;
      notifyListeners();

      debugPrint('📱 Sending real verification code to: $phoneNumber');

      // للاختبار: إذا كان الرقم من أرقام الاختبار، استخدم رمز ثابت
      if (_isTestPhoneNumber(phoneNumber)) {
        debugPrint('🧪 Using test phone number: $phoneNumber');
        _verificationId = 'test_verification_id';
        _isLoading = false;
        notifyListeners();
        return true;
      }

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // التحقق التلقائي (Android فقط)
          debugPrint('✅ Auto verification completed');
          await _signInWithCredential(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          debugPrint('❌ Verification failed: ${e.message}');
          debugPrint('❌ Error code: ${e.code}');
          debugPrint('❌ Error details: ${e.toString()}');
          _isLoading = false;
          notifyListeners();
        },
        codeSent: (String verificationId, int? resendToken) {
          debugPrint('📨 Code sent successfully');
          _verificationId = verificationId;
          _isLoading = false;
          notifyListeners();
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          debugPrint('⏰ Code auto retrieval timeout');
          _verificationId = verificationId;
        },
        timeout: const Duration(seconds: 60),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Error sending verification code: $e');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// التحقق من أرقام الاختبار
  bool _isTestPhoneNumber(String phoneNumber) {
    final testNumbers = [
      '+212638813823', // رقمك الأول
      '+212771878802', // رقمك الثاني
      '+1234567890',   // رقم اختبار عام
      '+966501234567', // رقم اختبار سعودي
    ];
    return testNumbers.contains(phoneNumber);
  }

  /// التحقق من رمز التحقق الحقيقي
  Future<bool> verifyCode(String code) async {
    try {
      if (_verificationId == null) {
        debugPrint('❌ No verification ID found');
        return false;
      }

      _isLoading = true;
      notifyListeners();

      // للاختبار: إذا كان verification ID للاختبار، استخدم رمز ثابت
      if (_verificationId == 'test_verification_id') {
        debugPrint('🧪 Using test verification code');
        if (code == '123456') {
          // محاكاة تسجيل دخول ناجح للاختبار
          await _handleTestSignIn();
          return true;
        } else {
          debugPrint('❌ Invalid test code. Use 123456');
          _isLoading = false;
          notifyListeners();
          return false;
        }
      }

      final credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: code,
      );

      await _signInWithCredential(credential);
      return true;
    } catch (e) {
      debugPrint('❌ Error verifying code: $e');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// التعامل مع تسجيل الدخول للاختبار
  Future<void> _handleTestSignIn() async {
    try {
      // إنشاء مستخدم اختبار
      _currentUser = UserModel(
        phoneNumber: '+212638813823',
        name: 'مستخدم تجريبي',
        gender: Gender.male,
        age: 25,
        country: 'المغرب',
        city: 'زاوية البئر',
        lastSeen: DateTime.now(),
        registrationDate: DateTime.now(),
      );

      // حفظ معلومات المستخدم محلياً
      await _saveUserToPrefs(_currentUser!);

      _isLoading = false;
      notifyListeners();

      debugPrint('✅ Test user signed in successfully');
    } catch (e) {
      debugPrint('❌ Error in test sign in: $e');
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  /// تسجيل الدخول باستخدام Credential
  Future<void> _signInWithCredential(PhoneAuthCredential credential) async {
    try {
      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user;

      if (user != null) {
        debugPrint('✅ User signed in: ${user.phoneNumber}');

        // البحث عن المستخدم في Firestore أو إنشاء جديد
        await _handleUserSignIn(user.phoneNumber!);
      }
    } catch (e) {
      debugPrint('❌ Error signing in: $e');
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  /// التعامل مع تسجيل الدخول
  Future<void> _handleUserSignIn(String phoneNumber) async {
    try {
      // البحث عن المستخدم في Firestore
      final userDoc = await _firestore.collection('users').doc(phoneNumber).get();

      if (userDoc.exists) {
        // المستخدم موجود، تحميل بياناته
        final userData = userDoc.data()!;
        _currentUser = UserModel.fromMap(userData);
        debugPrint('✅ User loaded from Firestore: ${_currentUser!.name}');
      } else {
        // مستخدم جديد، إنشاء ملف شخصي أساسي
        _currentUser = UserModel(
          phoneNumber: phoneNumber,
          name: 'مستخدم جديد',
          gender: Gender.male,
          age: 25,
          country: 'المغرب',
          city: 'زاوية البئر',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        );

        // حفظ في Firestore
        await _firestore.collection('users').doc(phoneNumber).set(_currentUser!.toMap());
        debugPrint('✅ New user created in Firestore');
      }

      // تحديث حالة المستخدم
      await updateUserOnlineStatus(true);
      await _saveUserToPrefs(_currentUser!);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error handling user sign in: $e');
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  // تسجيل مستخدم جديد
  Future<bool> registerUser({
    required String phoneNumber,
    required String name,
    required Gender gender,
    required int age,
    required String country,
    required String city,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      String formattedPhone = _formatPhoneNumber(phoneNumber);

      final user = UserModel(
        phoneNumber: formattedPhone,
        name: name,
        lastSeen: DateTime.now(),
        isOnline: true,
        gender: gender,
        age: age,
        country: country,
        city: city,
        registrationDate: DateTime.now(),
      );

      // حفظ المستخدم في Firestore
      await _firestore.collection('users').doc(formattedPhone).set(user.toMap());
      debugPrint('✅ User saved to Firestore: $name');

      final existingUserIndex = _localUsers.indexWhere(
        (u) => u.phoneNumber == formattedPhone,
      );

      if (existingUserIndex != -1) {
        _localUsers[existingUserIndex] = user;
      } else {
        _localUsers.add(user);
      }

      await _saveUserToPrefs(user);
      await _saveToRegisteredUsers(user);

      _currentUser = user;
      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('خطأ في التسجيل: $e');
      return false;
    }
  }

  // تسجيل الدخول بالهاتف (للمستخدمين المسجلين مسبقاً)
  Future<bool> loginWithPhone(String phoneNumber) async {
    try {
      _isLoading = true;
      notifyListeners();

      String formattedPhone = _formatPhoneNumber(phoneNumber);

      // البحث عن المستخدم في القائمة المحلية
      UserModel? existingUser;
      try {
        existingUser = _localUsers.firstWhere(
          (u) => u.phoneNumber == formattedPhone,
        );
      } catch (e) {
        // المستخدم غير موجود
        _isLoading = false;
        notifyListeners();
        return false;
      }

      // تحديث حالة المستخدم
      final updatedUser = existingUser.copyWith(
        lastSeen: DateTime.now(),
        isOnline: true,
      );

      // حفظ المستخدم في القائمة المحلية
      final existingUserIndex = _localUsers.indexWhere(
        (u) => u.phoneNumber == formattedPhone,
      );

      if (existingUserIndex != -1) {
        _localUsers[existingUserIndex] = updatedUser;
      }

      // حفظ معلومات المستخدم محلياً
      await _saveUserToPrefs(updatedUser);

      _currentUser = updatedUser;
      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('خطأ في تسجيل الدخول: $e');
      return false;
    }
  }

  // تحميل معلومات المستخدم من SharedPreferences
  Future<void> loadUserFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final phoneNumber = prefs.getString('user_phone');
      final name = prefs.getString('user_name');
      final profileImageUrl = prefs.getString('user_profile_image');
      final genderStr = prefs.getString('user_gender');
      final age = prefs.getInt('user_age');
      final country = prefs.getString('user_country');
      final city = prefs.getString('user_city');
      final registrationDateStr = prefs.getString('user_registration_date');
      final status = prefs.getString('user_status');
      final privacySettingsStr = prefs.getString('user_privacy_settings');

      if (phoneNumber != null && name != null && genderStr != null &&
          age != null && country != null && city != null && registrationDateStr != null) {

        final gender = Gender.values.firstWhere(
          (e) => e.toString().split('.').last == genderStr,
          orElse: () => Gender.male,
        );

        // تحميل إعدادات الخصوصية
        PrivacySettings privacySettings = const PrivacySettings();
        if (privacySettingsStr != null) {
          final parts = privacySettingsStr.split(',');
          if (parts.length == 7) {
            privacySettings = PrivacySettings(
              showPhoneNumber: parts[0] == 'true',
              showAge: parts[1] == 'true',
              showGender: parts[2] == 'true',
              showLocation: parts[3] == 'true',
              showRegistrationDate: parts[4] == 'true',
              showLastSeen: parts[5] == 'true',
              showOnlineStatus: parts[6] == 'true',
            );
          }
        }

        _currentUser = UserModel(
          phoneNumber: phoneNumber,
          name: name,
          profileImageUrl: profileImageUrl,
          status: status,
          lastSeen: DateTime.now(),
          isOnline: true,
          gender: gender,
          age: age,
          country: country,
          city: city,
          registrationDate: DateTime.parse(registrationDateStr),
          privacySettings: privacySettings,
        );

        // إضافة المستخدم إلى القائمة المحلية إذا لم يكن موجوداً
        final existingIndex = _localUsers.indexWhere(
          (u) => u.phoneNumber == phoneNumber,
        );
        if (existingIndex == -1) {
          _localUsers.add(_currentUser!);
        } else {
          _localUsers[existingIndex] = _currentUser!;
        }

        // تحديث حالة المستخدم
        await updateUserOnlineStatus(true);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل معلومات المستخدم: $e');
    }
  }

  // حفظ معلومات المستخدم محلياً
  Future<void> _saveUserToPrefs(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_phone', user.phoneNumber);
    await prefs.setString('user_name', user.name);
    await prefs.setString('user_gender', user.gender.toString().split('.').last);
    await prefs.setInt('user_age', user.age);
    await prefs.setString('user_country', user.country);
    await prefs.setString('user_city', user.city);
    await prefs.setString('user_registration_date', user.registrationDate.toIso8601String());

    // حفظ إعدادات الخصوصية
    await prefs.setString('user_privacy_settings',
        '${user.privacySettings.showPhoneNumber},${user.privacySettings.showAge},${user.privacySettings.showGender},${user.privacySettings.showLocation},${user.privacySettings.showRegistrationDate},${user.privacySettings.showLastSeen},${user.privacySettings.showOnlineStatus}');

    if (user.profileImageUrl != null) {
      await prefs.setString('user_profile_image', user.profileImageUrl!);
    }
    if (user.status != null) {
      await prefs.setString('user_status', user.status!);
    }
  }

  // حفظ المستخدم في قائمة المستخدمين المسجلين
  Future<void> _saveToRegisteredUsers(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل القائمة الحالية
      final usersJson = prefs.getString('registered_users');
      List<Map<String, dynamic>> usersList = [];

      if (usersJson != null) {
        final decoded = json.decode(usersJson) as List;
        usersList = decoded.cast<Map<String, dynamic>>();
      }

      // البحث عن المستخدم الحالي
      final existingIndex = usersList.indexWhere(
        (u) => u['phoneNumber'] == user.phoneNumber,
      );

      final userMap = user.toMap();

      if (existingIndex != -1) {
        // تحديث المستخدم الموجود
        usersList[existingIndex] = userMap;
      } else {
        // إضافة مستخدم جديد
        usersList.add(userMap);
      }

      // حفظ القائمة المحدثة
      final updatedJson = json.encode(usersList);
      await prefs.setString('registered_users', updatedJson);

      debugPrint('✅ User saved to registered users: ${user.name}');
    } catch (e) {
      debugPrint('❌ Error saving to registered users: $e');
    }
  }

  // تحديث حالة المستخدم (متصل/غير متصل)
  Future<void> updateUserOnlineStatus(bool isOnline) async {
    if (_currentUser == null) return;

    try {
      // تحديث في القائمة المحلية
      final userIndex = _localUsers.indexWhere(
        (u) => u.phoneNumber == _currentUser!.phoneNumber,
      );

      if (userIndex != -1) {
        _localUsers[userIndex] = _localUsers[userIndex].copyWith(
          isOnline: isOnline,
          lastSeen: DateTime.now(),
        );
      }

      _currentUser = _currentUser!.copyWith(
        isOnline: isOnline,
        lastSeen: DateTime.now(),
      );
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المستخدم: $e');
    }
  }

  // تحديث صورة الملف الشخصي
  Future<void> updateProfileImage(String imageUrl) async {
    if (_currentUser == null) return;

    try {
      // تحديث في القائمة المحلية
      final userIndex = _localUsers.indexWhere(
        (u) => u.phoneNumber == _currentUser!.phoneNumber,
      );

      if (userIndex != -1) {
        _localUsers[userIndex] = _localUsers[userIndex].copyWith(
          profileImageUrl: imageUrl,
        );
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_profile_image', imageUrl);

      _currentUser = _currentUser!.copyWith(profileImageUrl: imageUrl);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث صورة الملف الشخصي: $e');
    }
  }

  // الحصول على جميع المستخدمين
  List<UserModel> getAllUsers() {
    return _localUsers.where((user) =>
      user.phoneNumber != _currentUser?.phoneNumber
    ).toList();
  }

  // دوال ثابتة للوصول من خدمات أخرى (محدثة للبحث)
  static Future<List<UserModel>> getAllUsersStatic() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString('registered_users');

      if (usersJson != null) {
        final decoded = json.decode(usersJson) as List;
        final usersList = decoded.cast<Map<String, dynamic>>();

        return usersList.map((userMap) => UserModel.fromMap(userMap)).toList();
      }

      // إذا لم توجد مستخدمين محفوظين، إرجاع المستخدمين المحليين
      return List.from(_localUsers);
    } catch (e) {
      debugPrint('❌ Error loading registered users: $e');
      return List.from(_localUsers);
    }
  }

  static void addUserStatic(UserModel user) {
    final existingIndex = _localUsers.indexWhere(
      (u) => u.phoneNumber == user.phoneNumber,
    );

    if (existingIndex != -1) {
      _localUsers[existingIndex] = user;
    } else {
      _localUsers.add(user);
    }
  }

  // تحديث الملف الشخصي
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Update local user
      _currentUser = updatedUser;

      // Save to SharedPreferences
      await _saveUserToPrefs(updatedUser);

      // Update in static users list
      final index = _localUsers.indexWhere((user) => user.phoneNumber == updatedUser.phoneNumber);
      if (index != -1) {
        _localUsers[index] = updatedUser;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      throw Exception('Failed to update profile: $e');
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      // تحديث حالة المستخدم إلى غير متصل
      await updateUserOnlineStatus(false);

      // حذف معلومات المستخدم محلياً
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      _currentUser = null;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تسجيل الخروج: $e');
    }
  }

  // تنسيق رقم الهاتف
  String _formatPhoneNumber(String phoneNumber) {
    // إزالة المسافات والرموز الخاصة
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // إضافة رمز الدولة إذا لم يكن موجوداً
    if (!cleaned.startsWith('+')) {
      if (cleaned.startsWith('0')) {
        cleaned = '+966${cleaned.substring(1)}';
      } else {
        cleaned = '+966$cleaned';
      }
    }

    return cleaned;
  }



  // تحديث إعدادات الخصوصية
  Future<void> updatePrivacySettings(PrivacySettings privacySettings) async {
    try {
      if (_currentUser == null) return;

      _isLoading = true;
      notifyListeners();

      final updatedUser = _currentUser!.copyWith(privacySettings: privacySettings);
      await updateUserProfile(updatedUser);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      throw Exception('Failed to update privacy settings: $e');
    }
  }



  // البحث في المستخدمين بالاسم
  static Future<List<UserModel>> searchUsers(String query) async {
    try {
      final allUsers = await getAllUsersStatic();

      if (query.trim().isEmpty) {
        return allUsers;
      }

      final lowercaseQuery = query.toLowerCase();
      return allUsers.where((user) =>
        user.name.toLowerCase().contains(lowercaseQuery) ||
        user.city.toLowerCase().contains(lowercaseQuery) ||
        user.country.toLowerCase().contains(lowercaseQuery) ||
        user.phoneNumber.contains(query)
      ).toList();
    } catch (e) {
      debugPrint('❌ Error searching users: $e');
      return [];
    }
  }

  // Demo users disabled - using Firebase only

  @override
  void dispose() {
    // تحديث حالة المستخدم إلى غير متصل عند إغلاق التطبيق
    if (_currentUser != null) {
      updateUserOnlineStatus(false);
    }
    super.dispose();
  }
}
